/**
 * ==========================================================================
 * This file handles user profile functionality (Basic, Contact, Professional, Academic, Outcome)
 * ==========================================================================
 */

document.addEventListener("DOMContentLoaded", async function () {

    // ==========================================================================
    // INITIALIZATION & GLOBAL VARIABLES
    // ==========================================================================
    
    // Set initialization flag to prevent premature change detection
    window.isInitializing = true;
    window.isFormModified = false;
    
    const CountryCodeSelect = document.getElementById('country_code');
    const dropdownIcon = document.getElementById('dropdown-icon');
    const exportDropdownMenu = document.querySelector('.export_options .dropdown-menu');  // Changed selector to be more specific
    
    // Standardize variable names to match across files
    window.isFormModified = false; // Use isFormModified instead of isFormEdited
    window.isLeaving = false;
    window.switchAllowed = [];
    window.formModified = [];
    
    // Add initialization flag to prevent false positives during page load
    window.isInitializing = true;

    // Track modified state per tab to avoid false positives
    window.tabFormState = {
        'basic': false,
        'contact': false,
        'professional': false,
        'academics': false,
        'outcome': false
    };

    // ==========================================================================
    // FORM CHANGE DETECTION & STATE MANAGEMENT
    // ==========================================================================
    
    // Function to update form modified state and set flags
    function updateFormModifiedState(target) {
        // Don't track changes during initialization
        if (window.isInitializing) {
            return;
        }
        
        if (!target || !target.closest('form') || !target.closest('.profile-wraper')) {
            return;
        }
        
        // Find which tab this belongs to
        const tabSection = target.closest('.tab-section');
        if (!tabSection) return;
        
        const tabId = tabSection.id.replace('_tab', '');
        
        // Set both global and tab-specific flags
        window.isFormModified = true; // Changed from isFormEdited to isFormModified
        window.tabFormState[tabId] = true;
        
        if (!window.formModified.includes(tabId)) {
            window.formModified.push(tabId);
        }
    }

    // Global form change detection - capture any input on any form field
    document.addEventListener('input', function(event) {
        const target = event.target;
        // Check if the target is an input, select, or textarea inside a form AND inside profile-wraper
        if ((target.tagName === 'INPUT' || target.tagName === 'SELECT' || target.tagName === 'TEXTAREA') &&
            target.closest('form') && target.closest('.profile-wraper')) {
            updateFormModifiedState(target);
        }
    }, true); // Use capture phase to ensure we catch all events
    
    // Also capture change events for select elements (important for dropdowns)
    document.addEventListener('change', function(event) {
        const target = event.target;
        // Check specifically for select elements or elements with the 'choices' class inside profile-wraper
        if ((target.tagName === 'SELECT' || target.classList.contains('choices')) && 
            target.closest('form') && target.closest('.profile-wraper')) {
            updateFormModifiedState(target);
        }
    }, true); // Use capture phase to ensure we catch all events

    const months = [
        { value: "", text: "Month" },
        { value: "Jan", text: "January" },
        { value: "Feb", text: "February" },
        { value: "Mar", text: "March" },
        { value: "Apr", text: "April" },
        { value: "May", text: "May" },
        { value: "Jun", text: "June" },
        { value: "Jul", text: "July" },
        { value: "Aug", text: "August" },
        { value: "Sep", text: "September" },
        { value: "Oct", text: "October" },
        { value: "Nov", text: "November" },
        { value: "Dec", text: "December" }
    ];
    const years = [{ _id: "", name: "Year" }, ...Array.from({ length: 50 }, (_, i) => {
        const year = String(2025 - i);
        return { _id: year, name: year };
    })];

    // ==========================================================================
    // UTILITY FUNCTIONS
    // ==========================================================================
    
    // Utility function to ensure disabled inputs remain visible
    function ensureDisabledInputsVisible() {
        document.querySelectorAll('input[disabled], input[readonly]').forEach(input => {
            input.style.opacity = "1";
            input.style.visibility = "visible";
            input.style.display = "block";
        });
    }

    // Call this function on initial load
    ensureDisabledInputsVisible();

    if (profileUpdate==='true') {
        const notification = document.querySelector(".profile-updated");
        notification.classList.remove("hide");
        // Optionally hide after 5 seconds (matching your fadeOut animation)
        setTimeout(() => {
          notification.classList.add("hide");
        }, 5000);
      }

    // ==========================================================================
    // PAGE NAVIGATION & UNSAVED CHANGES HANDLING
    // ==========================================================================
    
    // Add beforeunload event handler for page navigation and tab closure
    window.addEventListener('beforeunload', function(event) {
        // Debug: log the form modified state (will only be visible in developer console)
        console.log('beforeunload triggered - Form modified:', window.isFormModified, 'Is leaving:', window.isLeaving);
        
        if (window.isFormModified && !window.isLeaving) {
            // For tab/window closure, we need to set returnValue
            const confirmationMessage = "You have unsaved changes. Are you sure you want to leave this page?";
            event.preventDefault(); // Required for some browsers
            event.returnValue = confirmationMessage; // For older browsers
            return confirmationMessage; // For modern browsers
        }
    });
    
    // Additional unload handler to ensure tab closing is captured
    window.addEventListener('unload', function(event) {
        // This is just a backup to ensure the beforeunload handler is triggered
        // No code needed here as the beforeunload does the actual work
    });

    // ==========================================================================
    // MODAL FUNCTIONALITY (Export & Delete Account)
    // ==========================================================================
    
    const downloadDataLink = document.querySelector(".download-data");
    const deleteAccountLink = document.querySelector(".delete-data");
    const exportModal = document.getElementById("exportModal");
    const deleteModal = document.getElementById("deleteUserProfileModal");
    const modalDialog = exportModal?.querySelector(".modal-dialog");
    const deleteModalDialog = deleteModal?.querySelector(".modal-dialog");
    const closeModal = exportModal?.querySelector(".admin-modal-close");
    const closeDeleteModal = deleteModal?.querySelector(".admin-modal-close");
    const formWrapper = document.querySelector(".form_main_wrapper");
    // Work Experience Section - Adding & Removing Entries
    const addWorkExpBtn = document.getElementById("add_work_exp");
    const workExpContainer = document.getElementById("js_work_exp");
    // Academic Details Section - Adding & Removing Entries 
    const addAcademicExpBtn = document.getElementById("add_academics_exp");
    const academicExpContainer = document.getElementById("js_div_qualification");

    // ==========================================================================
    // DATE PICKER INITIALIZATION & AGE VALIDATION
    // ==========================================================================
    
    // Get date 18 years ago for date picker max date
    const today = new Date();
    const minAge = 18;
    const maxDate = new Date(today);
    const eighteenYearsAgo = new Date(today.getFullYear() - minAge, today.getMonth(), today.getDate());

    // Initialize date picker with proper validation
    const datePicker = flatpickr("#datepicker", {
        enableTime: false,
        dateFormat: "d/m/Y",
        maxDate: "today", // Prevent future date selection
        onOpen: function() {
            document.querySelector(".dob_icon").style.backgroundPosition = "-35px -386px";
        },
        onClose: function() {
            document.querySelector(".dob_icon").style.backgroundPosition = "-5px -386px";
            
            // Validate age when date is selected
            const selectedDate = this.selectedDates[0];
            if (selectedDate) {
                const birthDate = new Date(selectedDate);
                const age = calculateAge(birthDate);
                
                const dobInput = document.getElementById("datepicker");
                const errorElement = dobInput.closest('.right-col').querySelector('.p_error');
                
                if (age < minAge) {
                    errorElement.textContent = "You must be at least 18 years old.";
                    errorElement.classList.remove("hide");
                } else {
                    errorElement.classList.add("hide");
                }
            }
        }
    });

    // Calculate age from birthdate
    function calculateAge(birthDate) {
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDifference = today.getMonth() - birthDate.getMonth();
        
        if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        return age;
    }

    // Handle calendar icon click to open date picker
    const dobIcon = document.querySelector(".dob_icon");
    if (dobIcon) {
        dobIcon.addEventListener("click", function() {
            datePicker.open();
        });
    }

    // Export options dropdown functionality
    if (dropdownIcon && exportDropdownMenu) {
        dropdownIcon.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            const isExpanded = dropdownIcon.getAttribute('aria-expanded') === 'true';
            dropdownIcon.setAttribute('aria-expanded', !isExpanded);
            exportDropdownMenu.classList.toggle('active-ul');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!dropdownIcon.contains(event.target) && !exportDropdownMenu.contains(event.target)) {
                exportDropdownMenu.classList.remove('active-ul');
                dropdownIcon.setAttribute('aria-expanded', 'false');
            }
        });

        // Keyboard accessibility
        dropdownIcon.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                const isExpanded = dropdownIcon.getAttribute('aria-expanded') === 'true';
                dropdownIcon.setAttribute('aria-expanded', !isExpanded);
                exportDropdownMenu.classList.toggle('active-ul');
            }
        });
    }

    // Support both the custom modal implementation and the data-toggle/data-target approach
    // Add event listeners to any elements with data-toggle="modal"
    document.querySelectorAll('[data-toggle="modal"]').forEach(function(element) {
        element.addEventListener('click', function(event) {
            event.preventDefault();
            const targetModal = this.getAttribute('data-target');
            if (targetModal === '#exportModal') {
                openModal();
            } else if (targetModal === '#deleteUserProfileModal') {
                openDeleteModal();
            }
        });
    });

    // Also support cloud6 style dropdown menu in heading
    const dropdownToggleIcon = document.querySelector('.export_options i[data-toggle="dropdown"]');
    const dropdownMenu = document.querySelector('.export_options .dropdown-menu');
    
    if (dropdownToggleIcon && dropdownMenu) {
        dropdownToggleIcon.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            
            const isExpanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !isExpanded);
            dropdownMenu.classList.toggle('active-ul');
            
            // Toggle visibility of dropdown
            const parentDropdown = this.closest('.dropdown');
            if (parentDropdown) {
                parentDropdown.classList.toggle('hide');
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!dropdownToggleIcon.contains(event.target) && !dropdownMenu.contains(event.target)) {
                dropdownMenu.classList.remove('active-ul');
                dropdownToggleIcon.setAttribute('aria-expanded', 'false');
                
                // Hide dropdown
                const parentDropdown = dropdownToggleIcon.closest('.dropdown');
                if (parentDropdown && !parentDropdown.classList.contains('hide')) {
                    parentDropdown.classList.add('hide');
                }
            }
        });
    }

    if (formWrapper) {
        formWrapper.classList.add("bounceInLeft"); // Trigger animation
        console.log("Added bounceInLeft animation.");
    } else {
        console.log("form_main_wrapper not found!");
    }

    function openModal() {
        if (exportModal) {
            exportModal.classList.add("show");
            exportModal.style.display = "flex";

            // Delay animation to prevent jump effect
            setTimeout(() => {
                modalDialog.style.transform = "translateY(0)";
                exportModal.style.opacity = "1";
            }, 10);

            document.body.classList.add("modal-open"); // Prevent background scrolling
        }
    }

    function openDeleteModal() {
        if (deleteModal) {
            deleteModal.classList.add("show");
            deleteModal.style.display = "flex";

            // Delay animation to prevent jump effect
            setTimeout(() => {
                if (deleteModalDialog) {
                    deleteModalDialog.style.transform = "translateY(0)";
                }
                deleteModal.style.opacity = "1";
            }, 10);

            document.body.classList.add("modal-open"); // Prevent background scrolling
        }
    }

    function closeModalFunc() {
        if (exportModal) {
            modalDialog.style.transform = "translateY(-50px)"; // Move modal back up
            exportModal.style.opacity = "0";

            setTimeout(() => {
                exportModal.style.display = "none";
                exportModal.classList.remove("show");
                document.body.classList.remove("modal-open");
            }, 300); // Matches fade-out timing
        }
    }

    function closeDeleteModalFunc() {
        if (deleteModal) {
            if (deleteModalDialog) {
                deleteModalDialog.style.transform = "translateY(-50px)"; // Move modal back up
            }
            deleteModal.style.opacity = "0";

            setTimeout(() => {
                deleteModal.style.display = "none";
                deleteModal.classList.remove("show");
                document.body.classList.remove("modal-open");
            }, 300); // Matches fade-out timing
        }
    }

    if (downloadDataLink) {
        downloadDataLink.addEventListener("click", function (event) {
            event.preventDefault();
            openModal();
        });
    }

    if (deleteAccountLink) {
        deleteAccountLink.addEventListener("click", function (event) {
            event.preventDefault();
            openDeleteModal();
        });
    }

    if (closeModal) {
        closeModal.addEventListener("click", closeModalFunc);
    }

    if (closeDeleteModal) {
        closeDeleteModal.addEventListener("click", closeDeleteModalFunc);
    }

    // Close export modal when clicking outside
    window.addEventListener("click", function (event) {
        if (event.target === exportModal) {
            closeModalFunc();
        }
        if (event.target === deleteModal) {
            closeDeleteModalFunc();
        }
    });

    // Close modals with Escape key
    document.addEventListener("keydown", function (event) {
        if (event.key === "Escape") {
            closeModalFunc();
            closeDeleteModalFunc();
        }
    });

    // Handle "I want to stay" button in delete modal
    const stayButton = deleteModal?.querySelector(".blue-bg-btn");
    if (stayButton) {
        stayButton.addEventListener("click", function() {
            closeDeleteModalFunc();
        });
    }

    // Handle delete account button functionality
    const deleteAccountButton = document.getElementById("account-delete");
    if (deleteAccountButton) {
        deleteAccountButton.addEventListener("click", function(event) {
            event.preventDefault(); // Prevent immediate navigation
            
            const deleteUrl = this.getAttribute("data-src");
            if (deleteUrl) {
                // Show browser confirmation dialog
                const confirmDelete = confirm("Are you sure you want to delete your account?");
                
                if (confirmDelete) {
                    // User confirmed, proceed with deletion
                    window.isLeaving = true; // Prevent form change warnings
                    window.location.href = deleteUrl;
                } else {
                    // User cancelled, do nothing
                    console.log("Account deletion cancelled by user");
                }
            } else {
                console.error("Delete URL not found");
                const errorElement = document.getElementById("delete_err_res");
                if (errorElement) {
                    errorElement.textContent = "An error occurred. Please try again later.";
                }
            }
        });
    }

    // ==========================================================================
    // DROPDOWN INITIALIZATION & CHOICES.JS FUNCTIONS
    // ==========================================================================
    
    function addSelectOption(select, item = undefined, selectedId = null, placeholderValue) {
        const newOption = document.createElement('option');
        if (item && item._id) {
            newOption.text = item.name;
            newOption.value = item._id;
        // Set option as selected if it matches selectedId
            if (item._id === selectedId) {
            newOption.selected = true;
            }
        } else {
            // This is a placeholder option
            newOption.text = placeholderValue || "--Select--";
            newOption.value = "";
            newOption.disabled = true;
            newOption.hidden = true;
            newOption.selected = true; // Placeholder should be selected by default
        }
        // Append options properly
        select.add(newOption);
    }
    
    function initializeChoicesDropdown(select, options = [], selectedId = null, isSearch = true, isSort = true, placeholderValue = "--Select--", disabled = false) {
        if (!select) {
            console.error("Dropdown element not found!");
            return;
        }

        // Check if Choices.js is already initialized and destroy it
        if (select.classList.contains("choices__input")) {
            console.log("Destroying existing Choices instance for", select);
            let existingChoices = select.choicesInstance || select.choices;
            if (existingChoices && typeof existingChoices.destroy === "function") {
                existingChoices.destroy();
            }
        }
        
        // Clear existing options before adding new ones
        select.textContent = "";

        // Only add placeholder option if no selectedId is provided
        if (!selectedId || selectedId === null || selectedId === "") {
            const placeholderOption = document.createElement('option');
            placeholderOption.value = "";
            placeholderOption.text = placeholderValue;
            placeholderOption.disabled = true;
            placeholderOption.hidden = true; // Hide from dropdown list
            placeholderOption.selected = true;
            select.appendChild(placeholderOption);
        }

        // Add provided options
        options.forEach(item => {
            if (item && (item._id || item.id || item.value)) {
                addSelectOption(select, item, selectedId, placeholderValue);
            }
        });
        
        // Initialize Choices.js
        const choice = new Choices(select, {
            searchEnabled: isSearch,
            placeholder: true,
            placeholderValue: placeholderValue,
            removeItemButton: false,
            position: 'bottom',
            shouldSort: isSort,
            itemSelectText: '',
            allowHTML: false
        });
        
        if(disabled){
            select.disabled = true;
            choice.disable();
        } else { 
            select.disabled = false;
            choice.enable();         // Explicitly enable Choices.js instance and remove 'is-disabled' class
        }

        // Only set a specific value if selectedId is provided and valid (not null or empty)
        if (selectedId && selectedId !== null && selectedId !== "" && options.some(item => 
            (item._id && item._id === selectedId) || 
            (item.id && item.id === selectedId) || 
            (item.value && item.value === selectedId)
        )) {
            choice.setChoiceByValue(selectedId);
        } else {
            // Ensure no option is selected when selectedId is null/empty
            choice.setChoiceByValue("");
        }
        
        // store the choices instance
        select.choicesInstance = choice;        
    }

    function populateMonthYearDropdown(select, data, selectedValue , placeholderValue='' , disabled = false) {
        if (!select) {
            console.error("Dropdown element not found!");
            return;
        }
        // Adding options
        let options = data.map(({ _id, name }) => ({ _id, name }));
        initializeChoicesDropdown(select, options, selectedValue, false, false, placeholderValue || "--Select--" , disabled);
    }

    function updateInputValues(className, inputPrefix, valueCallback) {
        document.querySelectorAll(`.${className}`).forEach((element, index) => {
            const targetInput = document.getElementById(`${inputPrefix}_${index}`);
            if (targetInput) {
                const value = valueCallback(element);
                if (value !== null) targetInput.value = value;
            }
        });
    }

    function toggleSaveButtonState(enable, ...buttons) {
        buttons.forEach(button => {
            if (enable) {
                button.removeAttribute("disabled");
                button.removeAttribute("aria-disabled");
            } else {
                button.setAttribute("disabled", "true");
                button.setAttribute("aria-disabled", "true");
            }
        });
    }

    function clearFields(fields) {
        fields.forEach(field => {
            if (field.type === "checkbox") {
                field.checked = false;
            } else {
                field.value = "";
            }
        });
    }

    // ==========================================================================
    // TAXONOMY DATA FETCHING & INITIALIZATION
    // ==========================================================================
    
    console.log("Checking Add Work Experience button...");
    async function fetchTaxonomyData() {
        const categories = [
            "where_are_you_in_your_career",
            "job_function",
            "industry",
            "qualification",
            "highest_level_of_education",
            "objective_of_taking_course"
        ].join(",");
        const res = await fetch(`/user-api/v1/get-taxonomy?category=${categories}`);
        return await res.json();
    }

    const taxonomyResponse = await fetchTaxonomyData();
    const TaxonomyData = taxonomyResponse?.data || [];

    // Total years of experience dropdown options
    if (TaxonomyData && TaxonomyData?.where_are_you_in_your_career?.length) {
        const select = document.getElementById('where_are_you_in_career');
        const selectedId = selectedExperienceId || null;
        if (!select) {
            console.error("Select where_are_you_in_your_career element not found!");
            return;
        }
        // Initialize with Choices.js
        initializeChoicesDropdown(select, TaxonomyData.where_are_you_in_your_career, selectedId, false, false);
    }
    // Highest level of educations dropdown options
    if (TaxonomyData && TaxonomyData.highest_level_of_education?.length) {
        const select = document.getElementById('highest_level_of_education');
        const selectedId = selectedEducationId || null;
        if (!select) {
            console.error("Select highest_level_of_education element not found!");
            return;
        }
        // Initialize with Choices.js
        initializeChoicesDropdown(select, TaxonomyData.highest_level_of_education, selectedId, false, false);
        
        // Add direct change listener to enable buttons when highest level of education changes
        select.addEventListener('change', function() {
            enableButtons();
        });
    }
    // Professtional prepopulate data (workExperiance is declared in hbs file)
    if (workExperience.length) {
        workExperience.forEach((exp, i) => {
            const industrySelect = document.getElementById(`industry_${i}`);
            const departmentSelect = document.getElementById(`department_${i}`);
            const startMonthSelect = document.getElementById(`from_month_${i}`);
            const startYearSelect = document.getElementById(`from_year_${i}`);
            const toMonthSelect = document.getElementById(`to_month_${i}`);
            const toYearSelect = document.getElementById(`to_year_${i}`);

            if (!industrySelect || !departmentSelect) {
                console.error(`Select industry_${i} element not found!`);
                return;
            }

            // Industry data
            if (TaxonomyData?.industry?.length) {
                // Populate Choices.js dropdowns 
                initializeChoicesDropdown(industrySelect, TaxonomyData?.industry, exp.industry, false);
            }

            // Department data
            if (TaxonomyData?.job_function?.length) {
                // Populate Choices.js dropdowns with search
                initializeChoicesDropdown(departmentSelect, TaxonomyData.job_function, exp.job_function);
            }

            // Start date population
            if (exp?.exp_from_month && exp?.exp_from_year) {
                populateMonthYearDropdown(startMonthSelect, months.map(({ value, text }) => ({ _id: value, name: text })), exp.exp_from_month , 'Month'); 
                populateMonthYearDropdown(startYearSelect, years, exp.exp_from_year , 'Year');
            }
            
            // End date population - if current role, show placeholders; otherwise show saved values
            if (exp.current_role == "1") {
                // Current role checked - show disabled placeholders
                populateMonthYearDropdown(toMonthSelect, months.map(({ value, text }) => ({ _id: value, name: text })), "", 'Month', true); 
                populateMonthYearDropdown(toYearSelect, years, "", 'Year', true);
            } else {
                // Not current role - show saved values if they exist
                populateMonthYearDropdown(toMonthSelect, months.map(({ value, text }) => ({ _id: value, name: text })), exp.exp_to_month , 'Month', false); 
                populateMonthYearDropdown(toYearSelect, years, exp.exp_to_year , 'Year', false);
            }
        });
    }
    // Academics prepopulate data (academicExperience is declared in hbs file)
    if (academicExperience.length) {
        academicExperience.forEach((exp, i) => {
            const qualificationSelect = document.getElementById(`field_qualification_${i}`);
            const startMonthSelect = document.getElementById(`a_from_month_${i}`);
            const startYearSelect = document.getElementById(`a_from_year_${i}`);
            const toMonthSelect = document.getElementById(`a_to_month_${i}`);
            const toYearSelect = document.getElementById(`a_to_year_${i}`);

            // qualification data
            if (TaxonomyData?.qualification?.length) {
                // Populate Choices.js dropdowns 
                initializeChoicesDropdown(qualificationSelect, TaxonomyData?.qualification, exp.qualification, false);
            }

            // start date and end date data
            if (exp?.course_from_month && exp?.course_from_year) {
                populateMonthYearDropdown(startMonthSelect, months.map(({ value, text }) => ({ _id: value, name: text })), exp?.course_from_month);
                populateMonthYearDropdown(toMonthSelect, months.map(({ value, text }) => ({ _id: value, name: text })), exp?.course_to_month);

                populateMonthYearDropdown(startYearSelect, years, exp?.course_from_year);
                populateMonthYearDropdown(toYearSelect, years, exp?.course_to_year);
            }
            
            // Add direct change listeners to enable buttons
            [qualificationSelect, startMonthSelect, startYearSelect, toMonthSelect, toYearSelect].forEach(select => {
                select.addEventListener('change', () => {
                    enableButtons();
                });
        });
        });
    }
    // ==========================================================================
    // PROFESSIONAL EXPERIENCE SECTION - START
    // ==========================================================================
    
    if (addWorkExpBtn && workExpContainer) {
        console.log("Add Work Experience button found.");

        addWorkExpBtn.addEventListener("click", function () {
            console.log("Add Work Experience Clicked!");

            // Count the number of work experience sections
            const expCount = document.querySelectorAll(".exp_main_wrap.professional").length;
            const newIndex = expCount;

            console.log(`Current Work Experience Count: ${newIndex + 1}`);
            // Generate industry dropdown options dynamically
            let industryOptions = `<option value="" disabled hidden selected>--Select--</option>`;
            if (TaxonomyData && TaxonomyData.industry?.length) {
                industryOptions += TaxonomyData.industry
                    .map(item => `<option value="${item._id}">${item.name}</option>`)
                    .join("");
            }

            // Generate Department dropdown options dynamically
            let DepartmentOptions = `<option value="" disabled selected>--Select--</option>`;
            if (TaxonomyData?.job_function?.length) {
                DepartmentOptions += TaxonomyData?.job_function
                    .map(item => `<option value="${item._id}">${item.name}</option>`)
                    .join("");
            }

            // Create new work experience block
            const newExpBlock = document.createElement("div");
            newExpBlock.classList.add("form-wrap", "exp_main_wrap","professional");
            newExpBlock.id = `exp_main_wrap_w_${newIndex}`;

            newExpBlock.innerHTML = `
                <div class="exp-inner-wrap">
                    <div class="frm_abel exp_label"><b>Work Experience <font class="work_exp_box_counter">${newIndex + 1}</font></b></div>
                    <div class="right-col"><a href="javascript:void(0)" class="delete">Delete</a></div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">Designation<i>*</i></div>
                    <div class="right-col">
                        <input type="text" name="company_designation[]" class="company_designation work_details input_field_company_designation" required />
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">Company<i>*</i></div>
                    <div class="right-col">
                        <input type="text" name="company_name[]" class="company_name work_details input_field_company_name" required />
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">Department<i>*</i></div>
                    <div class="right-col">
                        <select id="job_function" name="job_function[]" class="job_function custom_select1 work_details input_field_job_function choices" required>
                            ${DepartmentOptions}
                        </select>
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">Industry<i>*</i></div>
                    <div class="right-col">
                        <select id="industry" name="industry[]" class="select_industry industry work_details input_field_industry choices" required>
                            ${industryOptions}
                        </select>
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap select_dates">
                    <div class="frm_abel">Start Date<i>*</i></div>
                    <div class="right-col">
                        <div class="flex-date">
                            <div class="select_wraper for_cun month">
                                <select name="exp_from_month[]" class="exp_from_month work_details input_field_exp_from_month choices" required>
                                    ${months.map(({ value, text }) => `<option value="${value}">${text}</option>`).join("")}
                                </select>
                            </div>
                            <div class="select_wraper for_cun year">
                                <select name="exp_from_year[]" class="exp_from_year work_details input_field_exp_from_year choices" required>
                                    <option value="">Year</option>
                                    ${Array.from({ length: 50 }, (_, i) => `<option value="${2025 - i}">${2025 - i}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <p class="p_error error hide">Required</p>
                    </div>
                </div>
                <div class="exp-inner-wrap end_date select_dates">
                <div class="frm_abel">
                    End Date<i>*</i>
                    <span>
                        <input type="hidden" id="current_role_${newIndex}" name="current_role[]" value="0">    
                        <input type="checkbox" id="current_company_${newIndex}" class="current_company work_details input_field_current_company" >
                        <label for="current_company_${newIndex}" class="current_company_label">Current role</label>
                    </span>
                </div>
                <div class="right-col">
                    <div class="flex-date">
                        <div class="select_wraper for_cun month">
                            <input type="hidden" id="exp_to_month_${newIndex}" name="exp_to_month[]">
                            <select class="exp_to_month work_details input_field_exp_to_month choices" required>
                                ${months.map(({ value, text }) => `<option value="${value}">${text}</option>`).join("")}
                            </select>
                        </div>
                        <div class="select_wraper for_cun year">
                            <input type="hidden" id="exp_to_year_${newIndex}" name="exp_to_year[]">    
                            <select class="exp_to_year work_details input_field_exp_to_year choices" required>
                                <option value="">Year</option>
                                ${Array.from({ length: 50 }, (_, i) => `<option value="${2025 - i}">${2025 - i}</option>`).join('')}
                            </select>
                        </div>
                    </div>
                    <p class="p_error error hide">Required</p>
                    <p class="work_exp_err error hide"></p>
                </div>
            </div>
            
        `;

            workExpContainer.appendChild(newExpBlock);

            initializeProfessionalDropdowns(newIndex);
            console.log(`Work Experience ${newIndex + 1} added successfully.`);
        });

    } else {
        console.log("Add Work Experience button or Work Experience container not found!");
    }
    
    // ==========================================================================
    // PROFESSIONAL EXPERIENCE SECTION - END
    // ==========================================================================

    // ==========================================================================
    // ACADEMIC EXPERIENCE SECTION - START
    // ==========================================================================
    
    if (addAcademicExpBtn && academicExpContainer) {
        addAcademicExpBtn.addEventListener("click", function () {
            console.log("Add Academic Experience Clicked!");

            const expCount = document.querySelectorAll(".exp_main_wrap.academic").length;
            const newIndex = expCount;

            const newAcademicBlock = document.createElement("div");
            newAcademicBlock.classList.add("form-wrap", "exp_main_wrap", "academic");
            newAcademicBlock.id = `exp_main_wrap_a_${newIndex}`;

            // Generate Qualifications dropdown options dynamically
            let QualificationOptions = `<option value="" disabled selected>--Select--</option>`;
            if (TaxonomyData?.qualification?.length) {
                QualificationOptions += TaxonomyData?.qualification
                    .map(item => `<option value="${item._id}">${item.name}</option>`)
                    .join("");
            }

            newAcademicBlock.innerHTML = `
                <div class="exp-inner-wrap">
                    <div class="frm_abel exp_label"><b>Academic Experience <font class="academics_exp_box_counter">${newIndex + 1}</font></b></div>
                    <div class="right-col"><a href="javascript:void(0)" class="delete">Delete</a></div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">Qualification<i>*</i></div>
                    <div class="right-col">
                        <select name="field_qualification[]" class="field_qualification custom_select1 qualification_details input_field_field_qualification" required>
                            ${QualificationOptions}
                        </select>
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">School/College<i>*</i></div>
                    <div class="right-col">
                        <input type="text" name="institute_name[]" class="institute_name input_field_institute_name qualification_details" required />
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap">
                    <div class="frm_abel">Specialization<i>*</i></div>
                    <div class="right-col">
                        <input type="text" name="field_specialization[]" class="field_specialization input_field_field_specialization qualification_details" required />
                        <p class="p_error error hide"></p>
                    </div>
                </div>
                <div class="exp-inner-wrap select_dates">
                    <div class="frm_abel">Start Date<i>*</i></div>
                    <div class="right-col">
                        <div class="flex-date">
                            <div class="select_wraper for_cun month">
                                <select name="course_from_month[]" class="course_from_month qualification_details input_field_course_from_month" required>
                                    ${months.map(({ value, text }) => `<option value="${value}">${text}</option>`).join("")}
                                </select>
                            </div>
                            <div class="select_wraper for_cun year">
                                <select name="course_from_year[]" class="course_from_year qualification_details input_field_course_from_year" required>
                                    <option value="">Year</option>
                                    ${Array.from({ length: 50 }, (_, i) => `<option value="${2025 - i}">${2025 - i}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <p class="p_error error hide">Required</p>
                    </div>
                </div>
                <div class="exp-inner-wrap select_dates">
                    <div class="frm_abel">End Date<i>*</i>
                        <span>If you are still studying, please enter your expected graduation date</span>
                    </div>
                    <div class="right-col">
                        <div class="flex-date">
                            <div class="select_wraper for_cun month">
                                <select name="course_to_month[]" class="course_to_month qualification_details input_field_course_to_month" required>
                                    ${months.map(({ value, text }) => `<option value="${value}">${text}</option>`).join("")}
                                </select>
                            </div>
                            <div class="select_wraper for_cun year">
                                <select name="course_to_year[]" class="course_to_year qualification_details input_field_course_to_year" required>
                                    <option value="">Year</option>
                                    ${Array.from({ length: 50 }, (_, i) => `<option value="${2025 - i}">${2025 - i}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <p class="p_error error hide">Required</p>
                        <p class="academics_exp_err error hide"></p>
                    </div>
                </div>
               
            `;

            newAcademicBlock.appendChild(document.createTextNode("")); // Fix for IE
            academicExpContainer.appendChild(newAcademicBlock);
            initializeAcademicDropdowns(newIndex);
            
            // Add change listeners to the newly added fields
            const newSelects = newAcademicBlock.querySelectorAll('select');
            const newInputs = newAcademicBlock.querySelectorAll('input');
            
            [...newSelects, ...newInputs].forEach(field => {
                field.addEventListener('change', function() {
                    enableButtons();
                });
                field.addEventListener('input', function() {
                    enableButtons();
                });
            });
            
            console.log(`Academic Experience ${newIndex + 1} added successfully.`);
        });
    }
    
    // ==========================================================================
    // ACADEMIC EXPERIENCE SECTION - END
    // ==========================================================================

    // ==========================================================================
    // SHARED DELETE FUNCTIONALITY (Professional & Academic)
    // ==========================================================================
    
    // Handle Delete Button for both Work & Academic Experiences
    document.addEventListener("click", function (event) {
        if (event.target.classList.contains("delete")) {
            event.preventDefault();
            console.log("Delete button clicked.");

            const confirmDelete = confirm("Are you sure?");
            if (confirmDelete) {
                const expBlock = event.target.closest(".exp_main_wrap");
                if (expBlock) {
                    // Enable appropriate save button based on section
                    if (expBlock.classList.contains("professional")) {
                        enableSaveButton(); // For professional section
                    } else if (expBlock.classList.contains("academic")) {
                        enableButtons(); // For academic section
                    }
                    expBlock.remove();
                    console.log("Entry Removed.");
                }
            }
        }
    });

    // ==========================================================================
    // PROFESSIONAL EXPERIENCE - FORM HANDLING & VALIDATION
    // ==========================================================================
    
    const form = document.getElementById("form_professional_info");
    const professionalInfoSaveButton = form.querySelector(".btn.save");
    const professionalDiscardButton = form.querySelector(".btn.discard");
    const workExperienceFields = form.querySelectorAll("#js_work_exp input, #js_work_exp select, #js_total_exp select");

    function getWorkExperienceFields() {
        return document.querySelectorAll("#js_work_exp input, #js_work_exp select");
    }

    function enableSaveButton() {
        professionalInfoSaveButton.removeAttribute("disabled");
        professionalInfoSaveButton.removeAttribute("aria-disabled");

        professionalDiscardButton.removeAttribute("disabled");
        professionalDiscardButton.removeAttribute("aria-disabled");
    }
    function disableButtons() {
        professionalInfoSaveButton.setAttribute("disabled", "true");
        professionalInfoSaveButton.setAttribute("aria-disabled", "true");

        professionalDiscardButton.setAttribute("disabled", "true");
        professionalDiscardButton.setAttribute("aria-disabled", "true");
    }

    function checkboxActive() {
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        const activeCheckboxes = Array.from(checkboxes).filter(checkbox => checkbox.checked);

        checkboxes.forEach(checkbox => {
            checkbox.disabled = activeCheckboxes.length === 1 && !checkbox.checked;
        });
    }
    checkboxActive();

    workExperienceFields.forEach((field) => {
        field.addEventListener("input", enableSaveButton);
        field.addEventListener("change", enableSaveButton);
    });

    function clearWorkExperienceFields() {
        getWorkExperienceFields().forEach((field) => {
            if (field.type === "checkbox") {
                field.checked = false;
            } else {
                field.value = "";
            }
        });
    }

    // Add this new function to restore fields to their original values
    function restoreWorkExperienceFields() {
        if (workExperience && workExperience.length) {
            // Restore existing work experiences
            workExperience.forEach((exp, i) => {
                const designationInput = document.querySelector(`#exp_main_wrap_w_${i} .company_designation`);
                const companyInput = document.querySelector(`#exp_main_wrap_w_${i} .company_name`);
                const industrySelect = document.getElementById(`industry_${i}`);
                const departmentSelect = document.getElementById(`department_${i}`);
                const fromMonthSelect = document.getElementById(`from_month_${i}`);
                const fromYearSelect = document.getElementById(`from_year_${i}`);
                const toMonthSelect = document.getElementById(`to_month_${i}`);
                const toYearSelect = document.getElementById(`to_year_${i}`);
                const currentRoleCheckbox = document.querySelector(`#exp_main_wrap_w_${i} .current_company`);
                
                if (designationInput) designationInput.value = exp.company_designation || "";
                if (companyInput) companyInput.value = exp.company_name || "";
                
                // Restore dropdowns with Choices.js
                if (industrySelect && industrySelect.choicesInstance) {
                    industrySelect.choicesInstance.setChoiceByValue(exp.industry || "");
                }
                
                if (departmentSelect && departmentSelect.choicesInstance) {
                    departmentSelect.choicesInstance.setChoiceByValue(exp.job_function || "");
                }
                
                if (fromMonthSelect && fromMonthSelect.choicesInstance) {
                    fromMonthSelect.choicesInstance.setChoiceByValue(exp.exp_from_month || "");
                }
                
                if (fromYearSelect && fromYearSelect.choicesInstance) {
                    fromYearSelect.choicesInstance.setChoiceByValue(exp.exp_from_year || "");
                }
                
                // Handle current role checkbox
                if (currentRoleCheckbox) {
                    const isCurrentRole = exp.current_role == "1";
                    currentRoleCheckbox.checked = isCurrentRole;
                    
                    // Disable end date fields if current role
                    if (toMonthSelect && toMonthSelect.choicesInstance) {
                        if (isCurrentRole) {
                            toMonthSelect.choicesInstance.disable();
                        } else {
                            toMonthSelect.choicesInstance.enable();
                            toMonthSelect.choicesInstance.setChoiceByValue(exp.exp_to_month || "");
                        }
                    }
                    
                    if (toYearSelect && toYearSelect.choicesInstance) {
                        if (isCurrentRole) {
                            toYearSelect.choicesInstance.disable();
                        } else {
                            toYearSelect.choicesInstance.enable();
                            toYearSelect.choicesInstance.setChoiceByValue(exp.exp_to_year || "");
                        }
                    }
                }
            });
        }
        
        // Remove any dynamically added work experiences that weren't saved
        const expCount = document.querySelectorAll(".exp_main_wrap.professional").length;
        if (expCount > workExperience.length) {
            for (let i = workExperience.length; i < expCount; i++) {
                const expBlock = document.getElementById(`exp_main_wrap_w_${i}`);
                if (expBlock) expBlock.remove();
            }
        }
    }

    professionalDiscardButton.addEventListener("click", function () {
        restoreWorkExperienceFields();
        disableButtons();
        setupEventListeners();
        document.querySelectorAll(".error-border").forEach((field) => {
            field.classList.remove("error-border");
        });

        document.querySelectorAll(".p_error").forEach((errorElement) => {
            errorElement.textContent = "";
            errorElement.classList.add("hide");
        });
        
        // Reset form edit tracking
        window.isFormModified = false;
        window.isLeaving = true;
        window.tabFormState.professional = false;
        const index = window.formModified.indexOf('professional');
        if (index > -1) {
            window.formModified.splice(index, 1);
        }
    });

    function validateWorkExperienceFields(showErrors = true, type = 1) {
        let isValid = true;
        let hasChanges = false;

        // Only clear errors if we're going to show them
        if (showErrors) {
            // Clear previous errors
            document.querySelectorAll("#js_work_exp .p_error").forEach((errorElement) => {
                errorElement.textContent = "";
                errorElement.classList.add("hide");
            });

            document.querySelectorAll("#js_work_exp .error-border").forEach((field) => {
                field.classList.remove("error-border");
            });
        }

        // Get all work experience fields / academic fields
        const container = type === 1 ? "#js_work_exp" : "#js_div_qualification";

        // Get all required fields within the container
        const requiredFields = document.querySelectorAll(
            "#js_work_exp input[required]:not(:disabled), \
             #js_work_exp select[required]:not(:disabled), \
             #js_div_qualification input[required]:not(:disabled), \
             #js_div_qualification select[required]:not(:disabled)"
        );

        requiredFields.forEach((field) => {
            if (field.value.trim() !== "") {
                hasChanges = true;
            }
        });

        // Only validate if there are actual changes
        if (hasChanges) {
            requiredFields.forEach((field) => {
                const errorMsg = field.closest(".right-col")?.querySelector(".p_error");
                const choicesInner = field.closest(".choices__inner");

                if (field.value.trim() === "") {
                    isValid = false;
                    
                    // Only add error styling if showErrors is true
                    if (showErrors) {
                        
                        if (choicesInner) {
                            choicesInner.classList.add("error-border");
                        } else {
                            // Fallback: if no choices__inner found, add to field itself
                            field.classList.add("error-border");
                        }
            
                        if (errorMsg) {
                            errorMsg.textContent = "Required";
                            errorMsg.classList.remove("hide");
                        }
                    }
                } else if (showErrors) {
                    field.classList.remove("error-border");
                    if (choicesInner) {
                        choicesInner.classList.remove("error-border");
                    }
        
                    if (errorMsg) {
                        errorMsg.textContent = "";
                        errorMsg.classList.add("hide");
                    }
                }
            });
        }

        return { isValid, hasChanges };
    }

    professionalInfoSaveButton.addEventListener("click", function (event) {
        event.preventDefault(); // Always prevent default first
        
        // Update hidden inputs with current dropdown values before validation/submission
        updateInputValues("input_field_exp_to_month", "exp_to_month",
            (select) => (select.value && select.value !== "Month" ? select.value : "")
        );

        updateInputValues("input_field_exp_to_year", "exp_to_year",
            (select) => (select.value && select.value !== "Year" ? select.value : "")
        );

        updateInputValues("input_field_current_company", "current_role",
            (checkbox) => (checkbox.checked ? "1" : "0")
        );
        
        if (validateProfessionalInputFields()) {
            window.isLeaving = true; // Prevent leaving prompt
            window.tabFormState.professional = false; // Reset this tab's form state
            document.getElementById("form_professional_info").submit(); // Submit form if valid
        }
    });

    function setupEventListeners() {
        const workExperienceFields = getWorkExperienceFields();

        workExperienceFields.forEach((field) => {
            field.addEventListener("input", () => {
                // Simple change detection - just check if any field has a value
                const hasChanges = Array.from(workExperienceFields).some(f => f.value && f.value.trim() !== "");
                if (hasChanges) {
                    enableSaveButton();
                    updateFormModifiedState(field);
                }
            });
            field.addEventListener("change", () => {
                // Simple change detection - just check if any field has a value
                const hasChanges = Array.from(workExperienceFields).some(f => f.value && f.value.trim() !== "");
                if (hasChanges) {
                    enableSaveButton();
                    updateFormModifiedState(field);
                }
            });
        });

        // Add event listeners to current role checkboxes
        document.querySelectorAll('.current_company').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Get the parent work experience section
                const workExpSection = this.closest('.exp_main_wrap.professional');
                if (!workExpSection) return;
                
                // Find the end date fields
                const toMonth = workExpSection.querySelector('.exp_to_month');
                const toYear = workExpSection.querySelector('.exp_to_year');
                
                // Get corresponding hidden inputs that will be submitted
                const toMonthInput = workExpSection.querySelector('input[name="exp_to_month[]"]');
                const toYearInput = workExpSection.querySelector('input[name="exp_to_year[]"]');
                
                // Set the hidden current_role input
                const index = workExpSection ? workExpSection.id.split('_').pop() : 0;
                const currentRoleInput = document.getElementById(`current_role_${index}`);
                if (currentRoleInput) {
                    currentRoleInput.value = this.checked ? "1" : "0";
                }
                
                // Handle end date fields based on checkbox state
                if (this.checked) {
                    // CHECKED - Disable end date fields, store current values
                    
                    // Store current values for potential restoration
                    if (toMonth && toMonth.value) {
                        toMonth.dataset.previousValue = toMonth.value;
                        if (toMonthInput) toMonthInput.value = "";
                    }
                    
                    if (toYear && toYear.value) {
                        toYear.dataset.previousValue = toYear.value;
                        if (toYearInput) toYearInput.value = "";
                    }
                    
                                    // Reinitialize dropdowns with placeholders and disable them
                if (toMonth) {
                    populateMonthYearDropdown(toMonth, months.map(({ value, text }) => ({ _id: value, name: text })), "", 'Month', true);
                }
                
                if (toYear) {
                    populateMonthYearDropdown(toYear, years, "", 'Year', true);
                }
                    
                    // Clear any error styling on the end date fields
                    if (toMonth) clearError(toMonth);
                    if (toYear) clearError(toYear);
                    
                    // Clear section-specific error messages
                    const sectionErrors = workExpSection.querySelectorAll('.work_exp_err');
                    sectionErrors.forEach(err => {
                        // Only clear if it's a date-related error message
                        if (err.innerHTML.includes('joining and relieving') || err.innerHTML.includes('start and end duration')) {
                            err.classList.add('hide');
                            err.textContent = '';
                        }
                    });
                    
                    // Validate other required fields in the section - this is important
                    if (workExpSection) {
                        validateOtherWorkExperienceFields(workExpSection);
                    }
                } else {
                    // UNCHECKED - Enable end date fields, restore values if available
                    
                    // Reinitialize dropdowns as enabled and restore previous values
                    if (toMonth) {
                        const previousMonth = toMonth.dataset.previousValue || "";
                        populateMonthYearDropdown(toMonth, months.map(({ value, text }) => ({ _id: value, name: text })), previousMonth, 'Month', false);
                        if (toMonthInput && previousMonth) toMonthInput.value = previousMonth;
                    }
                    
                    if (toYear) {
                        const previousYear = toYear.dataset.previousValue || "";
                        populateMonthYearDropdown(toYear, years, previousYear, 'Year', false);
                        if (toYearInput && previousYear) toYearInput.value = previousYear;
                    }
                    
                    // Validate all fields in the section, including end date fields
                    validateProfessionalInputFields();
                }
                
                // Enable save button in all cases
                enableSaveButton();
                
                // Update checkbox states for all sections
                checkboxActive();
                
                // Update hidden fields
                updateInputValues("input_field_exp_to_month", "exp_to_month",
                    (select) => (select.value ? select.value : "Month")
                );

                updateInputValues("input_field_exp_to_year", "exp_to_year",
                    (select) => (select.value ? select.value : "year")
                );

                updateInputValues("input_field_current_company", "current_role",
                    (checkbox) => (checkbox.checked ? "1" : "0")
                );
            });
        });

        // Bind input/change events on all relevant fields to call updateFormModifiedState
        document.querySelectorAll('input, select, textarea').forEach(el => {
            el.addEventListener('input', e => {
                if (e.target.closest('form') && e.target.closest('.profile-wraper')) {
                    updateFormModifiedState(e.target);
                }
            });
            el.addEventListener('change', e => {
                if (e.target.closest('form') && e.target.closest('.profile-wraper')) {
                    updateFormModifiedState(e.target);
                }
            });
        });
    }

    document.getElementById("add_work_exp").addEventListener("click", function () {
        // Don't enable save button just on adding new section
        setupEventListeners();
    });

    // ==========================================================================
    // ACADEMIC EXPERIENCE - FORM HANDLING & VALIDATION
    // ==========================================================================
    
    //  Academic details validation
    const academicForm = document.getElementById("form_academics_info")
    const AcademicInfoSaveButton = academicForm.querySelector(".btn.save");
    const AcademicDiscardButton = academicForm.querySelector(".btn.discard");
    const academicExperienceFields = document.querySelectorAll("#js_div_qualification input, #js_div_qualification select");

    function enableButtons() {
        AcademicInfoSaveButton.removeAttribute("disabled");
        AcademicInfoSaveButton.removeAttribute("aria-disabled");
        AcademicDiscardButton.removeAttribute("disabled");
        AcademicDiscardButton.removeAttribute("aria-disabled");
    }
    function disableSaveButtons() {
        AcademicInfoSaveButton.setAttribute("disabled", "true");
        AcademicInfoSaveButton.setAttribute("aria-disabled", "true");

        AcademicDiscardButton.setAttribute("disabled", "true");
        AcademicDiscardButton.setAttribute("aria-disabled", "true");
    }

    function getAcademicsExperienceFields() {
        return document.querySelectorAll("#js_div_qualification input, #js_div_qualification select, #js_academic_exp select");
    }

    function validateAcademicFields(showErrors = true) {
        let hasChanges = false;
        let isValid = true;
        const fields = getAcademicsExperienceFields();
        
        // Only clear errors if we're going to show them
        if (showErrors) {
            document.querySelectorAll("#js_div_qualification .p_error").forEach((errorElement) => {
                errorElement.textContent = "";
                errorElement.classList.add("hide");
            });

            document.querySelectorAll("#js_div_qualification .error-border").forEach((field) => {
                field.classList.remove("error-border");
            });
        }
        
        fields.forEach(field => {
            if (field.value.trim() !== "") {
                hasChanges = true;
            }

            if (field.hasAttribute('required') && field.value.trim() === "") {
                isValid = false;
                
                // Only show errors if requested
                if (showErrors) {
                    const errorMsg = field.closest(".right-col")?.querySelector(".p_error");
                    const choicesInner = field.closest(".choices__inner");
                    
                    // Only add error-border to choices__inner, not to the field itself (to be consistent with other dropdowns)
                    if (choicesInner) {
                        choicesInner.classList.add("error-border");
                    } else {
                        // Fallback: if no choices__inner found, add to field itself
                        field.classList.add("error-border");
                    }
        
                    if (errorMsg) {
                        errorMsg.textContent = "Required";
                        errorMsg.classList.remove("hide");
                    }
                }
            }
        });

        return hasChanges && isValid;
    }

    function setupAcademicEventListeners() {
        const academicExperienceFields = getAcademicsExperienceFields();
        academicExperienceFields.forEach((field) => {
            field.addEventListener("input", () => {
                enableButtons();
            });
            field.addEventListener("change", () => {
                enableButtons();
            });
        });
    }

    // Initial setup of event listeners
    setupAcademicEventListeners();

    document.getElementById("js_div_qualification").addEventListener("click", function (event) {
        if (event.target.classList.contains("delete")) {
            if (validateAcademicFields()) {
                enableButtons();
            }
        }
    });

    document.getElementById("add_academics_exp").addEventListener("click", function () {
        // Don't enable save button just on adding new section
        setupAcademicEventListeners();
    });

    function clearAcademicExperienceFields() {
        getAcademicsExperienceFields().forEach((field) => {
            field.value = "";
        });
    }

    // Add this new function to restore academic fields to their original values
    function restoreAcademicExperienceFields() {
        if (academicExperience && academicExperience.length) {
            // Restore existing academic experiences
            academicExperience.forEach((exp, i) => {
                const qualificationSelect = document.getElementById(`field_qualification_${i}`);
                const instituteInput = document.querySelector(`#exp_main_wrap_a_${i} .institute_name`);
                const specializationInput = document.querySelector(`#exp_main_wrap_a_${i} .field_specialization`);
                const fromMonthSelect = document.getElementById(`a_from_month_${i}`);
                const fromYearSelect = document.getElementById(`a_from_year_${i}`);
                const toMonthSelect = document.getElementById(`a_to_month_${i}`);
                const toYearSelect = document.getElementById(`a_to_year_${i}`);
                
                if (instituteInput) instituteInput.value = exp.institute_name || "";
                if (specializationInput) specializationInput.value = exp.field_specialization || "";
                
                // Restore dropdowns with Choices.js
                if (qualificationSelect && qualificationSelect.choicesInstance) {
                    qualificationSelect.choicesInstance.setChoiceByValue(exp.qualification || "");
                }
                
                if (fromMonthSelect && fromMonthSelect.choicesInstance) {
                    fromMonthSelect.choicesInstance.setChoiceByValue(exp.course_from_month || "");
                }
                
                if (fromYearSelect && fromYearSelect.choicesInstance) {
                    fromYearSelect.choicesInstance.setChoiceByValue(exp.course_from_year || "");
                }
                
                if (toMonthSelect && toMonthSelect.choicesInstance) {
                    toMonthSelect.choicesInstance.setChoiceByValue(exp.course_to_month || "");
                }
                
                if (toYearSelect && toYearSelect.choicesInstance) {
                    toYearSelect.choicesInstance.setChoiceByValue(exp.course_to_year || "");
                }
            });
        }
        
        // Remove any dynamically added academic experiences that weren't saved
        const expCount = document.querySelectorAll(".exp_main_wrap.academic").length;
        if (expCount > academicExperience.length) {
            for (let i = academicExperience.length; i < expCount; i++) {
                const expBlock = document.getElementById(`exp_main_wrap_a_${i}`);
                if (expBlock) expBlock.remove();
            }
        }
    }

    AcademicDiscardButton.addEventListener("click", function () {
        restoreAcademicExperienceFields();
        disableSaveButtons();
        setupAcademicEventListeners();
        document.querySelectorAll(".error-border").forEach((field) => {
            field.classList.remove("error-border");
        });

        document.querySelectorAll(".p_error").forEach((errorElement) => {
            errorElement.textContent = "";
            errorElement.classList.add("hide");
        });
        
        // Reset form edit tracking
        window.isFormModified = false;
        window.isLeaving = true;
        window.tabFormState.academics = false;
        const index = window.formModified.indexOf('academics');
        if (index > -1) {
            window.formModified.splice(index, 1);
        }
    });

    AcademicInfoSaveButton.addEventListener("click", function (event) {
        event.preventDefault(); // Always prevent default first
        
        if (validateAcademicInputFields()) {
            window.isLeaving = true; // Prevent leaving prompt 
            window.tabFormState.academics = false; // Reset this tab's form state
            document.getElementById("form_academics_info").submit(); // Submit form if valid
        }
    });

    // ==========================================================================
    // CURRENT ROLE CHECKBOX HANDLING
    // ==========================================================================
    
    // Handle "Current Role" checkbox : IMPLEMENTED BELOW 
    document.addEventListener("change", function (event) {
        if (event.target.classList.contains("current_company")) {
            const parentRow = event.target.closest(".exp-inner-wrap.end_date");
            const workExpSection = event.target.closest(".exp_main_wrap.professional");
            
            // Get explicit references to the month and year dropdowns
            const toMonthField = parentRow.querySelector(".exp_to_month");
            const toYearField = parentRow.querySelector(".exp_to_year");
            
            // Get corresponding hidden inputs that will be submitted
            const toMonthInput = parentRow.querySelector('input[name="exp_to_month[]"]');
            const toYearInput = parentRow.querySelector('input[name="exp_to_year[]"]');
            
            // Set the hidden current_role input
            const index = workExpSection ? workExpSection.id.split('_').pop() : 0;
            const currentRoleInput = document.getElementById(`current_role_${index}`);
            if (currentRoleInput) {
                currentRoleInput.value = event.target.checked ? "1" : "0";
            }
            
            // Handle end date fields based on checkbox state
            if (event.target.checked) {
                // CHECKED - Disable end date fields, store current values
                
                // Only store values that are meaningful user selections (not placeholders or auto-selected values)
                if (toMonthField && toMonthField.value && 
                    toMonthField.value !== "" && 
                    toMonthField.value !== "Month" &&
                    !toMonthField.closest('.choices__inner')?.querySelector('.choices__placeholder')) {
                    toMonthField.dataset.previousValue = toMonthField.value;
                } else {
                    // Clear any existing previous value since current value is not a user selection
                    toMonthField.dataset.previousValue = "";
                }
                
                if (toYearField && toYearField.value && 
                    toYearField.value !== "" && 
                    toYearField.value !== "Year" &&
                    !toYearField.closest('.choices__inner')?.querySelector('.choices__placeholder')) {
                    toYearField.dataset.previousValue = toYearField.value;
                } else {
                    // Clear any existing previous value since current value is not a user selection
                    toYearField.dataset.previousValue = "";
                }
                
                // Clear hidden inputs
                if (toMonthInput) toMonthInput.value = "";
                if (toYearInput) toYearInput.value = "";
                
                // Reinitialize dropdowns with placeholders and disable them
                if (toMonthField) {
                    populateMonthYearDropdown(toMonthField, months.map(({ value, text }) => ({ _id: value, name: text })), undefined, 'Month', true);
                }
                
                if (toYearField) {
                    populateMonthYearDropdown(toYearField, years, undefined, 'Year', true);
                }
                
                // Clear any error styling on the end date fields
                if (toMonthField) clearError(toMonthField);
                if (toYearField) clearError(toYearField);
                
                // Clear section-specific error messages for end date validation
                const sectionErrors = workExpSection.querySelectorAll('.work_exp_err');
                sectionErrors.forEach(err => {
                    // Only clear if it's a date-related error message
                    if (err.innerHTML.includes('joining and relieving') || err.innerHTML.includes('start and end duration')) {
                        err.classList.add('hide');
                        err.textContent = '';
                    }
                });
                
                // Validate other required fields in the section - this is important
                if (workExpSection) {
                    validateOtherWorkExperienceFields(workExpSection);
                }
            } else {
                // UNCHECKED - Enable end date fields, restore values if available
                
                // Get previous values - only restore if they exist and are not empty/placeholder values
                const previousMonth = toMonthField?.dataset.previousValue;
                const previousYear = toYearField?.dataset.previousValue;
                
                // Only use previous values if they are meaningful (not empty, not placeholder values)
                const validPreviousMonth = previousMonth && previousMonth !== "" && previousMonth !== "Month" ? previousMonth : null;
                const validPreviousYear = previousYear && previousYear !== "" && previousYear !== "Year" ? previousYear : null;
                
                // Reinitialize dropdowns as enabled - pass null for empty state
                if (toMonthField) {
                    populateMonthYearDropdown(
                        toMonthField, 
                        months.map(({ value, text }) => ({ _id: value, name: text })), 
                        validPreviousMonth, // Only restore if it's a valid user selection
                        'Month', 
                        false
                    );
                    if (toMonthInput && validPreviousMonth) toMonthInput.value = validPreviousMonth;
                }
                
                if (toYearField) {
                    populateMonthYearDropdown(
                        toYearField, 
                        years, 
                        validPreviousYear, // Only restore if it's a valid user selection
                        'Year', 
                        false
                    );
                    if (toYearInput && validPreviousYear) toYearInput.value = validPreviousYear;
                }
                
                // Validate all fields in the section, including end date fields
                validateProfessionalInputFields();
            }
            
            // Enable save button in all cases
            enableSaveButton();
            
            // Update checkbox states for all sections
            checkboxActive();
            
            // Update hidden fields
            updateInputValues("input_field_exp_to_month", "exp_to_month",
                (select) => (select.value && select.value !== "Month" ? select.value : "")
            );

            updateInputValues("input_field_exp_to_year", "exp_to_year",
                (select) => (select.value && select.value !== "Year" ? select.value : "")
            );

            updateInputValues("input_field_current_company", "current_role",
                (checkbox) => (checkbox.checked ? "1" : "0")
            );
        }
    });


    function initializeProfessionalDropdowns(specificIndex = null) {
        // Determine the selector based on whether we're targeting a specific block
        const baseSelector = specificIndex !== null 
            ? `#exp_main_wrap_w_${specificIndex} ` 
            : '';
        
        // Initialize searchable dropdowns for both pre-populated and dynamic elements
        // For industry dropdowns - handle both pre-populated and dynamic
        document.querySelectorAll(`${baseSelector}.select_industry_prepopulate, ${baseSelector}.select_industry, ${baseSelector}.industry`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(select, TaxonomyData?.industry || [], null, true, false);
            }
        });
        
        // For department/job function dropdowns - handle both pre-populated and dynamic
        document.querySelectorAll(`${baseSelector}.job_function_prepopulate, ${baseSelector}.job_function`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(select, TaxonomyData?.job_function || [], null, true, false);
            }
        });
        
        // Initialize non-searchable dropdowns with natural order
        document.querySelectorAll(`${baseSelector}.exp_from_month, ${baseSelector}.exp_to_month`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(
                    select, 
                    months.map(({value, text}) => ({_id: value, name: text})), 
                    null, 
                    false, // No search
                    false, // No sorting
                    "Month"
                );
            }
        });
        
        document.querySelectorAll(`${baseSelector}.exp_from_year, ${baseSelector}.exp_to_year`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(
                    select,
                    years,
                    null,
                    false, // No search
                    false, // No sorting
                    "Year"
                );
            }
        });
    }

    function initializeAcademicDropdowns(specificIndex = null) {
        // Determine the selector based on whether we're targeting a specific block
        const baseSelector = specificIndex !== null 
            ? `#exp_main_wrap_a_${specificIndex} ` 
            : '';
        
        // For qualification, use searchable dropdown
        document.querySelectorAll(`${baseSelector}.field_qualification`).forEach(select => {
            initializeChoicesDropdown(select, TaxonomyData?.qualification || [], null, true, false);
        });
        
        // For month dropdowns, use non-searchable and maintain order
        document.querySelectorAll(`${baseSelector}.course_from_month, ${baseSelector}.course_to_month`).forEach(select => {
            initializeChoicesDropdown(
                select, 
                months.map(({value, text}) => ({_id: value, name: text})), 
                null, 
                false, // No search
                false, // No sorting
                "Month"
            );
        });
        
        // For year dropdowns, use non-searchable and maintain order
        document.querySelectorAll(`${baseSelector}.course_from_year, ${baseSelector}.course_to_year`).forEach(select => {
            initializeChoicesDropdown(
                select,
                years,
                null,
                false, // No search
                false, // No sorting
                "Year"
            );
        });
    }

    // ==========================================================================
    // COMMON UTILITY FUNCTIONS (Basic & Contact Forms)
    // ==========================================================================

    function addInputChangeListeners(fields, saveButton, discardButton) {
        fields.forEach(input => {
            input.addEventListener("input", () => {
                toggleSaveButtonState(true, saveButton, discardButton);
                // Set form as modified
                updateFormModifiedState(input);
                // Don't clear errors on input - wait for form submission
            });
            input.addEventListener("change", () => {
                toggleSaveButtonState(true, saveButton, discardButton);
                // Set form as modified
                updateFormModifiedState(input);
                // Don't clear errors on change - wait for form submission
            });
        });
    }

    function showError(element, message) {
        if (!element) return; // Guard against null/undefined elements
        
        // Find the closest right-col div
        const parentCol = element.closest('.right-col');
        // Find the error message element
        const errorElement = parentCol?.querySelector('.p_error');
        
        if (errorElement && message) {
            errorElement.textContent = message;
            errorElement.classList.remove('hide');
            errorElement.style.display = 'block'; // Ensure visibility
        }

        // Handle date fields with month/year in flex-date container
        if (element.classList.contains('exp_from_month') ||
            element.classList.contains('exp_from_year') ||
            element.classList.contains('exp_to_month') ||
            element.classList.contains('exp_to_year') ||
            element.classList.contains('course_from_month') ||
            element.classList.contains('course_from_year') ||
            element.classList.contains('course_to_month') ||
            element.classList.contains('course_to_year')) {
        
            // Find the select_wraper that contains this element
            const selectWrapper = element.closest('.select_wraper');
            if (selectWrapper) {
                // Find the Choices container within this specific wrapper
                const choicesContainer = selectWrapper.querySelector('.choices');
                if (choicesContainer) {
                    const innerContainer = choicesContainer.querySelector('.choices__inner');
                    if (innerContainer) {
                        innerContainer.classList.add('error-border');
                    }
                }
            }
            return; // Exit after handling date fields
        }

        // Handle department, industry, and other special dropdowns
        if (element.classList.contains('job_function') || 
            element.classList.contains('job_function_prepopulate') ||
            element.classList.contains('industry') ||
            element.classList.contains('select_industry') ||
            element.classList.contains('select_industry_prepopulate') ||
            element.id === 'country_of_residence' ||
            element.id === 'state') {
        
            // Find the parent right-col
            const rightCol = element.closest('.right-col');
            if (rightCol) {
                // Find the Choices container in this right-col
                const choicesContainer = rightCol.querySelector('.choices');
                if (choicesContainer) {
                    // Add error-border to the inner container
                    const innerContainer = choicesContainer.querySelector('.choices__inner');
                    if (innerContainer) {
                        innerContainer.classList.add('error-border');
                    }
                }
            }
            return; // Exit after handling these special fields
        }
        
        // Handle regular select with Choices.js
        if (element.tagName === 'SELECT') {
            // First look for closest .choices container
            let choicesContainer = element.closest('.choices');
            
            // If not found as a parent, try finding by ID
            if (!choicesContainer && element.id) {
                choicesContainer = document.querySelector(`.choices[data-id="${element.id}"], .choices#${element.id}_choices`);
            }
            
            if (choicesContainer) {
                // Find and add error-border to choices__inner
                const innerContainer = choicesContainer.querySelector('.choices__inner');
                if (innerContainer) {
                    innerContainer.classList.add('error-border');
                }
                return; // Exit after handling Choices.js containers
            }
        }
        
        // Default case: add error-border to the element itself
        element.classList.add('error-border');
    }

    function clearError(element) {
        if (!element) return; // Guard against null/undefined elements
        
        const parentCol = element.closest('.right-col');
        const errorElement = parentCol?.querySelector('.p_error');
        
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.classList.add('hide');
        }

        // Handle date fields with month/year in flex-date container
        if (element.classList.contains('exp_from_month') ||
            element.classList.contains('exp_from_year') ||
            element.classList.contains('exp_to_month') ||
            element.classList.contains('exp_to_year') ||
            element.classList.contains('course_from_month') ||
            element.classList.contains('course_from_year') ||
            element.classList.contains('course_to_month') ||
            element.classList.contains('course_to_year')) {
        
            // Find the select_wraper that contains this element
            const selectWrapper = element.closest('.select_wraper');
            if (selectWrapper) {
                // Find the Choices container within this specific wrapper
                const choicesContainer = selectWrapper.querySelector('.choices');
                if (choicesContainer) {
                    // Remove error-border from both choices container and inner container
                    choicesContainer.classList.remove('error-border');
                    const innerContainer = choicesContainer.querySelector('.choices__inner');
                    if (innerContainer) {
                        innerContainer.classList.remove('error-border');
                    }
                }
            }
            // Also remove from the element itself (in case it was added by validation functions)
            element.classList.remove('error-border');
            return; // Exit after handling date fields
        }

        // Handle department, industry, and other special dropdowns
        if (element.classList.contains('job_function') || 
            element.classList.contains('job_function_prepopulate') ||
            element.classList.contains('industry') ||
            element.classList.contains('select_industry') ||
            element.classList.contains('select_industry_prepopulate') ||
            element.id === 'country_of_residence' ||
            element.id === 'state') {
        
            // Find the parent right-col
            const rightCol = element.closest('.right-col');
            if (rightCol) {
                // Find the Choices container in this right-col
                const choicesContainer = rightCol.querySelector('.choices');
                if (choicesContainer) {
                    // Remove error-border from the inner container
                    const innerContainer = choicesContainer.querySelector('.choices__inner');
                    if (innerContainer) {
                        innerContainer.classList.remove('error-border');
                    }
                }
            }
            return; // Exit after handling these special fields
        }
        
        // Handle regular select with Choices.js
        if (element.tagName === 'SELECT') {
            // First look for closest .choices container
            let choicesContainer = element.closest('.choices');
            
            // If not found as a parent, try finding by ID
            if (!choicesContainer && element.id) {
                choicesContainer = document.querySelector(`.choices[data-id="${element.id}"], .choices#${element.id}_choices`);
            }
            
            if (choicesContainer) {
                // Remove error-border from inner container
                const innerContainer = choicesContainer.querySelector('.choices__inner');
                if (innerContainer) {
                    innerContainer.classList.remove('error-border');
                }
                return; // Exit after handling Choices.js containers
            }
        }
        
        // Default case: remove error-border from the element itself
        element.classList.remove('error-border');
    }

    // New function to validate individual required fields
    function validateRequiredField(field, showErrors = true) {
        if (!field) return true;
        
        const isEmpty = !field.value || field.value.trim() === "";
        const isDisabled = field.disabled;
        
        if (isEmpty && !isDisabled && field.hasAttribute('required')) {
            if (showErrors) {
                showError(field, 'Required');
            }
            return false;
        } else if (showErrors) {
            clearError(field);
        }
        
        return true;
    }

    // New function specifically for Choices.js dropdown validation
    function validateChoicesDropdown(selectElement, showErrors = true) {
        if (!selectElement) return true;
        
        const isEmpty = !selectElement.value || selectElement.value.trim() === "";
        const isDisabled = selectElement.disabled;
        
        if (isEmpty && !isDisabled && selectElement.hasAttribute('required')) {
            if (showErrors) {
                // Find and add error border to the Choices inner container
                const parentCol = selectElement.closest('.right-col');
                if (parentCol) {
                    const choicesContainer = parentCol.querySelector('.choices');
                    if (choicesContainer) {
                        const innerContainer = choicesContainer.querySelector('.choices__inner');
                        if (innerContainer) {
                            innerContainer.classList.add('error-border');
                        }
                    }
                }
                showError(selectElement, "Required");
            }
            return false;
        }
        
        // When field is valid, explicitly clear any error styling
        if (showErrors) {
            clearError(selectElement);
        }
        return true;
    }

    function initializeDropdown(select ,selectedValue = "",options) {
        // Initialize Choices.js if not already initialized
        if (!select.choicesInstance) {
            select.choicesInstance = new Choices(select, {
                searchEnabled: false, // Disable search
                shouldSort: false, // Maintain order
                removeItemButton: false, // No remove button
                position: 'bottom',
                itemSelectText: '' 
        });
    }

    
        // Populate the dropdown dynamically
        select.choicesInstance.clearChoices();
        select.choicesInstance.setChoices(
            options.map(({ value, label }) => ({
                value,
                label,
                selected: value === selectedValue, // Pre-select if value matches
            })),
            "value",
            "label",
            true
        );
    }

    // ==========================================================================
    // CONTACT DETAILS SECTION - START
    // ==========================================================================

    // Clear any existing values from select elements before initializing Choices.js
    const countrySelectElement = document.getElementById("country_of_residence");
    if (countrySelectElement) {
        countrySelectElement.value = "";
        countrySelectElement.selectedIndex = -1;
    }

    // Fetching contact details elements and storing them in constants
    const countryDropdown = new Choices("#country_of_residence", {
        removeItemButton: true,
        searchEnabled: true,
        maxItemCount: 10,
        itemSelectText: "",
        shouldSort: false,
        placeholder: true,
        placeholderValue: "--Select--",
        silent: true, // Prevent auto-selection
        allowHTML: false
    });
    const stateDropdown = new Choices("#state", {
        removeItemButton: true,
        searchEnabled: true,
        itemSelectText: "",
        shouldSort: false,
        placeholder: true,
        placeholderValue: "--Select--",
        silent: true, // Prevent auto-selection
        allowHTML: false
    });

    const timezoneDropdown = new Choices("#timezone-dropdown", {
        searchEnabled: true,
        shouldSort: false,
        placeholder: true,
        placeholderValue: "Select Timezone",
        silent: true, // Prevent auto-selection
        allowHTML: true
    });

    const contactForm = document.getElementById("form_contact_info");
    const ContactsaveButton = contactForm.querySelector(".btn.save")
    const discardButton = document.querySelector(".btn.discard.contact")
    const emailInput = document.getElementById("user_email")
    const phoneInput = document.getElementById("phone_no");
    const addressInput = document.getElementById("correspondence_address")
    const countrySelect = document.getElementById("country_of_residence");
    const stateSelect = document.getElementById("state");
    const cityInput = document.getElementById("location");
    const timezoneSelect = document.getElementById("timezone-dropdown");
    const timezoneError = document.getElementById("timezone-error");

    // Store choices instances for consistency
    if (countrySelect) countrySelect.choicesInstance = countryDropdown;
    if (stateSelect) stateSelect.choicesInstance = stateDropdown;
    if (timezoneSelect) timezoneSelect.choicesInstance = timezoneDropdown;

    // Ensure email field is visible if disabled
    if (emailInput && emailInput.disabled) {
        emailInput.style.opacity = "1";
        emailInput.style.visibility = "visible";
        emailInput.style.display = "block";
    }

    function validateContactForm() {
        let isValid = true;

        // Clear previous errors first
        document.querySelectorAll("#form_contact_info .p_error").forEach((errorElement) => {
            errorElement.textContent = "";
            errorElement.classList.add("hide");
        });

        document.querySelectorAll("#form_contact_info .error-border").forEach((field) => {
            field.classList.remove("error-border");
        });
        
        // Also clear error borders from all Choices.js containers
        document.querySelectorAll("#form_contact_info .choices").forEach((choices) => {
            choices.classList.remove("error-border");
        });

        // Validate required fields using the new validation functions
        if (!validateRequiredField(phoneInput)) isValid = false;
        if (!validateRequiredField(cityInput)) isValid = false;
        if (!validateChoicesDropdown(countrySelect)) isValid = false;
        if (!validateChoicesDropdown(stateSelect)) isValid = false;
        if (!validateChoicesDropdown(timezoneSelect)) isValid = false;
        
        return isValid;
    }
    
    
    ContactsaveButton.addEventListener("click", function (event) {
        if (!validateContactForm()) {
            event.preventDefault(); 
        } else {
            window.isLeaving = true; // Prevent leaving prompt
            window.tabFormState.contact = false; // Reset this tab's form state
        }
    });

    const contactDetailsFields = [emailInput , phoneInput, addressInput , countrySelect, stateSelect, cityInput , timezoneSelect];

    // enable buttons
    addInputChangeListeners(contactDetailsFields, ContactsaveButton , discardButton)

    discardButton.addEventListener("click", function () {
        restoreContactDetailsFields();
        toggleSaveButtonState(false, ContactsaveButton, discardButton);
        addInputChangeListeners(contactDetailsFields, ContactsaveButton, discardButton);
        
        // Clear errors for all contact form fields
        contactDetailsFields.forEach(field => {
            if (field) {
                clearError(field);
            }
        });
        
        // Ensure disabled inputs like email remain visible
        ensureDisabledInputsVisible();
        
        // Reset form edit tracking
        window.isFormModified = false;
        window.isLeaving = true;
        window.tabFormState.contact = false;
        const index = window.formModified.indexOf('contact');
        if (index > -1) {
            window.formModified.splice(index, 1);
        }
    });
    
    // ==========================================================================
    // CONTACT DETAILS SECTION - END ABOVE
    // ==========================================================================

    // Add this new function to restore contact details fields to their original values
    function restoreContactDetailsFields() {
        // Restore text and email inputs - email is disabled but should still show its value
        if (emailInput) {
            emailInput.value = userData.user_email || userData.email || "";
            
            // Since email input is disabled, ensure it remains visible
            if (emailInput.disabled) {
                emailInput.style.opacity = "1";
                emailInput.style.visibility = "visible";
                emailInput.style.display = "block";
            }
        }
        
        if (phoneInput) phoneInput.value = userData.phone_no || "";
        if (addressInput) addressInput.value = userData.correspondence_address || "";
        if (cityInput) cityInput.value = userData.location || "";
        
        // Restore country dropdown - properly restore user's saved country
        if (countrySelect && countryDropdown) {
            if (userData?.country_of_residence) {
                // Find and restore the user's existing country
                const userCountry = countryDataIe.find(country => country.name === userData.country_of_residence);
                if (userCountry) {
                    countryDropdown.setChoiceByValue(JSON.stringify({ id: userCountry.id, name: userCountry.name }));
                    
                    // Also restore the state if user has one
                    if (userData?.state) {
                        // Populate states for the selected country first
                        populateStatesDropdown(userCountry.id).then(() => {
                            setTimeout(() => {
                                if (stateDropdown && userData.state) {
                                    stateDropdown.setChoiceByValue(userData.state);
                                }
                            }, 100);
                        });
                    }
                } else {
                    countryDropdown.setChoiceByValue("");
                }
            } else {
                countryDropdown.setChoiceByValue("");
            }
        }
        
        // Reset state dropdown to saved value or placeholder
        if (stateSelect && stateDropdown) {
            if (userData?.state && userData?.country_of_residence) {
                // State will be set above after country population
            } else {
                stateDropdown.setChoiceByValue("");
            }
        }
        
        // Restore timezone dropdown
        if (timezoneSelect && timezoneDropdown && userData.timezone) {
            timezoneDropdown.setChoiceByValue(userData.timezone);
        }
        
        // Restore country code dropdown
        if (CountryCodeSelect && userData.country_code) {
            for (let i = 0; i < CountryCodeSelect.options.length; i++) {
                if (CountryCodeSelect.options[i].value === userData.country_code) {
                    CountryCodeSelect.selectedIndex = i;
                    CountryCodeSelect.dispatchEvent(new Event('change'));
                    break;
                }
            }
        }
    }

    const optionsLength = CountryCodeSelect.options.length;
    let countryData = '';

    if (optionsLength === 10) {
        const separatorOption = new Option('---------------------------------------------------------', '', false, false);
        separatorOption.disabled = true;
        CountryCodeSelect.add(separatorOption);
    }

    function populateCountryDropdown() {
        countryDropdown.clearChoices();
        countryDropdown.setChoices(
            [{ value: "", label: "Select your country", disabled: true },
            ...countryDataIe.map(({ id, name }) => ({ value: JSON.stringify({ id, name }), label: name }))],
            "value",
            "label",
            true
        );
        
        // Only clear selection if user doesn't have an existing country_of_residence
        if (!userData?.country_of_residence) {
            countryDropdown.setChoiceByValue("");
        } else {
            // Try to set the user's existing country
            const userCountry = countryDataIe.find(country => country.name === userData.country_of_residence);
            if (userCountry) {
                const countryValue = JSON.stringify({ id: userCountry.id, name: userCountry.name }); 
                countryDropdown.setChoiceByValue(countryValue);
            }
        }
    }

    async function populateStatesDropdown(selectedCountryId) {
        // Clear and reset state dropdown
        stateDropdown.clearStore();
        
        if (!selectedCountryId) {
            stateDropdown.setChoices([{ value: "", label: "--Select--", disabled: true, selected: true }], "value", "label", true);
            return;
        }
        
        try {
            const response = await fetch(`/user/profile/get-region-by-country?countryId=${selectedCountryId}`);
            const data = await response.json();
    
            const seenRegions = new Set();
            const stateChoices = (data || [])
                .filter(({ region_name }) => {
                    if (seenRegions.has(region_name)) {
                        return false;
                    }
                    seenRegions.add(region_name);
                    return true;
                })
                .map(({ city_id, region_name }) => ({
                    id: city_id,
                    value: region_name,
                    label: region_name,
                    selected: false // Explicitly set to false
                }));
    
            if (stateChoices.length === 0) {
                stateChoices.push({ value: "", label: "No states available", disabled: true });
            }

            // Always put placeholder first and mark it as selected
            const allChoices = [
                { value: "", label: "--Select--", disabled: true, selected: true },
                ...stateChoices
            ];
    
            stateDropdown.setChoices(allChoices, "value", "label", true);
        }
         catch (error) {
            console.error("Error fetching state data:", error);
            stateDropdown.setChoices([{ value: "", label: "--Select--", disabled: true, selected: true }], "value", "label", true);
        }
    }

    // Populate state dropdown if user has existing country and state data
    // This needs to happen after country dropdown is set
    async function initializeUserCountryAndState() {
        
        if(userData?.country_of_residence && userData?.state){
            const selectedCountry = countryDataIe.find(({ name }) => name === userData?.country_of_residence);

            if (selectedCountry) {
                await populateStatesDropdown(selectedCountry.id);
                // Set the user's existing state after states are loaded
                setTimeout(() => {
                    if (stateDropdown && userData.state) {
                        stateDropdown.setChoiceByValue(userData.state);
                    }
                }, 100);
            }
        }
    }

    countrySelect.addEventListener("change", async function () {
        // First, clear the state dropdown immediately
        stateDropdown.setChoiceByValue("");
        
        const selectedCountryValue = countrySelect.value;
        if (!selectedCountryValue || selectedCountryValue.trim() === "") {
            // If no country selected, clear state dropdown completely
            stateDropdown.clearStore();
            stateDropdown.setChoices([{ value: "", label: "--Select--", disabled: true }], "value", "label", true);
            stateDropdown.setChoiceByValue("");
            return;
        }
        
        try {
            const selectedCountry = JSON.parse(selectedCountryValue)?.id;
            await populateStatesDropdown(selectedCountry);
        } catch (error) {
            console.error("Error parsing country selection:", error);
            // On error, ensure state is reset
            stateDropdown.setChoiceByValue("");
        }
    });

    populateCountryDropdown();

    // Initialize country and state after country dropdown is populated
    setTimeout(async () => {
        await initializeUserCountryAndState();
        
        // Only clear country selection for users who have no saved country data
        // AND only if the country wasn't already set properly
        if (countryDropdown && !userData?.country_of_residence) {
            const currentCountryValue = countrySelect.value;
            if (!currentCountryValue || currentCountryValue.trim() === "") {
                countryDropdown.setChoiceByValue("");
                // Also clear the underlying select element
                const selectElement = document.getElementById("country_of_residence");
                if (selectElement) {
                    selectElement.value = "";
                    selectElement.selectedIndex = -1;
                }
            }
        }
    }, 200);

    countryDataIe.forEach(({ code, name }, index) => {    
        if (index === 10) {
            const separator = new Option('---------------------------------------------------------', '');
            separator.disabled = true;
            CountryCodeSelect.add(separator);
        }

        const option = new Option(`${code} \u00A0\u00A0\u00A0 - \u00A0\u00A0\u00A0 ${name}`, code);
        
        // Mark the user's country as selected
        if (userData?.country_code && code === userData?.country_code) {
            option.selected = true;
        }
    
        CountryCodeSelect.add(option);
    });

    countryData = JSON.stringify(countryDataIe[0]);
    const selectedCountryCodeSpan = document.getElementById('country_code_span');

    CountryCodeSelect.addEventListener('change', function ({ target: { value } }) {
        countryData = countryDataIe.find(({ code }) => code === value);
        selectedCountryCodeSpan.textContent = `+${countryData.phnCode}`;
    });

    CountryCodeSelect.dispatchEvent(new Event('change'));

    async function initializeTimezoneDropdown() {
        try {
            const timezones = await fetchTimezones();
            const timezoneChoices = timezones.flatMap(({ text, children }) =>
                [{ label: text, disabled: true }, ...children.map(({ id, name }) => ({ value: id, label: name, selected : id === userData.timezone,customProperties: { group: text } }))]
            );

            timezoneDropdown.clearChoices();
            timezoneDropdown.setChoices(
                [{ value: "", label: "Select your timezone", disabled: true }, ...timezoneChoices],
                "value",
                "label",
                true
            );
        } catch (error) {
            console.error("Error fetching timezones:", error);
        }
    }

    initializeTimezoneDropdown();

    // ==========================================================================
    // BASIC DETAILS SECTION - START
    // ==========================================================================
    
    // Fetching basic details elements and storing them in constants
    const title = document.getElementById("title");
    const firstName = document.getElementById("first_name");
    const middleName = document.getElementById("middle_name");
    const lastName = document.getElementById("last_name");
    const gender = document.getElementById("gender");
    const dob = document.getElementById("datepicker");
    const profilePicture = document.querySelector("input[name='profile_pic']");
    const profilePictureImageElement = document.getElementById("profile_picture");
    const linkedInUrl = document.getElementById("user_linkedin_url");
    const trainingFundedBy = document.querySelector("input[name='training_funded_by']"); // Selected radio button
    const basicForm = document.getElementById("form_basic_details");
    const basicSaveButton = basicForm.querySelector('.btn.save')
    const basicdiscardButton = basicForm.querySelector('.btn.discard')
    const changePictureBtn = document.getElementById("change_picture");
    const fileInput = document.getElementById("file_input");
    const deletePictureBtn = document.getElementById("delete_picture");

    const genderOptions = [
        { value: "", label: "--Select--", disabled: true },
        { value: "Female", label: "Female" },
        { value: "Male", label: "Male" },
        { value: "Non-binary", label: "Non-binary" },
        { value: "Prefer not to say", label: "Prefer not to say" },
        { value: "Other", label: "Other" },
    ];
    const titleOptions = [
        { value: "", label: "--Select--", disabled: true },
        { value: "Mr.", label: "Mr." },
        { value: "Mrs.", label: "Mrs." },
        { value: "Ms.", label: "Ms." },
        { value: "Dr.", label: "Dr." },
        { value: "Prof.", label: "Prof." },
        { value: "Other", label: "Other" },
    ];

    initializeDropdown(title,userData.title,titleOptions)
    initializeDropdown(gender,userData.gender,genderOptions)
    
    function validateBasicForm() {
        let isValid = true;

        // Clear previous errors first
        document.querySelectorAll("#form_basic_details .p_error").forEach((errorElement) => {
            errorElement.textContent = "";
            errorElement.classList.add("hide");
        });

        document.querySelectorAll("#form_basic_details .error-border").forEach((field) => {
            field.classList.remove("error-border");
        });
        
        // Also clear error borders from all Choices.js containers
        document.querySelectorAll("#form_basic_details .choices").forEach((choices) => {
            choices.classList.remove("error-border");
        });

        // Validate required fields using the new validation functions
        if (!validateChoicesDropdown(title)) isValid = false;
        if (!validateRequiredField(firstName)) isValid = false;
        if (!validateRequiredField(lastName)) isValid = false;
        if (!validateChoicesDropdown(gender)) isValid = false;
        if (!validateRequiredField(dob)) isValid = false;
        
        // Validate training funded by radio button
        if (!trainingFundedBy || !trainingFundedBy.value.trim()) {
            showError(trainingFundedBy, "Required");
            isValid = false;
        }

        // Add age validation
        if (dob && dob.value.trim() !== "") {
            const dobParts = dob.value.split('/');
            if (dobParts.length === 3) {
                // Date format is d/m/Y
                const birthDate = new Date(dobParts[2], dobParts[1] - 1, dobParts[0]);
                const age = calculateAge(birthDate);
                
                if (age < 18) {
                    showError(dob, "You must be at least 18 years old.");
                    isValid = false;
                } else {
                    clearError(dob);
                }
            }
        }
        
        // Add LinkedIn URL validation if a value is provided
        if (linkedInUrl && linkedInUrl.value.trim() !== "") {
            // LinkedIn URL pattern - accepts linkedin.com/in/ format with various TLDs
            const linkedInPattern = /^(https?:\/\/)?(www\.)?linkedin\.com\/in\/[\w-]+\/?$/i;
            
            if (!linkedInPattern.test(linkedInUrl.value.trim())) {
                showError(linkedInUrl, "Please enter a valid LinkedIn URL");
                isValid = false;
            } else {
                clearError(linkedInUrl);
            }
        } else if (linkedInUrl) {
            clearError(linkedInUrl);
        }

        return isValid;
    }

    const basicDetailsFields = [
        title, 
        firstName, 
        middleName,
        lastName, 
        gender, 
        dob, 
        trainingFundedBy,
        profilePicture,
        linkedInUrl
    ];
    addInputChangeListeners(basicDetailsFields, basicSaveButton , basicdiscardButton)

    // change picture functionality    
    if (changePictureBtn && fileInput && profilePicture) {
        changePictureBtn.addEventListener("click", function (event) {
            event.preventDefault(); // Prevent default anchor behavior
            fileInput.click(); // Opens file selection dialog
        });
        
        fileInput.addEventListener("change", async function (event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    profilePictureImageElement.src = e.target.result; // Show selected image
                    
                    // Display file name above the change picture link
                    let filenameSpan = changePictureBtn.querySelector("span");
                    if (!filenameSpan) {
                        filenameSpan = document.createElement("span");
                        changePictureBtn.prepend(filenameSpan);
                    }
                    filenameSpan.textContent = file.name + "\n";
                    filenameSpan.style.display = "block";
                    
                    // Enable save buttons when file is selected
                    if (basicSaveButton && basicdiscardButton) {
                        toggleSaveButtonState(true, basicSaveButton, basicdiscardButton);
                    }
                    
                    // Show the delete button if it exists
                    if (deletePictureBtn) {
                        deletePictureBtn.style.display = "inline-block";
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Setup delete picture button if it exists
    if (deletePictureBtn) {
        deletePictureBtn.addEventListener("click", function(event) {
            event.preventDefault();
            // The actual delete functionality is handled by the AJAX implementation below
            // This event listener is removed to prevent conflicts
        });
    }
    
    // training fundd by radio button toggle functionality
    if(userData.training_funded_by){
        const selectedRadio = document.getElementById(userData.training_funded_by);

        if (selectedRadio) {
            selectedRadio.checked = true;
        }
    }

    basicdiscardButton.addEventListener("click", function () {
        restoreBasicDetailsFields();
        toggleSaveButtonState(false, basicSaveButton, basicdiscardButton);
        addInputChangeListeners(basicDetailsFields, basicSaveButton, basicdiscardButton);
        document.querySelectorAll(".p_error").forEach((errorElement) => {
            errorElement.textContent = "";
            errorElement.classList.add("hide");
        });
        
        // Reset form edit tracking
        window.isFormModified = false;
        window.isLeaving = true;
        window.tabFormState.basic = false;
        const index = window.formModified.indexOf('basic');
        if (index > -1) {
            window.formModified.splice(index, 1);
        }
    });

    // Add this new function to restore basic details fields to their original values
    function restoreBasicDetailsFields() {
        // Restore dropdown selections
        if (title && title.choicesInstance) {
            title.choicesInstance.setChoiceByValue(userData.title || "");
        }
        
        if (gender && gender.choicesInstance) {
            gender.choicesInstance.setChoiceByValue(userData.gender || "");
        }
        
        // Restore text inputs
        if (firstName) firstName.value = userData.first_name || "";
        if (middleName) middleName.value = userData.middle_name || "";
        if (lastName) lastName.value = userData.last_name || "";
        
        // Handle date of birth restoration - convert timestamp to formatted date
        if (dob) {
            // Check if dob is a timestamp (numeric value)
            if (userData.dob && !isNaN(userData.dob) && userData.dob.toString().length > 8) {
                // Convert timestamp to date in d/m/Y format
                const date = new Date(parseInt(userData.dob) * 1000); // Convert from seconds to milliseconds
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                const formattedDate = `${day}/${month}/${year}`;
                
                dob.value = formattedDate;
                console.log("Converted timestamp to formatted date:", formattedDate);
            } else {
                // Use the value as is if it's already a formatted date
                dob.value = userData.dob || "";
                console.log("Using date value as-is:", dob.value);
            }
        }
        
        // Explicitly restore LinkedIn URL - note the field is actually named linkedin_url in HBS template
        if (linkedInUrl) {
            // In HBS template, it's userData.linkedin_url not userData.user_linkedin_url
            linkedInUrl.value = userData.linkedin_url || "";
            console.log("Set LinkedIn URL to:", linkedInUrl.value);
        }
        
        // Restore profile picture and reset file-related elements
        if (profilePictureImageElement && userData.profile_pic_url) {
            profilePictureImageElement.src = userData.profile_pic_url;
            
            // Reset file input
            if (fileInput) fileInput.value = "";
            
            // Clear the filename span
            let filenameSpan = changePictureBtn.querySelector("span");
            if (filenameSpan) {
                filenameSpan.textContent = "";
                filenameSpan.style.display = "none";
            }
            
            // Show/hide delete button based on whether there's a profile pic
            if (deletePictureBtn) {
                deletePictureBtn.style.display = userData.profile_pic_url ? "inline-block" : "none";
            }
        }
        
        // Restore training funded by radio selection
        if (userData.training_funded_by) {
            const selectedRadio = document.getElementById(userData.training_funded_by);
            if (selectedRadio) {
                selectedRadio.checked = true;
            }
        }
    }

    basicSaveButton.addEventListener("click", function (event) {
        if (!validateBasicForm()) {
            event.preventDefault();
        } else {
            window.isLeaving = true; // Prevent leaving prompt
            window.tabFormState.basic = false; // Reset this tab's form state
        }
    });
    
    // ==========================================================================
    // BASIC DETAILS SECTION - END ABOVE
    // ==========================================================================
    

    // ==========================================================================
    // LEARNING OUTCOME SECTION - START
    // ==========================================================================
    
    const outcomeDropdown = document.getElementById("objective_taking_course");
    const saveOutcomeButton = document.getElementById("save-btn");
    const outcomeSelect = document.getElementById('objective_taking_course');

    // Outcomes data
    if (TaxonomyData?.objective_of_taking_course?.length) {
        // Populate Choices.js dropdowns 
        initializeChoicesDropdown(outcomeSelect, TaxonomyData?.objective_of_taking_course, selectedOutcomeId, false);
    }
    outcomeDropdown.addEventListener("change", function () {
        if (outcomeDropdown.value) {
            saveOutcomeButton.removeAttribute("disabled");
        } else {
            saveOutcomeButton.setAttribute("disabled", "true");
        }
    });

    // Add click event listener for learning outcome save button
    if (saveOutcomeButton) {
        saveOutcomeButton.addEventListener("click", function(event) {
            // Set leaving flag to prevent beforeunload confirmation dialog
            window.isLeaving = true;
            window.tabFormState.outcome = false; // Reset this tab's form state
        });
    }
    
    // ==========================================================================
    // LEARNING OUTCOME SECTION - END
    // ==========================================================================

    // ==========================================================================
    // VALIDATION FUNCTIONS & HELPERS
    // ==========================================================================
    
    function getMonthIndex(monthName) {
        const months = {
            'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
            'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
        };
        return months[monthName] ?? 0;
    }

    function validateProfessionalInputFields() {
        let isValid = true;

        // Clear previous errors
        document.querySelectorAll('#form_professional_info .p_error').forEach(err => {
            err.classList.add('hide');
            err.innerHTML = '';
        });
        document.querySelectorAll('#form_professional_info input, #form_professional_info select').forEach(input => {
            input.classList.remove('error-border');
        });
        document.querySelectorAll('#form_professional_info .choices__inner').forEach(choicesInner => {
            choicesInner.classList.remove('error-border');
        });
        document.querySelectorAll('.work_exp_err').forEach(err => {
            err.classList.add('hide');
            err.innerHTML = '';
        });

        // Process each work experience section
        document.querySelectorAll('.exp_main_wrap.professional').forEach(wrapper => {
            // Validate basic text inputs first
            const designationField = wrapper.querySelector('.company_designation');
            const companyField = wrapper.querySelector('.company_name');
            
            if (designationField && !validateRequiredField(designationField, true)) {
                isValid = false;
            }
            
            if (companyField && !validateRequiredField(companyField, true)) {
                isValid = false;
            }
            
            // Validate department dropdown - handle both pre-populated and dynamic
            const departmentField = wrapper.querySelector('.job_function_prepopulate') || wrapper.querySelector('.job_function');
            if (departmentField && !validateChoicesDropdown(departmentField, true)) {
                isValid = false;
            }
            
            // Validate industry dropdown - handle both pre-populated and dynamic  
            const industryField = wrapper.querySelector('.select_industry_prepopulate') || wrapper.querySelector('.select_industry') || wrapper.querySelector('.industry');
            if (industryField && !validateChoicesDropdown(industryField, true)) {
                isValid = false;
            }

            // Date validation
            const fromMonth = wrapper.querySelector('.exp_from_month');
            const fromYear = wrapper.querySelector('.exp_from_year');
            const toMonth = wrapper.querySelector('.exp_to_month');
            const toYear = wrapper.querySelector('.exp_to_year');
            const currentRoleCheckbox = wrapper.querySelector('.current_company');
            const isCurrentRole = currentRoleCheckbox?.checked;

            // Validate Start Date using validateChoicesDropdown for Choices.js dropdown fields
            if (fromMonth && !validateChoicesDropdown(fromMonth, true)) {
                isValid = false;
            }
            
            if (fromYear && !validateChoicesDropdown(fromYear, true)) {
                isValid = false;
            }

            // Validate End Date only if "Current Role" is not checked
            if (!isCurrentRole) {
                if (toMonth && !validateChoicesDropdown(toMonth, true)) {
                    isValid = false;
                }
                
                if (toYear && !validateChoicesDropdown(toYear, true)) {
                    isValid = false;
                }
            }

            // Date logic validation (if all date fields have valid values)
            if (fromMonth?.value && fromYear?.value && (isCurrentRole || (toMonth?.value && toYear?.value))) {
                const startDate = new Date(parseInt(fromYear.value), getMonthIndex(fromMonth.value), 1);
                const endDate = isCurrentRole ? null : new Date(parseInt(toYear.value), getMonthIndex(toMonth.value), 1);
                const today = new Date();

                // Check if Start Date is future
                if (startDate > today) {
                    showError(fromMonth, 'Please select a valid start date');
                    showError(fromYear, 'Please select a valid start date');
                    isValid = false;
                }

                if (!isCurrentRole && endDate) {
                    // Check if End Date is future
                    if (endDate > today) {
                        showError(toMonth, 'Please select a valid end date');
                        showError(toYear, 'Please select a valid end date');
                        isValid = false;
                    }

                    // Check Start Date before End Date
                    if (startDate >= endDate) {
                        const errorContainer = wrapper.querySelector('.work_exp_err');
                        if (errorContainer) {
                            errorContainer.classList.remove('hide');
                            errorContainer.innerHTML = 'Please select a valid company joining and relieving details.';
                            errorContainer.style.display = 'block'; // Force display to ensure visibility
                        }
                        isValid = false;

                        // Also highlight fields
                        showError(fromMonth, '');
                        showError(fromYear, '');
                        showError(toMonth, '');
                        showError(toYear, '');
                    }
                }
            }
        });

        return isValid;
    }

    function validateAcademicInputFields() {
        let isValid = true;

        // Clear previous errors
        document.querySelectorAll('#form_academics_info .p_error').forEach(err => {
            err.classList.add('hide');
            err.innerHTML = '';
        });
        document.querySelectorAll('#form_academics_info input, #form_academics_info select').forEach(input => {
            input.classList.remove('error-border');
        });
        document.querySelectorAll('#form_academics_info .choices').forEach(choices => {
            choices.classList.remove('error-border');
            const choicesInner = choices.querySelector('.choices__inner');
            if (choicesInner) {
                choicesInner.classList.remove('error-border');
            }
        });
        document.querySelectorAll('.academics_exp_err').forEach(err => {
            err.classList.add('hide');
            err.innerHTML = '';
        });

        // Validate each academic experience section
        document.querySelectorAll('.exp_main_wrap.academic').forEach(wrapper => {
            // Validate basic text inputs
            const instituteField = wrapper.querySelector('.institute_name');
            const specializationField = wrapper.querySelector('.field_specialization');
            
            if (!validateRequiredField(instituteField)) isValid = false;
            if (!validateRequiredField(specializationField)) isValid = false;

            // Validate qualification dropdown
            const qualificationField = wrapper.querySelector('.field_qualification');
            if (!validateChoicesDropdown(qualificationField)) isValid = false;
            
            // Validate date fields
            const fromMonth = wrapper.querySelector('.course_from_month');
            const fromYear = wrapper.querySelector('.course_from_year');
            const toMonth = wrapper.querySelector('.course_to_month');
            const toYear = wrapper.querySelector('.course_to_year');

            // Validate all date fields as required
            if (!validateChoicesDropdown(fromMonth)) isValid = false;
            if (!validateChoicesDropdown(fromYear)) isValid = false;
            if (!validateChoicesDropdown(toMonth)) isValid = false;
            if (!validateChoicesDropdown(toYear)) isValid = false;
            
            // Date logic validation
            if (fromMonth?.value && fromYear?.value && toMonth?.value && toYear?.value) {
                const startDate = new Date(parseInt(fromYear.value), getMonthIndex(fromMonth.value), 1);
                const endDate = new Date(parseInt(toYear.value), getMonthIndex(toMonth.value), 1);
                const today = new Date();

                // Check if Start Date is future
                if (startDate > today) {
                    showError(fromMonth, 'Please select a valid start date');
                    showError(fromYear, 'Please select a valid start date');
                    isValid = false;
                }

                // Start Date must be before End Date
                if (startDate >= endDate) {
                    const errorContainer = wrapper.querySelector('.academics_exp_err');
                    if (errorContainer) {
                        errorContainer.classList.remove('hide');
                        errorContainer.innerHTML = 'Please select a valid course start and end duration.';
                        errorContainer.style.display = 'block'; // Force display to ensure visibility
                    }
                    isValid = false;

                    // Highlight the date fields without showing individual error messages
                    showError(fromMonth, '');
                    showError(fromYear, '');
                    showError(toMonth, '');
                    showError(toYear, '');
                }
            }
        });

        return isValid;
    }

    // Add checks to event listeners for fields to prevent immediate error display
    document.querySelectorAll("input, select, textarea").forEach(function(field) {
        field.addEventListener("focus", function() {
            // Only clear error for text inputs, not for dropdowns
            if (this.tagName !== 'SELECT') {
                clearError(this);
            }
        });
        
        // Add input event to clear error messages as user types
        field.addEventListener("input", function() {
            // Clear error styling and messages as soon as user types anything
            if (this.value && this.value.trim() !== "") {
                clearError(this);
            }
        });
        
        // Also handle change events for dropdowns
        field.addEventListener("change", function() {
            // Clear error styling and messages on change
            if (this.value && this.value.trim() !== "") {
                clearError(this);
            }
        });
    });


    // Add specific event listener for Choices.js change events
    document.addEventListener('change', function(event) {
        // Check if the changed element is a select with Choices.js
        if (event.target.tagName === 'SELECT' && event.target.closest('.choices')) {
            // Clear error when a valid selection is made
            if (event.target.value && event.target.value.trim() !== "") {
                clearError(event.target);
            }
        }
        
        // Handle professional experience dropdowns specifically
        if (event.target.classList.contains('job_function_prepopulate') || 
            event.target.classList.contains('job_function') ||
            event.target.classList.contains('select_industry_prepopulate') ||
            event.target.classList.contains('select_industry') ||
            event.target.classList.contains('industry')) {
            
            // Clear error when a valid selection is made
            if (event.target.value && event.target.value.trim() !== "" && event.target.value !== "--Select--") {
                clearError(event.target);
            }
        }
    });

    // Add event delegation for handling company_designation and company_name inputs
    document.addEventListener("input", function(event) {
        // Check if the target is company_designation or company_name
        if (event.target.classList.contains('company_designation') || 
            event.target.classList.contains('company_name') ||
            event.target.classList.contains('institute_name') ||
            event.target.classList.contains('field_specialization')) {
            
            if (event.target.value.trim() !== "") {
                clearError(event.target);
            }
        }
    });

    // Add event delegation for handling specialized input types
    document.addEventListener("input", function(event) {
        // For any input with a value, clear any error styling
        if (event.target.value && event.target.value.trim() !== "") {
            clearError(event.target);
            
            // Also clear any section-specific error messages
            const expMainWrap = event.target.closest('.exp_main_wrap');
            if (expMainWrap && event.target.closest('.select_dates')) {
                const sectionError = expMainWrap.querySelector('.work_exp_err, .academics_exp_err');
                if (sectionError) {
                    sectionError.classList.add('hide');
                    sectionError.innerHTML = '';
                }
            }
        }
    });

    // Add an additional event listener specifically for academic date fields validation
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('save') && event.target.closest('#form_academics_info')) {
            // Apply validation to all course_to_year fields
            document.querySelectorAll('.course_to_year').forEach(field => {
                if (!field.value || field.value.trim() === '') {
                    // Use the standard showError function for consistency
                    showError(field, 'Required');
                }
            });
        }
    });

    // ==========================================================================
    // PROFILE PICTURE DELETE FUNCTIONALITY
    // ==========================================================================
    
    const deleteBtn = document.getElementById('delete_picture');

    if (deleteBtn) {
      deleteBtn.addEventListener('click', async function (event) {
        event.preventDefault();
        
        // Show confirmation dialog
        const confirmDelete = confirm("Are you sure, you want to delete your profile picture?");
        
        if (!confirmDelete) {
          return; // User cancelled, do nothing
        }
        
        try {
          const response = await fetch('/user/profile/delete-profile-picture', {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
              'Content-Type': 'application/json'
            }
          });
    
          const result = await response.json();

    
          if (response.ok && result.type === 'success') {
            const profilePics = document.querySelectorAll('.profile_picture');
                profilePics.forEach((img) => {
                img.src = 'https://cfaccounts.simplilearn.com/frontend/images/profile.png'
                });
            //  Update profile completion text and bar using the API response
            const completionPercent = result.profileCompletion || '0%';
            const numericValue = parseInt(completionPercent.replace('%', ''));
    
            const progressText = document.querySelector('.profile_progress_val');
            const progressBar = document.getElementById('profile_progress');
    
            if (progressText) {
              progressText.textContent = completionPercent;
            }
    
            if (progressBar) {
              progressBar.style.width = completionPercent;
              progressBar.setAttribute('aria-valuenow', numericValue.toString());
            }

            deleteBtn.style.display = 'none';
            
            // Show success message
            const successMessage = document.createElement('div');
            successMessage.textContent = 'Profile picture removed successfully.';
            successMessage.className = 'profile-picture-success';
            
            // Find the upload section and add the message
            const uploadSection = deleteBtn.closest('.exp-inner-wrap') || deleteBtn.parentElement;
            if (uploadSection) {
              uploadSection.appendChild(successMessage);
              
              // Remove the message after 5 seconds (handled by CSS animation)
              setTimeout(() => {
                if (successMessage && successMessage.parentElement) {
                  successMessage.parentElement.removeChild(successMessage);
                }
              }, 5000);
            }
    
          } else {
            console.log('Failed to delete profile picture.');
          }
        } catch (err) {
          console.log('Error deleting profile picture:', err);
        }
      });
    }

    // ==========================================================================
    // DROPDOWN INITIALIZATION ON PAGE LOAD
    // ==========================================================================
    
    // Add this function to ensure all dropdowns are properly initialized on page load
    function initializeAllDropdowns() {
        // Initialize professional dropdowns for existing work experiences
        document.querySelectorAll('.exp_main_wrap.professional').forEach((wrapper, index) => {
            initializeProfessionalDropdowns(index);
        });
        
        // Initialize academic dropdowns for existing academic experiences  
        document.querySelectorAll('.exp_main_wrap.academic').forEach((wrapper, index) => {
            initializeAcademicDropdowns(index);
        });
    }

    // Call this after all data is loaded
    setTimeout(() => {
        initializeAllDropdowns();
    }, 100);

    // Ensure validation works correctly after form submission
    function validateFormAfterSubmission() {
        // Re-initialize all dropdowns to ensure they work after page reload/form submission
        initializeAllDropdowns();
        
        // Ensure all required validation still works
        document.querySelectorAll('form').forEach(form => {
            const saveButton = form.querySelector('.btn.save');
            const discardButton = form.querySelector('.btn.discard');
            
            if (saveButton && discardButton) {
                // Re-enable form change detection
                const formFields = form.querySelectorAll('input, select, textarea');
                formFields.forEach(field => {
                    field.addEventListener('input', () => {
                        toggleSaveButtonState(true, saveButton, discardButton);
                    });
                    field.addEventListener('change', () => {
                        toggleSaveButtonState(true, saveButton, discardButton);
                    });
                });
            }
        });
    }

    // Call validation setup after DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(validateFormAfterSubmission, 200);
    });

    // Note: Removed forced clearing of country selection to preserve user data after save

    // Add specific event listener for Choices.js change events in professional section
    document.addEventListener('change', function(event) {
        // Check if the changed element is a select with Choices.js in professional section
        if (event.target.tagName === 'SELECT' && event.target.closest('#form_professional_info')) {
            // Clear error when a valid selection is made
            if (event.target.value && event.target.value.trim() !== "" && event.target.value !== "--Select--") {
                clearError(event.target);
            }
        }
        
        // Handle professional experience dropdowns specifically
        if (event.target.classList.contains('job_function_prepopulate') || 
            event.target.classList.contains('job_function') ||
            event.target.classList.contains('select_industry_prepopulate') ||
            event.target.classList.contains('select_industry') ||
            event.target.classList.contains('industry') ||
            event.target.classList.contains('exp_from_month') ||
            event.target.classList.contains('exp_from_year') ||
            event.target.classList.contains('exp_to_month') ||
            event.target.classList.contains('exp_to_year')) {
            
            // Clear error when a valid selection is made
            if (event.target.value && event.target.value.trim() !== "" && event.target.value !== "--Select--") {
                clearError(event.target);
            }
        }
        
        // Handle academic experience dropdowns specifically
        if (event.target.classList.contains('field_qualification_populate') ||
            event.target.classList.contains('field_qualification') ||
            event.target.classList.contains('course_from_month') ||
            event.target.classList.contains('course_from_year') ||
            event.target.classList.contains('course_to_month') ||
            event.target.classList.contains('course_to_year')) {
            
            // Clear error when a valid selection is made
            if (event.target.value && event.target.value.trim() !== "" && event.target.value !== "--Select--") {
                clearError(event.target);
            }
        }
    });

    // Helper function to validate only the non-end-date fields in a work experience section
    function validateOtherWorkExperienceFields(workExpSection) {
        if (!workExpSection) return;
        
        // Get all required fields except end date fields
        const fieldsToValidate = [
            workExpSection.querySelector('.company_designation'),
            workExpSection.querySelector('.company_name'),
            workExpSection.querySelector('.job_function, .job_function_prepopulate'),
            workExpSection.querySelector('.industry, .select_industry, .select_industry_prepopulate'),
            workExpSection.querySelector('.exp_from_month'),
            workExpSection.querySelector('.exp_from_year')
        ];
        
        // Validate each field and apply error styling if needed
        fieldsToValidate.forEach(field => {
            if (field && field.hasAttribute('required') && !field.disabled) {
                if (field.classList.contains('job_function') || 
                    field.classList.contains('job_function_prepopulate') ||
                    field.classList.contains('industry') || 
                    field.classList.contains('select_industry') ||
                    field.classList.contains('select_industry_prepopulate') ||
                    field.classList.contains('exp_from_month') ||
                    field.classList.contains('exp_from_year')) {
                    validateChoicesDropdown(field, true);
                } else {
                    validateRequiredField(field, true);
                }
            }
        });
    }

    function initializeProfessionalDropdowns(specificIndex = null) {
        // Determine the selector based on whether we're targeting a specific block
        const baseSelector = specificIndex !== null 
            ? `#exp_main_wrap_w_${specificIndex} ` 
            : '';
        
        // Initialize searchable dropdowns for both pre-populated and dynamic elements
        // For industry dropdowns - handle both pre-populated and dynamic
        document.querySelectorAll(`${baseSelector}.select_industry_prepopulate, ${baseSelector}.select_industry, ${baseSelector}.industry`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(select, TaxonomyData?.industry || [], null, true, false);
            }
        });
        
        // For department/job function dropdowns - handle both pre-populated and dynamic
        document.querySelectorAll(`${baseSelector}.job_function_prepopulate, ${baseSelector}.job_function`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(select, TaxonomyData?.job_function || [], null, true, false);
            }
        });
        
        // Initialize non-searchable dropdowns with natural order
        document.querySelectorAll(`${baseSelector}.exp_from_month, ${baseSelector}.exp_to_month`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(
                    select, 
                    months.map(({value, text}) => ({_id: value, name: text})), 
                    null, 
                    false, // No search
                    false, // No sorting
                    "Month"
                );
            }
        });
        
        document.querySelectorAll(`${baseSelector}.exp_from_year, ${baseSelector}.exp_to_year`).forEach(select => {
            if (!select.choicesInstance) {
                initializeChoicesDropdown(
                    select,
                    years,
                    null,
                    false, // No search
                    false, // No sorting
                    "Year"
                );
            }
        });
    }


    
    // ==========================================================================
    // FINALIZATION & CLEANUP
    // ==========================================================================
    
    // Reset initialization flag after all setup is complete
    setTimeout(() => {
        window.isInitializing = false;
        console.log('Form initialization complete - change detection enabled');
    }, 1000); // Give enough time for all async operations to complete


const confirmButton = document.getElementById("profile-download");

if (confirmButton) {
    confirmButton.addEventListener("click", async function () {
        const email = emailInput.value.trim();
        
        if (!email) {
            showToast("Please enter a valid email address.", "error");
            return;
        }

        try {
            const response = await fetch("/user/profile/export-profile", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (response.ok && data.success) {
                showToast(data.message || "Your request is being processed", "success");
                closeModalFunc();
            } else {
                throw new Error(data.message || "Something went wrong. Please try again.");
            }
        } catch (error) {
            showToast(error.message, "error");
        }
    });
}
});



// ==========================================================================
// GLOBAL FORM CHANGE TRACKING 
// ==========================================================================

document.querySelectorAll("form.track_changes input, form.track_changes select, form.track_changes textarea").forEach(function(input) {
    input.addEventListener("input", function() {
        // Don't track changes during initialization and ensure it's only set for profile forms
        if (!window.isInitializing && this.closest('.profile-wraper')) {
            window.isFormModified = true; // Changed from isFormEdited to window.isFormModified
            this.closest('form').querySelectorAll('.save, .discard').forEach(function(btn) {
                btn.removeAttribute('disabled');
            });
        }
    });
    input.addEventListener("change", function() {
        // Don't track changes during initialization and ensure it's only set for profile forms
        if (!window.isInitializing && this.closest('.profile-wraper')) {
            window.isFormModified = true; // Changed from isFormEdited to window.isFormModified
            this.closest('form').querySelectorAll('.save, .discard').forEach(function(btn) {
                btn.removeAttribute('disabled');
            });
        }
    });
});


