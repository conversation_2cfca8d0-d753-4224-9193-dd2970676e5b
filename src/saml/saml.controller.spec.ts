import { Test, TestingModule } from '@nestjs/testing';
import { SamlController } from './saml.controller';
import { SamlService } from './services/saml.service';
import { ConfigService } from '@nestjs/config';
import { Logger, APILog } from '../logging/logger';

jest.mock('../logging/logger', () => ({
  Logger: { log: jest.fn() },
  APILog: { error: jest.fn() },
}));

describe('SamlController', () => {
  let controller: SamlController;
  let samlService: jest.Mocked<SamlService>;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SamlController],
      providers: [
        {
          provide: SamlService,
          useValue: {
            generateServiceProviderMetadata: jest.fn(),
            acs: jest.fn(),
            mobileSuccess: jest.fn(),
            mobileUserAgent: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<SamlController>(SamlController);
    samlService = module.get(SamlService);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('spInitiatedSso', () => {
    it('should log SP initiated SSO', async () => {
      await controller.spInitiatedSso();
      expect(Logger.log).toHaveBeenCalledWith('SP initiated SSO');
    });
  });

  describe('spMetaData', () => {
    it('should return metadata xml', async () => {
      const req = { params: { titleValue: 'testTitle' } };
      const res = { type: jest.fn().mockReturnThis(), send: jest.fn() };
      samlService.generateServiceProviderMetadata.mockResolvedValue('<xml>data</xml>');

      await controller.spMetaData(req as any, res as any);

      expect(samlService.generateServiceProviderMetadata).toHaveBeenCalledWith('testTitle');
      expect(res.type).toHaveBeenCalledWith('application/xml');
      expect(res.send).toHaveBeenCalledWith('<xml>data</xml>');
    });

    it('should handle error and log', async () => {
      const req = { params: { titleValue: 'testTitle' } };
      const res = { type: jest.fn().mockReturnThis(), send: jest.fn(), status: jest.fn().mockReturnThis() };
      const error = new Error('fail');
      samlService.generateServiceProviderMetadata.mockRejectedValue(error);

      await controller.spMetaData(req as any, res as any);

      expect(APILog.error).toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith('Error generating metadata');
    });
  });

  describe('logout', () => {
    it('should logout and redirect', async () => {
      const req = { logout: jest.fn(), user: { id: 1 }, params: {} };
      const res = { redirect: jest.fn() };

      await controller.logout(req as any, res as any);

      expect(Logger.log).toHaveBeenCalledWith('SLS Action');
      expect(Logger.log).toHaveBeenCalledWith('SLS', req.user);
      expect(req.logout).toHaveBeenCalled();
      expect(res.redirect).toHaveBeenCalledWith('/auth/login');
    });

    it('should handle error and log', async () => {
      const req = { logout: jest.fn().mockImplementation(() => { throw new Error('fail'); }), user: {}, params: {} };
      const res = { redirect: jest.fn(), status: jest.fn().mockReturnThis(), send: jest.fn() };

      await controller.logout(req as any, res as any);

      expect(APILog.error).toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith('Error during logout');
    });
  });

  describe('acs', () => {
    it('should call samlService.acs', async () => {
      const req = { body: {}, params: {} };
      const res = {};
      samlService.acs.mockImplementation(async () => {});

      await controller.acs(req as any, res as any);

      expect(samlService.acs).toHaveBeenCalledWith(req, res);
    });

    it('should handle error and redirect', async () => {
      const req = { body: {}, params: {} };
      const res = { redirect: jest.fn() };
      samlService.acs.mockImplementation(() => { throw new Error('fail'); });

      await controller.acs(req as any, res as any);

      expect(APILog.error).toHaveBeenCalled();
      expect(res.redirect).toHaveBeenCalledWith('/saml/error');
    });
  });

  describe('proxyAcs', () => {
    it('should return response from samlService.acs', async () => {
      const req = { body: {}, params: {} };
      const res = {};
      samlService.acs.mockResolvedValue({ foo: 'bar' });

      const result = await controller.proxyAcs(req as any, res as any);

      expect(samlService.acs).toHaveBeenCalledWith(req, res, 'json');
      expect(result).toEqual({ foo: 'bar' });
    });

    it('should handle error and redirect', async () => {
      const req = { body: {}, params: {} };
      const res = { redirect: jest.fn() };
      samlService.acs.mockRejectedValue(new Error('fail'));

      await controller.proxyAcs(req as any, res as any);

      expect(APILog.error).toHaveBeenCalled();
      expect(res.redirect).toHaveBeenCalledWith('/saml/error');
    });
  });

  describe('spInitiatedSlsAction', () => {
    it('should logout and redirect', async () => {
      const req = { logout: jest.fn(), user: { id: 1 }, params: {} };
      const res = { redirect: jest.fn() };

      await controller.spInitiatedSlsAction(req as any, res as any);

      expect(Logger.log).toHaveBeenCalledWith('sp initiated sls', req.user);
      expect(req.logout).toHaveBeenCalled();
      expect(res.redirect).toHaveBeenCalledWith('/auth/login');
    });

    it('should handle error and log', async () => {
      const req = { logout: jest.fn().mockImplementation(() => { throw new Error('fail'); }), user: {}, params: {} };
      const res = { redirect: jest.fn(), status: jest.fn().mockReturnThis(), send: jest.fn() };

      await controller.spInitiatedSlsAction(req as any, res as any);

      expect(APILog.error).toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith('Error during SP initiated SLS');
    });
  });

  describe('error', () => {
    it('should return error page data', async () => {
      configService.get.mockReturnValue('https://test.site');
      const req = {};
      const res = {};

      const result = await controller.error(req as any, res as any);

      expect(Logger.log).toHaveBeenCalled();
      expect(result).toEqual({ ICE9_SITE_URL: 'https://test.site' });
    });

    it('should handle error and log', async () => {
      configService.get.mockImplementation(() => { throw new Error('fail'); });
      const req = {};
      const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };

      await controller.error(req as any, res as any);

      expect(APILog.error).toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.send).toHaveBeenCalledWith('Error rendering error page');
    });
  });



  describe('mobileError', () => {
    it('should return mobile error data', async () => {
      const req = {
        get: jest.fn().mockReturnValue('user-agent'),
        params: {},
      };
      samlService.mobileUserAgent.mockReturnValue({ isMobile: true, isIos: false, isAndroid: true });

      const result = await controller.mobileError(req as any);

      expect(samlService.mobileUserAgent).toHaveBeenCalledWith('user-agent');
      expect(result).toEqual({
        data: JSON.stringify({
          error: { msg: 'Error occurred' },
          status: -1,
          code: 400,
        }),
        msg: 'error',
        isiOS: false,
        isAndroidOS: true,
      });
      expect(Logger.log).toHaveBeenCalledWith('mobile error', result);
    });

    it('should handle error and log', async () => {
      const req = {
        get: jest.fn().mockReturnValue('user-agent'),
        params: {},
      };
      samlService.mobileUserAgent.mockImplementation(() => { throw new Error('fail'); });

      await expect(controller.mobileError(req as any)).rejects.toThrow('fail');
      expect(APILog.error).toHaveBeenCalled();
    });
  });
});