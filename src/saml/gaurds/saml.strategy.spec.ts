import { Test, TestingModule } from '@nestjs/testing';
import { SamlStrategy } from './saml.strategy';
import { SamlService } from '../services/saml.service';

// Create a mock for the SamlConfigurationService
const mockSamlConfigService = {
  getConfiguration: jest.fn(),
  findUserBySamlProfile: jest.fn(),
};

describe('SamlStrategy', () => {
  let samlStrategy: SamlStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SamlStrategy,
        {
          provide: SamlService,
          useValue: mockSamlConfigService, // Provide the mock service
        },
      ],
    }).compile();

    samlStrategy = module.get<SamlStrategy>(SamlStrategy);
  });

  it('should be defined', () => {
    expect(samlStrategy).toBeDefined();
  });
});
