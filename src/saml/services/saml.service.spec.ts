
import { Test, TestingModule } from '@nestjs/testing';
import { SamlService } from './saml.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { GroupSettings } from '../../db/mysql/entity/group-settings.entity';
import { SamlLogs } from '../../db/mysql/entity/saml-logs.entity';
import { CustomFieldLabelMaster } from '../../db/mysql/entity/custom_field_label_master.entity';
import { CustomFieldUserValue } from '../../db/mysql/entity/custom_field_user_value.entity';
import { HelperService } from '../../helper/helper.service';
import { BadRequestException } from '@nestjs/common';
import { SAML } from '@node-saml/passport-saml';

describe('SamlService', () => {
  let service: SamlService;
  let mockGroupSettingRepo;
  let mockSamlLogsRepo;
  let mockCustomFieldLabelRepo;
  let mockCustomFieldUserValueRepo;
  let mockConfigService;
  let mockHelperService;

  beforeEach(async () => {
    mockGroupSettingRepo = {
      findOne: jest.fn(),
      find: jest.fn(),
      save: jest.fn(),
      update: jest.fn()
    };

    mockSamlLogsRepo = {
      save: jest.fn(),
      update: jest.fn()
    };

    mockCustomFieldLabelRepo = {
      find: jest.fn()
    };

    mockCustomFieldUserValueRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
      update: jest.fn()
    };

    mockConfigService = {
      get: jest.fn()
    };

    mockHelperService = {
      get: jest.fn(),
      getHelper: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SamlService,
        {
          provide: getRepositoryToken(GroupSettings),
          useValue: mockGroupSettingRepo
        },
        {
          provide: getRepositoryToken(SamlLogs),
          useValue: mockSamlLogsRepo
        },
        {
          provide: getRepositoryToken(CustomFieldLabelMaster), 
          useValue: mockCustomFieldLabelRepo
        },
        {
          provide: getRepositoryToken(CustomFieldUserValue),
          useValue: mockCustomFieldUserValueRepo
        },
        {
          provide: ConfigService,
          useValue: mockConfigService
        },
        {
          provide: HelperService,
          useValue: mockHelperService
        }
      ],
    }).compile();

    service = module.get<SamlService>(SamlService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getConfiguration', () => {
    it('should get SAML configuration for a title', async () => {
      const title = 'test-title';
      const gid = 1;
      
      mockGroupSettingRepo.findOne.mockResolvedValue({
        gid
      });

      mockGroupSettingRepo.find.mockResolvedValue([
        {
          identifier: 'idp_metadata',
          value: JSON.stringify({
            idp: {
              sso_url: 'http://sso.test',
              entity_id: 'test-entity'
            }
          })
        },
        {
          identifier: 'certificate',
          value: 'test-cert'
        }
      ]);

      mockConfigService.get.mockImplementation((key) => {
        const config = {
          saml: {
            callbackUrl: 'http://callback/',
            logoutCallbackUrl: 'http://logout/',
            samlAppKeySet: 'test'
          },
          issuer: 'test-issuer'
        };
        return config[key];
      });

      const result = await service.getConfiguration(title);

      expect(result).toEqual({
        saml: {
          path: 'http://callback/test-title',
          callbackUrl: 'http://callback/test-title',
          logoutCallbackUrl: 'http://logout/test-title',
          entryPoint: 'http://sso.test',
          idpIssuer: 'test-entity',
          issuer: 'test-issuer',
          logoutUrl: 'http://sso.test',
          idpCert: 'test-cert'
        },
        gid: 1
      });
    });
  });

  describe('generateServiceProviderMetadata', () => {
    it('should generate SP metadata', async () => {
      const title = 'test';
     

      jest.spyOn(service, 'getConfiguration').mockResolvedValue({
        saml: {
          path: 'test',
          callbackUrl: 'callbackUrl',
          entryPoint: 'entryPoint',
          issuer: 'issuer',
          logoutCallbackUrl: 'logoutCallbackUrl',
          logoutUrl: 'logoutUrl',
          idpIssuer: 'idpIssuer',
          idpCert: 'idpCert'
        },
        gid: 1
      });

      jest.spyOn(SAML.prototype, 'generateServiceProviderMetadata')
        .mockReturnValue('metadata');

      const result = await service.generateServiceProviderMetadata(title);
      expect(result).toBe('metadata');
    });
  });

  describe('saveSamlLogs', () => {
    it('should save SAML logs', async () => {
      const req = {
        query: {},
        body: {}
      };

      const data = {
        gid: 1,
        title: 'test'
      };

      mockSamlLogsRepo.save.mockResolvedValue({
        id: 1
      });

      const result = await service.saveSamlLogs(req, data);

      expect(result).toEqual({
        id: 1
      });

      expect(mockSamlLogsRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          gid: 1,
          title: 'test',
          email: '',
          attributes: '',
          get_params: '{}',
          post_params: '{}'
        })
      );
    });
  });

  describe('mobileUserAgent', () => {
    it('should detect mobile user agents', () => {
      const iosAgent = 'SimplilearnAppIOS';
      const androidAgent = 'SimplilearnAppAndroid';
      const webAgent = 'Mozilla/5.0';

      expect(service.mobileUserAgent(iosAgent)).toEqual({
        isMobile: true,
        isIos: true,
        isAndroid: false
      });

      expect(service.mobileUserAgent(androidAgent)).toEqual({
        isMobile: true,
        isIos: false, 
        isAndroid: true
      });

      expect(service.mobileUserAgent(webAgent)).toEqual({
        isMobile: false,
        isIos: false,
        isAndroid: false
      });
    });
  });

  describe('acs', () => {
    it('should throw error if email is missing', async () => {
      const req = {
        user: {},
        body: {},
        params: {
          titleValue: 'test'
        },
        get: jest.fn()
      };

      const res = {};

      await expect(service.acs(req, res)).rejects.toThrow(BadRequestException);
    });
  });

});