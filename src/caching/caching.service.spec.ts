import { Test, TestingModule } from '@nestjs/testing';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { CachingService } from './caching.service';
import { Cache } from 'cache-manager';

class CachingServiceMock {
  set = jest.fn();
  get = jest.fn();
  del = jest.fn();
}

describe('CachingService', () => {
  let service: CachingService;
  let cm: Cache;

  beforeEach(async () => {
    //let CacheMock  = await caching('memory', {type: });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: CACHE_MANAGER,
          useValue: {},
        },
        {
          provide: CachingService,
          useClass: CachingService,
        },
      ],
    })
      .overrideProvider(CACHE_MANAGER)
      .useValue({
        set: jest.fn(),
        get: jest.fn(),
        del: jest.fn(),
      })
      .compile();

    service = module.get<CachingService>(CachingService);
    cm = module.get(CACHE_MANAGER);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('Set Cache', () => {
    const spy = jest.spyOn(cm, 'set');
    expect.assertions(2);

    service.set<string>('some_key', 'some_data');
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith('some_key', 'some_data', undefined);
  });
  it('Set Cache - With TTL', () => {
    const spy = jest.spyOn(cm, 'set');

    expect.assertions(2);

    service.set<string>('some_key', 'some_data', 5000);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith('some_key', 'some_data', 5000);
  });
  it('Get Cache - for item in cache', async () => {
    const expected = 'some_data_1';
    const spy = jest.spyOn(cm, 'get');
    spy.mockResolvedValue('some_data_1');

    expect.assertions(3);

    const result = await service.get<string>('some_key');
    expect(spy).toHaveBeenCalledWith('some_key');
    expect(spy).toHaveBeenCalledTimes(1);
    expect(result).toEqual(expected);
  });

  it('Get Cache - for item not in cache', async () => {
    const expected = undefined;
    const spy = jest.spyOn(cm, 'get');
    spy.mockResolvedValue(undefined);

    expect.assertions(3);

    const result = await service.get<string>('some_key');
    expect(spy).toHaveBeenCalledWith('some_key');
    expect(spy).toHaveBeenCalledTimes(1);
    expect(result).toEqual(expected);
  });

  it('Delete Cache', () => {
    const spy = jest.spyOn(cm, 'del');
    service.del('some_key');
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith('some_key');
  });
});
