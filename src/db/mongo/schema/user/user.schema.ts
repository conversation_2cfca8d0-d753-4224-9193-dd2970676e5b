import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { WorkExperience } from './work.experience.schema';
import { Academics } from './academics.schema';
import { SocialUrl } from './social.url.schema';
import { Role } from '../roles/role.schema';
import { Schema as MongooseSchema, SchemaTypes, Types } from 'mongoose';
import { UserSocial } from './social.schema';
import { USERCATEGORY, UserType } from '../../../../common/typeDef/auth.type';
import { ProfilePhoto } from './profile.pic.schema';
import { Taxonomies } from '../taxonomies/taxonomies.schema';
export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  @Prop({ index: true })
  uid: number;

  @Prop({ required: true, index: true })
  readonly email: string;

  @Prop([{ type: MongooseSchema.Types.ObjectId, ref: 'Role' }])
  roles: Role[];

  @Prop()
  readonly password: string;

  @Prop()
  readonly display_name: string;

  @Prop()
  readonly name: string;

  @Prop()
  readonly first_name: string;

  @Prop()
  readonly last_name: string;

  @Prop()
  readonly country_code: string;

  @Prop()
  readonly phone_no: string;

  @Prop()
  readonly gender: string;

  @Prop({ type: SchemaTypes.Mixed }) // Accepts both string & number
  readonly dob: string | number;

  @Prop()
  readonly location: string;

  @Prop()
  readonly timezone: string;

  @Prop({ type: SocialUrl })
  urls?: SocialUrl;

  @Prop()
  readonly work_experience: WorkExperience[];

  @Prop()
  readonly academics: Academics[];

  @Prop([{ type: MongooseSchema.Types.ObjectId, ref: 'Taxonomies' }])
  readonly interests: Taxonomies[];

  @Prop()
  readonly objective_taking_course: string;

  @Prop()
  readonly training_funded: string;

  @Prop()
  readonly degree_name: string;

  // Matched with cloud6
  @Prop()
  readonly user_career_type: string;

  @Prop()
  readonly profile_visibility: string;

  @Prop()
  readonly newsletter: number;

  // Matched to cloud6
  @Prop()
  readonly profile_pic: ProfilePhoto;

  @Prop()
  readonly signature: number;

  @Prop()
  readonly accept_agreement: boolean;

  @Prop()
  readonly user_options: number;

  @Prop()
  readonly sso_attributes: string;

  @Prop()
  readonly account_setup: number;

  @Prop({})
  readonly password_created: number;

  @Prop({})
  readonly user_type: UserType;

  @Prop({})
  readonly user_category: USERCATEGORY;

  @Prop({ type: Date })
  readonly login: Date;

  @Prop({ type: Date })
  readonly access: Date;

  @Prop()
  readonly status: number;

  @Prop({})
  readonly language: string;

  @Prop()
  readonly user_groups: number[];

  @Prop({})
  user_social_data: UserSocial[];

  @Prop()
  total_work_experience: string;

  @Prop()
  training_funded_by: string;

  @Prop()
  where_are_you_in_career: string;

  @Prop()
  linkedin_status: number;

  @Prop()
  updated_on: number;

  @Prop()
  middle_name: string;

  @Prop()
  title: string;

  @Prop()
  state: string;

  @Prop()
  correspondence_address: string;

  @Prop()
  highest_level_of_education: Types.ObjectId;

  @Prop()
  country_of_residence: string;

  @Prop()
  linkedin_url: string;

  @Prop()
  created: number;  
}

export const UserSchema = SchemaFactory.createForClass(User);
