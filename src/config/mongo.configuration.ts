export default () => ({
  defaultdb: process.env.<PERSON>ON<PERSON><PERSON>_DB_NAME,
  url: `${process.env.MONGO_ATLAS_URI}`,
  maxPoolSize: parseInt(process.env.MONGO_MAX_POOL_SIZE) || 100,
  maxTimeout: parseInt(process.env.MONOGO_CONN_TIMEOUT) || 1000,
  kmsSettings: {
    provider: process.env.MONGO_KMS_PROVIDER,
    kmsVaultDB: process.env.MONGO_KMS_KEY_VAULT_DB,
    keys: {
      token:
        process.env.MONGO_KMS_PROVIDER == 'local'
          ? process.env.MONGO_SCHEMA_ECRYPTION_KEY
          : process.env.MONGO_SCHEMA_ENCRYPTION_KEY,
    },
  },
});
