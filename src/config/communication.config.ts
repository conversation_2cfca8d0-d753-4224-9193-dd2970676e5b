export default () => ({
  cloud6Url: process.env.CLOUD6_URL,
  enterpriseUrl: process.env.ENTERPRISE_SERVICE_URL,
  deviantsUrl: process.env.DEVIANTS_URL_HTTP,
  ic9SiteUrl: process.env.ICE9_SITE_URL,
  ice9ApiUrl: process.env.ICE9_SIMPLEX_ENDPOINT,
  accountSetupUrl: process.env.ACCOUNT_SETUP_URL,
  tribeAPIEndpoint: process.env.TRIBE_API_ENDPOINT,
  communityBaseUrl: process.env.COMMUNITY_BASE_URL,
  emailCommunicationUrl: process.env.EMAIL_COMMUNICATION_URL,
  communicationUsername: process.env.COMMUNICATION_USERNAME,
  communicationPassword: process.env.COMMUNICATION_PASSWORD,
  communityAtpUserGroupId: 8,
  communityDefaultUserGroupId: 2,
  freemiumAssignmentBlockRedirectUrl: process.env.FREEMIUM_ASSIGNMENT_BLOCK_REDIRECT_URL,
  freemiumAssignmentRedirectUrl: process.env.FREEMIUM_ASSIGNMENT_REFIRECT_URL,
  paperclipApiEndpoint: process.env.PAPERCLIP_ENTERPRISE_API_ENDPOINT,
  dataChangeLogApiUrl: process.env.DATA_CHANGE_LOG_API_URL,
  enableDataChangeLog: process.env.ENABLE_DATA_CHANGE_LOG,
  tribeCommunityBaseUrl: process.env.TRIBE_COMMUNITY_BASE_URL,
  tribePrivateKey: process.env.TRIBE_PRIVATE_KEY,
  tribeApiNetworkId: process.env.TRIBE_API_NETWORKID,
  tribeEncSecretKey: process.env.TRIBE_SECRET_KEY,
  tribeEncSecretIv: process.env.TRIBE_SECRET_IV,
  tribeAPIEndpointWithoutCredentials: process.env.TRIBE_API_ENDPOINT_WITHOUT_CREDENTIAL,
  skillUpSimpliLogo: 'https://www.simplilearn.com/ice9/assets/SkillUp-by-Simplilearn-logo-2.png',
  credentialsForTribe: process.env.TRIBE_CREDENTIALS,
  kafkaUrl: process.env.KAFKA_URL,
  lrsApiUrl: process.env.LRS_API_URL,
  topicPrefix: process.env.TOPIC_PREFIX,
  defaultAppId: 2,
  defaultAppName: 'cloud6',
  lrsUserSecret: 'lrssecret',
  lrsTtl: 30,
  lrsEnabled: 1,
  allowedTopics: [
    '_lrs_rest.login',
    '_rest.learners',
    '_lrs_rest.cert',
    '_lrs_rest.visit',
    process.env.KAFKA_PROFILE_TOPIC,
  ],
  couch_salt: process.env.COUCH_SALT,
  sync_cookie: 'sync_ref_earn_link_',
  // TODO : verify if the below implementation topic.
  profileTopic: process.env.KAFKA_PROFILE_TOPIC,
  futurex_title: 'FutureX',
  b2b_vendor_title: ['dryrun'],
  b2b_without_sso_template: 'newB2bLearnerWithoutSsoAccountSetupEmail',
  account_setup_template: 'newLearnerAccountSetupEmail',
  b2b_with_sso_course: 'b2bLearnerWithSsoCoursePurchaseConfirmationEmail',
  existing_b2b_without_sso_course: 'existingB2bLearnerWithoutSsoCoursePurchaseConfirmationEmail',
  existing_learner_template: 'existingLearnerCoursePurchaseEmail',
  digitalKeyUrl: process.env.CLOUD6_URL + '/user/profile/digital-key',
  examVoucherUrl: process.env.CLOUD6_URL + '/user/profile/exam-voucher',
  managePaymentsUrl: process.env.CLOUD6_URL + '/user/profile/manage-payments',
  freeTrialUrl: process.env.CLOUD6_URL + '/user/profile/free-trial',
  tickets: process.env.CLOUD6_URL + '/user/profile/tickets',
});
