import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { EmailService } from './email.service';
import { Logger } from '../../../logging/logger';

describe('EmailService', () => {
  let emailService: EmailService;
  let configService: ConfigService;
  let jwtService: JwtService;

  beforeEach(() => {
    configService = new ConfigService();
    jwtService = new JwtService({ secret: 'your-jwt-secret' });
    emailService = new EmailService(configService, jwtService);
  });

  describe('SendEmailMicroService', () => {
    it('should return the response', async () => {
      const response = {
        enterpriseList: [],
        learnerCourses: [],
      };
      // Mock the post method to return the expected response
      // Mock the post method to return a resolved Promise
      const postSpy = jest.spyOn(emailService as any, 'post').mockResolvedValue(response);

      const reqObj = {
        email: '<EMAIL>',
        id: '1001099',
        name: 'test client',
      };
      const result = await emailService.sendEmail(reqObj);
      expect(result).toEqual(response);
    });

    it('should log and return the error', async () => {
      const error = { msg: 'Please provide email.', type: 'error' };
      // Mock the post method to throw an error
      const postSpy = jest.spyOn(emailService as any, 'post').mockRejectedValue(error);

      const reqObj = {
        email: '',
        id: '1001099',
        name: 'test client',
      };
      try {
        const response = await emailService.sendEmail(reqObj);
        // If the catch block doesn't throw an error, fail the test
        expect(response).toBeUndefined();
      } catch (error: any) {
        // Verify that the post method was called with the correct path
        expect(postSpy).toHaveBeenCalledWith('/email-template/send-email-api');
        // Verify that the error is an instance of Error and has the expected message
      }
    });
  });
  describe('resetUri', () => {
    it('should reset the URI correctly', () => {
      const emailCommunicationUrl = 'http://example.com/email';
      configService.get = jest.fn(() => emailCommunicationUrl);

      emailService.resetUri();

      expect(emailService.url).toBe(emailCommunicationUrl);
    });
  });
});
