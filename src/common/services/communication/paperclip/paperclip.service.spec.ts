import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PaperclipService } from './paperclip.service';
import { BadRequestException } from '@nestjs/common';

describe('Cloud6Service', () => {
  let service: PaperclipService;

  // Mock ConfigService and CryptoHelper
  const configServiceMock = {
    get: jest.fn().mockReturnValue('your-config-value'),
  };

  const cryptoHelperMock = {
    createHmac: jest.fn().mockReturnValue('your-hmac-value'),
  };
  const loggerMock = {
    error: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaperclipService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: cryptoHelperMock,
        },
        {
          provide: 'Logger', // Assuming 'Logger' is the injection token for your logger
          useValue: loggerMock,
        },
      ],
    }).compile();

    service = module.get<PaperclipService>(PaperclipService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should call setCloud6AuthorizationHeader', () => {
    // Mock a timestamp value (e.g., **********)
    const mockTimestamp = **********;

    // Mock ConfigService and CryptoHelper methods
    configServiceMock.get.mockReturnValue('your-config-value');
    cryptoHelperMock.createHmac.mockImplementation((timestamp, secretSalt, algo, encoding) => {
      expect(typeof timestamp).toBe('string'); // Ensure it's a number
      expect(secretSalt).toBe('your-config-value');
      expect(algo).toBe('your-config-value');
      expect(encoding).toBe('your-config-value');
      return 'your-hmac-value';
    });

    // Call the method you want to test
    service['setPaperclipAuthorizationHeader']();

    // Assert that the methods were called with the expected values
    expect(configServiceMock.get).toHaveBeenCalledWith('paperclipApiAuthSecretSalt');
    expect(cryptoHelperMock.createHmac).toHaveBeenCalledWith(
      expect.any(String), // Ensure it's a numeric timestamp
      'your-config-value',
      'your-config-value',
      'your-config-value',
    );
    // You can add more assertions based on your specific use case
  });

  it('should reset the URL correctly when a public method calls resetUri', () => {
    configServiceMock.get.mockReturnValue('paperclipApiEndpoint');
    service['_resetUri']();
    expect(service['url']).toBe('paperclipApiEndpoint');
  });

  it('should get the assigned B2C courses count', async () => {
    // Mock the methods used in getAssignedB2cCoursesCount
    // Mock the methods used in getAssignedB2cCoursesCount
    const resetUriSpy = jest.spyOn(service as any, '_resetUri').mockImplementation();
    const setPaperclipAuthorizationHeaderSpy = jest
      .spyOn(service as any, 'setPaperclipAuthorizationHeader')
      .mockImplementation();
    const setFormDataSpy = jest.spyOn(service as any, 'setFormData').mockImplementation();
    // Define a data object for testing
    const data = { userId: 'your-user-id' };

    // Mock the post method to return a resolved Promise
    const postSpy = jest.spyOn(service as any, 'post').mockResolvedValue({ status: 'success', count: 42 });

    // Call the method you want to test
    const result = await service.getAssignedB2cCoursesCount(data);

    // Verify that the methods were called with the expected values
    expect(resetUriSpy).toHaveBeenCalled();
    expect(setPaperclipAuthorizationHeaderSpy).toHaveBeenCalled();
    expect(setFormDataSpy).toHaveBeenCalledWith({
      userId: {
        userId: 'your-user-id',
      },
    });
    expect(postSpy).toHaveBeenCalledWith('get-assigned-courses');

    // Verify the result
    expect(result).toEqual({ status: 'success', count: 42 });

    // You can add more assertions based on your specific use case
  });

  it('should call getSkillupReferralRewardInfo', async () => {
    // Mock a timestamp value (e.g., **********)
    const mockTimestamp = **********;

    // Mock ConfigService methods
    configServiceMock.get.mockReturnValue('your-config-value');

    // Mock the methods used in getSkillupReferralRewardInfo
    const resetUriSpy = jest.spyOn(service as any, '_resetUri').mockImplementation();
    const setPaperclipAuthorizationHeaderSpy = jest
      .spyOn(service as any, 'setPaperclipAuthorizationHeader')
      .mockImplementation();
    const setFormDataSpy = jest.spyOn(service as any, 'setFormData').mockImplementation();

    // Mock the post method and return a resolved Promise
    const postSpy = jest.spyOn(service as any, 'post').mockResolvedValue('your-response-data');

    // Define parameters for testing
    const params = { user_id: 'your-user-id' };

    // Call the getSkillupReferralRewardInfo method
    service.getSkillupReferralRewardInfo(params);

    // Verify that the methods were called with the expected values
    expect(resetUriSpy).toHaveBeenCalled();
    expect(setPaperclipAuthorizationHeaderSpy).toHaveBeenCalled();
    expect(setFormDataSpy).toHaveBeenCalledWith(params);
    expect(postSpy).toHaveBeenCalledWith('get-skillup-referral-reward-info');

    // You can add more assertions based on your specific use case
  });

  it('should call saveSkillupReferral', async () => {
    // Mock ConfigService methods
    configServiceMock.get.mockReturnValue('your-config-value');

    // Mock the methods used in saveSkillupReferral
    const resetUriSpy = jest.spyOn(service as any, '_resetUri').mockImplementation();
    const setPaperclipAuthorizationHeaderSpy = jest
      .spyOn(service as any, 'setPaperclipAuthorizationHeader')
      .mockImplementation();
    const setFormDataSpy = jest.spyOn(service as any, 'setFormData').mockImplementation();

    // Mock the post method and return a resolved Promise
    const postSpy = jest.spyOn(service as any, 'post').mockResolvedValue('your-response-data');

    // Define data for testing
    const data = {
      user_id: 123, // Your user ID
      email: '<EMAIL>',
      first_name: 'John',
      refcode: 'your-refcode',
    };

    // Call the saveSkillupReferral method
    const response = await service.saveSkillupReferral(data);

    // Verify that the methods were called with the expected values
    expect(resetUriSpy).toHaveBeenCalled();
    expect(setPaperclipAuthorizationHeaderSpy).toHaveBeenCalled();
    expect(setFormDataSpy).toHaveBeenCalledWith(data);
    expect(postSpy).toHaveBeenCalledWith('save-skillup-referral');

    // You can add more assertions based on your specific use case
  });

  it('should handle a string result', async () => {
    // Define a sample string result
    const sampleResult = 'Sample string result';

    // Call the cleanupResult method with the sample string result
    const result = await service.cleanupResult(sampleResult);

    // Verify that the result matches the expected behavior for a string
    expect(result).toEqual(sampleResult);
    // Verify that Logger.error was not called since it's not an error case
    expect(loggerMock.error).not.toHaveBeenCalled();
  });

  it('should handle a JSON result', async () => {
    // Define a sample JSON object
    const sampleResult = { status: 'success', msg: 'Sample message' };

    // Call the cleanupResult method with the JSON result
    const result = await service.cleanupResult(sampleResult);

    // Verify that the result matches the expected behavior for a JSON object
    expect(result).toEqual(sampleResult);
    // Verify that Logger.error was not called since it's not an error case
    expect(loggerMock.error).not.toHaveBeenCalled();
  });

  it('should handle a string result with JSON object', async () => {
    // Define a sample string result containing JSON objects
    const sampleResult = 'Sample string result\n{"status": "success", "msg": "Sample message"}\nAdditional line';

    // Call the cleanupResult method with the sample string result
    const result = await service.cleanupResult(sampleResult);

    // Verify that the result matches the expected behavior for a JSON object
    expect(result).toEqual({ status: 'success', msg: 'Sample message' });
    // Verify that Logger.error was not called since it's not an error case
    expect(loggerMock.error).not.toHaveBeenCalled();
  });

  it('should handle errors in the catch block', async () => {
    // Mock ConfigService methods
    configServiceMock.get.mockReturnValue('your-config-value');

    // Define a sample error
    const sampleError = new Error('Sample error message');

    // Call the method you want to test and pass an error (mocked behavior)
    const params = { user_id: 'your-user-id' };
    await service.getSkillupReferralRewardInfo(params).catch((error) => {
      // Verify that the Logger.error method was called with the expected parameters
      expect(loggerMock.error).toHaveBeenCalledWith('getSkillupReferralRewardInfo', {
        METHOD: service.constructor.name + '@' + service.getSkillupReferralRewardInfo.name,
        MESSAGE: sampleError.message,
        REQUEST: params,
        RESPONSE: sampleError.stack,
        TIMESTAMP: expect.any(Number), // Ensure it's a timestamp
      });
    });
  });
  it('should handle errors in the catch block', async () => {
    // Mock ConfigService methods
    configServiceMock.get.mockReturnValue('your-config-value');

    // Define a sample error
    const sampleError = new Error('Sample error message');

    // Define the data parameter
    const data = { userId: 'your-user-id' };

    // Call the method you want to test and pass an error (mocked behavior)
    await service.getAssignedB2cCoursesCount(data).catch((error) => {
      // Verify that the Logger.error method was called with the expected parameters
      expect(loggerMock.error).toHaveBeenCalledWith('getAssignedB2cCoursesCount', {
        METHOD: service.constructor.name + '@' + service.getAssignedB2cCoursesCount.name,
        MESSAGE: sampleError.message,
        REQUEST: { userId: data },
        RESPONSE: sampleError.stack,
        TIMESTAMP: expect.any(Number), // Ensure it's a timestamp
      });
    });
  });

  it('should handle errors in the catch block', async () => {
    // Mock ConfigService methods
    configServiceMock.get.mockReturnValue('your-config-value');

    // Define a sample error
    const sampleError = new Error('Sample error message');

    // Define the data parameter
    const data = {
      user_id: 123,
      email: '<EMAIL>',
      first_name: 'John',
      refcode: 'ref123',
    };

    // Call the method you want to test and pass an error (mocked behavior)
    await service.saveSkillupReferral(data).catch((error) => {
      // Verify that the Logger.error method was called with the expected parameters
      expect(loggerMock.error).toHaveBeenCalledWith('saveSkillupReferral', {
        METHOD: service.constructor.name + '@' + service.saveSkillupReferral.name,
        MESSAGE: sampleError.message,
        REQUEST: { data },
        RESPONSE: sampleError.stack,
        TIMESTAMP: expect.any(Number), // Ensure it's a timestamp
      });
    });
  });

  // Write similar tests for other methods as needed
  it('should be defined', () => {
    jest.spyOn(service as any, 'getSkillupUserInfo');
    jest.spyOn(service as any, 'cleanupResult');

    expect(service).toBeDefined();
    expect(service['cleanupResult']).toBeDefined();
    expect(service['getSkillupUserInfo']).toBeDefined();
  });

  it('should return cleaned result on success', async () => {
    jest.spyOn(service as any, 'getSkillupUserInfo').mockResolvedValue({ status: 'success', code : 200, data: { user: 'test-user' } });
    jest.spyOn(service as any, 'cleanupResult');

    const result = await service.getSkillupUserInfo({ userId: '123' });
    expect(result).toEqual({ status: 'success', code : 200 ,data: { user: 'test-user' } });
    expect(service['getSkillupUserInfo']).toHaveBeenCalled();
  });

  it('should log error and return undefined on failure', async () => {
    jest.spyOn(service as any, 'post').mockRejectedValue(new Error('Request failed'));
    jest.spyOn(service as any, 'getSkillupUserInfo');
    jest.spyOn(service as any, 'cleanupResult');
    
    const result = await service.getSkillupUserInfo({ userId: '123' });
    
    expect(result).toBeUndefined();
    expect(service['getSkillupUserInfo']).toHaveBeenCalled();
  });
});