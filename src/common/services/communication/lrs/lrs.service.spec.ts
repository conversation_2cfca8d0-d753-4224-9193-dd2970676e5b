import { Test, TestingModule } from '@nestjs/testing';
import { LrsService } from './lrs.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../../logging/logger';

describe('LrsService', () => {
  let lrsService: LrsService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LrsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        // {
        //   provide: Logger,
        //   useValue: mockLogger,
        // },
      ],
    }).compile();

    lrsService = module.get<LrsService>(LrsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(lrsService).toBeDefined();
  });

  it('should send LRS data successfully', async () => {
    const mockPayload = {
      verb: 'register',
      objectType: 'accounts',
      objectId: '1004029',
      dataVals: '{"client_id":"sl_looper"}',
      applicationId: 2,
      applicationName: 'cloud6',
      userId: 'anonymous_userid',
      userEmail: 'anonymous_email',
      userName: '',
      firstName: 'XXXXXX',
      lastName: 'XXXXXX',
      timezone: 'America/Chicago',
      userRole: '{"2":"authenticated user","59":"looper_student"}',
      signature: '74c5ffce294eda9d89d393c4058348aa',
      eventTime: *************,
      ip: '127.0.0.1',
      referalUrl: '',
      url: 'localhost-cli',
      userAgent: 'localhost-cli',
      from_server: 'true',
      ttl: *************,
    };
    mockConfigService.get.mockReturnValue('http://lrsapi.example.com');
    // Mock the post method of the Communication class
    const postSpy = jest.spyOn(lrsService as any, 'post').mockResolvedValue('LRS data sent successfully');

    const response = await lrsService.sendLrsData(mockPayload);

    expect(response).toEqual('LRS data sent successfully');
    // expect(Logger.log).toHaveBeenCalledWith('LRS sent successfully', { data: response });
  });

  it('should handle errors when sending LRS data', async () => {
    const mockPayload = {
      verb: 'register',
      objectType: 'accounts',
      objectId: '1004029',
      dataVals: '{"client_id":"sl_looper"}',
      applicationId: 2,
      applicationName: 'cloud6',
      userId: 'anonymous_userid',
      userEmail: 'anonymous_email',
      userName: '',
      firstName: 'XXXXXX',
      lastName: 'XXXXXX',
      timezone: 'America/Chicago',
      userRole: '{"2":"authenticated user","59":"looper_student"}',
      // signature: '74c5ffce294eda9d89d393c4058348aa',
      eventTime: *************,
      ip: '127.0.0.1',
      referalUrl: '',
      url: 'localhost-cli',
      userAgent: 'localhost-cli',
      from_server: 'true',
      ttl: *************,
    };
    mockConfigService.get.mockReturnValue('http://lrsapi.example.com');
    // Mock the post method of the Communication class to throw an error

    const postSpy = jest.spyOn(lrsService as any, 'post').mockRejectedValue(new Error('Failed to send data to LRS'));

    const response = await lrsService.sendLrsData(mockPayload);

    expect(response instanceof Error).toBe(true);
    // expect(Logger.error).toHaveBeenCalledWith('Failed to send to LRS', { error: response });
  });

  // Add more test cases to cover other scenarios and edge cases.
});
