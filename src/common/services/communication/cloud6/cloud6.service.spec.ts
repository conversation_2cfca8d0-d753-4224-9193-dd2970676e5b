import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Cloud6Service } from './cloud6.service';
import { Logger } from '../../../../logging/logger';
import { CryptoHelper } from '../../../../helper/helper.crypto';

jest.mock('../../../../logging/logger');
jest.mock('../communication');

describe('Cloud6Service', () => {
  let service: Cloud6Service;
  let configService: ConfigService;
  let cryptoHelper: CryptoHelper;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    const mockCryptoHelper = {
      createHmac: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        Cloud6Service,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: mockCryptoHelper,
        },
      ],
    }).compile();

    service = module.get<Cloud6Service>(Cloud6Service);
    configService = module.get<ConfigService>(ConfigService);
    cryptoHelper = module.get<CryptoHelper>('CRYPTO_HELPER');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserEnterpriseList', () => {
    it('should fetch user enterprise list successfully', async () => {
      const mockResponse = { data: 'mockData' };
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.getUserEnterpriseList({ key: 'value' });

      expect(service['post']).toHaveBeenCalledWith('/get-user-enterprise-list-course');
      expect(result).toEqual(mockResponse);
    });

    it('should log error and throw when fetching user enterprise list fails', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(service as any, 'post').mockRejectedValue(mockError);

      await expect(service.getUserEnterpriseList({ key: 'value' })).rejects.toThrow('Network error');
      expect(Logger.error).toHaveBeenCalledWith('getUserEnterpriseList', expect.any(Object));
    });
  });

  describe('getLmsEnterpriseSettings', () => {
    it('should fetch LMS enterprise settings successfully', async () => {
      const mockResponse = { data: 'mockData' };
      jest.spyOn(service as any, 'get').mockResolvedValue(mockResponse);

      const result = await service.getLmsEnterpriseSettings({ key: 'value' });

      expect(service['get']).toHaveBeenCalledWith('get-enterprise-lms-settings-for-gid');
      expect(result).toEqual(mockResponse);
    });

    it('should log error and return error object when fetching LMS enterprise settings fails', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(service as any, 'get').mockRejectedValue(mockError);

      const result = await service.getLmsEnterpriseSettings({ key: 'value' });

      expect(Logger.error).toHaveBeenCalledWith('getLmsEnterpriseSettings', {
      METHOD: expect.stringContaining('Cloud6Service@getLmsEnterpriseSettings'),
      MESSAGE: mockError.message,
      REQUEST: { reqObj: { key: 'value' } },
      RESPONSE: mockError.stack,
      TIMESTAMP: expect.any(Number),
      });
      expect(result).toEqual(mockError);
    });
  });

  describe('sendWelcomeEmailToManager', () => {
    it('should send welcome email successfully', async () => {
      const mockResponse = { data: 'mockData' };
      jest.spyOn(service as any, 'get').mockResolvedValue(mockResponse);

      const result = await service.sendWelcomeEmailToManager({ email: '<EMAIL>' });

      expect(service['get']).toHaveBeenCalledWith('send-welcome-email-to-manager');
      expect(result).toEqual(mockResponse);
    });

    it('should log error and throw when sending welcome email fails', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(service as any, 'get').mockRejectedValue(mockError);

      await expect(service.sendWelcomeEmailToManager({ email: '<EMAIL>' })).rejects.toThrow('Network error');
      expect(Logger.error).toHaveBeenCalledWith('sendWelcomeEmailToManager', expect.any(Object));
    });
  });

  describe('setCloud6AuthorizationHeader', () => {
    it('should set authorization header correctly', () => {
      const mockTimestamp = Math.round(new Date().getTime() / 1000);
      const mockAuthorization = 'mockAuthorization';
      jest.spyOn(global.Date, 'now').mockReturnValue(mockTimestamp * 1000);
      (cryptoHelper.createHmac as jest.Mock).mockReturnValue(mockAuthorization);
      (configService.get as jest.Mock)
      .mockReturnValueOnce('mockSalt')
      .mockReturnValueOnce('mockAlgo')
      .mockReturnValueOnce('mockEncoding');

      service['setCloud6AuthorizationHeader']();

      expect(cryptoHelper.createHmac).toHaveBeenCalledWith(
      mockTimestamp.toString(),
      'mockSalt',
      'mockAlgo',
      'mockEncoding',
      );
      expect(service['setRequestHeaders']).toHaveBeenCalledWith({
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: mockAuthorization,
      timestamp: mockTimestamp,
      });
    });
  });
});