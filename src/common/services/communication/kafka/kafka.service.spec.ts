import { Test, TestingModule } from '@nestjs/testing';
import { KafkaService } from './kafka.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../../logging/logger';
import { Communication } from '../communication';

jest.mock('../communication'); // Mock Communication class

describe('KafkaService', () => {
  let kafkaService: KafkaService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KafkaService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    kafkaService = module.get<KafkaService>(KafkaService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(kafkaService).toBeDefined();
  });

  describe('setUri', () => {
    it('should set the URL property', () => {
      // Mock the configuration values
      mockConfigService.get.mockReturnValue('http://kafka.example.com');

      // Invoke the setUri method
      kafkaService['setUri']();

      // Verify that the URL is set correctly
      expect(kafkaService['url']).toBe('http://kafka.example.com/topics/');
    });

    it('should throw an error if the Uri is invalid', () => {
      // Mock the configuration values
      mockConfigService.get.mockReturnValue(undefined);

      // Invoke the setUri method and expect it to throw an error
      expect(() => kafkaService['setUri']()).toThrowError('Invalid Uri');
    });
  });

  describe('publish', () => {
    it('should publish data to Kafka successfully', async () => {
      // Mock the configuration values
      mockConfigService.get.mockReturnValue(['topic1', 'topic2']);
      mockConfigService.get.mockReturnValue('http://kafka.example.com');
      mockConfigService.get.mockReturnValue('topicPrefix');

      // Mock the post method of the Communication class
      const postSpy = jest.spyOn(kafkaService as any, 'post').mockResolvedValue(true);

      // Test data
      const topic = 'topic1';
      const payload = { key: 'value' };
      const key = 'fakeKey';

      const response = await kafkaService.publish(topic, payload, key);

      // Verify that the post method was called with the correct arguments
      // expect(postSpy).toHaveBeenCalled();

      // Verify the response
      // expect(response).toEqual(true);
    });

    it('should handle errors when publishing data to Kafka', async () => {
      // Mock the configuration values
      mockConfigService.get.mockReturnValue(['topic1', 'topic2']);
      mockConfigService.get.mockReturnValue('http://kafka.example.com');

      // Mock the post method of the Communication class to throw an error
      const postSpy = jest
        .spyOn(kafkaService as any, 'post')
        .mockRejectedValue(new Error('Failed to send data to Kafka'));

      // Test data
      const topic = 'invalidTopic'; // This will trigger the Logger.error branch
      const payload = { key: 'value' };
      const key = 'fakeKey';

      const response = await kafkaService.publish(topic, payload, key);

      // Verify the response
      // expect(response instanceof Error).toBe(true);
    });

    it('should handle invalid topic name and log an error', () => {
      // Mock the configuration values
      mockConfigService.get.mockReturnValue(['topic1', 'topic2']);
      mockConfigService.get.mockReturnValue('http://kafka.example.com');
      mockConfigService.get.mockReturnValue('topicPrefix');

      // Test data
      const topic = 'invalidTopic';
      const payload = { key: 'value' };
      const key = 'fakeKey';

      // Spy on Logger.error method
      const errorSpy = jest.spyOn(Logger, 'error');

      // Invoke the publish method and expect it to log an error
      kafkaService.publish(topic, payload, key);

      // Verify that the Logger.error method was called with the correct arguments
      expect(errorSpy).toHaveBeenCalledWith('Invalid topic name', { topic: 'invalidTopic' });
    });

    it('should handle errors when sending data to Kafka and log an error', async () => {
      // Mock the configuration values
      mockConfigService.get.mockReturnValue(['topic1', 'topic2']);
      mockConfigService.get.mockReturnValue('http://kafka.example.com');
      mockConfigService.get.mockReturnValue('topicPrefix');

      // Mock the post method of the Communication class to throw an error
      const postSpy = jest
        .spyOn(kafkaService as any, 'post')
        .mockRejectedValue(new Error('Failed to send data to Kafka'));

      // Test data
      const topic = 'topic1';
      const payload = { key: 'value' };
      const key = 'fakeKey';

      // Spy on Logger.error method
      const errorSpy = jest.spyOn(Logger, 'error');

      await kafkaService.publish(topic, payload, key);

      // Verify that the Logger.error method was called with the correct arguments
      expect(errorSpy).toHaveBeenCalledWith('Invalid topic name');
    });

    // Add more test cases to cover other scenarios and edge cases.
  });
});
