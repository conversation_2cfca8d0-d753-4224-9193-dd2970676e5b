import { Test, TestingModule } from '@nestjs/testing';
import { CDPEventListener } from './helper.cdp.listener';
import { Webengage } from './helper.cdp.webengage';
import { Logger } from '../../logging/logger';

// Mocking the Segment and Amplitude classes
class WebengageMock {
  identify = jest.fn();
  track = jest.fn();
}

describe('CDPEventListener', () => {
  let cdpEventListener: CDPEventListener;
  let webengageB2C: WebengageMock;
  let webengageB2B: WebengageMock;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CDPEventListener,

        { provide: 'WEBENGAGE_B2C', useClass: WebengageMock },
        { provide: 'WEBENGAGE_B2B', useClass: WebengageMock },
      ],
    }).compile();

    cdpEventListener = module.get<CDPEventListener>(CDPEventListener);
    webengageB2C = module.get<WebengageMock>('WEBENGAGE_B2C');
    webengageB2B = module.get<WebengageMock>('WEBENGAGE_B2B');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(cdpEventListener).toBeDefined();
  });

  // it('should call segment.identify and segment.track for user.login event', async () => {
  //   const params = { gid: 2, email: '<EMAIL>' };
  //   await cdpEventListener.onLogin(params);

  //   expect(webengageB2C.identify).toHaveBeenCalledWith(params.email, {});
  //   expect(webengageB2B.track).toHaveBeenCalledWith(params.email, 'sentinel_event', { test_event: 'from sentinel' });

  //   expect(webengageB2B.identify).not.toHaveBeenCalled();
  //   expect(webengageB2B.track).not.toHaveBeenCalled();
  // });

  it('should call segment.identify and segment.track for user.impersonate event', async () => {
    const params = { gid: 1, email: '<EMAIL>' };
    await cdpEventListener.onLogin(params);

    expect(webengageB2B.identify).toHaveBeenCalledWith(params.email, {});
    expect(webengageB2B.track).toHaveBeenCalledWith(params.email, 'sentinel_event', { test_event: 'from sentinel' });

    expect(webengageB2C.identify).not.toHaveBeenCalled();
    expect(webengageB2C.track).not.toHaveBeenCalled();
  });

  it('should call segment.identify and segment.track for user.signup event', async () => {
    const params = { gid: 1, email: '<EMAIL>' };
    await cdpEventListener.onSignup(params);

    expect(webengageB2B.identify).toHaveBeenCalledWith(params.email, {});
    expect(webengageB2B.track).toHaveBeenCalledWith(params.email, 'sentinel_event', {
      test_event: 'from sentinel signup',
    });

    expect(webengageB2C.identify).not.toHaveBeenCalled();
    expect(webengageB2C.track).not.toHaveBeenCalled();
  });
});
