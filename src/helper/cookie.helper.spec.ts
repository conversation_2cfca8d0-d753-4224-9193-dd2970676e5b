import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../helper/helper.service';
import { AuthTokenHelper } from '../auth/helper/auth.tokenhelper';
import { <PERSON><PERSON>Helper } from '../helper/cookie.helper';
import { Logger } from '../logging/logger';
import { UserMgmtCommunityService } from '../user/services/communication/usermgmt.community.service';
import { Utility } from './../common/util/utility';
import { ResponseCookieType } from '../common/typeDef/auth.type';

describe('CookieHelper', () => {
  let cookieHelper: CookieHelper;
  let configServiceMock: ConfigService;
  let helperServiceMock: HelperService;
  let authTokenHelperMock: AuthTokenHelper;
  let userMgmtCommunityServiceMock: UserMgmtCommunityService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CookieHelper,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: HelperService,
          useValue: {
            getHelper: jest.fn(),
            get: jest.fn(),
          },
        },
        {
          provide: AuthTokenHelper,
          useValue: {
            createSignedToken: jest.fn(),
          },
        },
        {
          provide: UserMgmtCommunityService,
          useValue: {
            getTribeSSOLink: jest.fn(),
          },
        },
      ],
    }).compile();

    cookieHelper = module.get<CookieHelper>(CookieHelper);
    configServiceMock = module.get<ConfigService>(ConfigService);
    helperServiceMock = module.get<HelperService>(HelperService);
    authTokenHelperMock = module.get<AuthTokenHelper>(AuthTokenHelper);
    userMgmtCommunityServiceMock = module.get<UserMgmtCommunityService>(UserMgmtCommunityService);
  });

  describe('setCookie', () => {
    it('should set a cookie with default options', async () => {
      // Mock dependencies as needed
      const res = { cookie: jest.fn() };
      configServiceMock.get = jest.fn().mockReturnValue('example.com');

      await cookieHelper.setCookie(res, 'testCookie', 'cookieValue');

      // Add assertions for the expected behavior
      expect(res.cookie).toHaveBeenCalledWith(
        'testCookie',
        'cookieValue',
        expect.objectContaining({
          maxAge: expect.any(Date),
          path: '/',
          domain: 'example.com',
          sameSite: 'Strict',
        }),
      );
    });
  });

  describe('clearCookie', () => {
    it('should clear a cookie with the correct options', () => {
      // Mock dependencies as needed
      const res = {
        clearCookie: jest.fn(),
      };
      configServiceMock.get = jest.fn().mockReturnValue('example.com');

      // Call the method
      cookieHelper.clearCookie(res, 'testCookie');

      // Add assertions for the expected behavior
      expect(res.clearCookie).toHaveBeenCalledWith('testCookie', {
        httpOnly: true,
        domain: 'example.com',
      });
    });
  });

  describe('getCalendarCookie', () => {
    it('should set a calendar cookie with the correct options and return the calendarUrl', async () => {
      // Mock dependencies as needed
      const res = {
        cookie: jest.fn(),
      };

      const calendarUrl = 'https://example.com/calendar';
      const encodedUrl = encodeURIComponent(calendarUrl);
      configServiceMock.get = jest.fn().mockReturnValue('calendarRedirect');
      const expectedCalendarUrl = configServiceMock.get('calendarRedirect') + encodedUrl;

      // configServiceMock.get=jest.fn().mockReturnValueOnce(3600000); // Mock cookieMaxAge

      const result = await cookieHelper.getCalendarCookie(calendarUrl, res);

      // Add assertions for the expected behavior
      expect(configServiceMock.get).toHaveBeenCalledWith('calendarUrl');
      expect(configServiceMock.get).toHaveBeenCalledWith('calendarRedirect');
      expect(configServiceMock.get).toHaveBeenCalledWith('cookieMaxAge');

      expect(result).toEqual(expectedCalendarUrl);
    });

    it('should handle errors and log them', async () => {
      // Mock dependencies as needed
      const res = {
        cookie: jest.fn(),
      };

      // Mock an error scenario
      jest.spyOn(cookieHelper, 'setCookie').mockRejectedValueOnce(new Error('Test error'));

      const calendarUrl = 'https://example.com/calendar';

      await expect(cookieHelper.getCalendarCookie(calendarUrl, res)).resolves.toBeUndefined();
    });
  });
  describe('setCalenderCookie', () => {
    it('should set a calendar cookie with the correct options when calendarUrl is provided', async () => {
      // Mock dependencies as needed
      const res = {
        cookie: jest.fn(),
      };

      const calendarUrl = 'https://example.com/calendar';
      const expectedCalendarUrl = calendarUrl.trim();
      configServiceMock.get = jest.fn().mockReturnValueOnce('calendarRedirect');

      await cookieHelper.setCalenderCookie(calendarUrl, res);

      // Add assertions for the expected behavior
      expect(configServiceMock.get).toHaveBeenCalledWith('calendarRedirect');
      expect(res.cookie).toHaveBeenCalledWith(
        'calendarRedirect',
        expectedCalendarUrl,
        expect.objectContaining({
          maxAge: expect.any(Date),
          path: '/',
          sameSite: 'Strict',
        }),
      );
    });

    it('should not set a calendar cookie when calendarUrl is not provided', async () => {
      // Mock dependencies as needed
      const res = {
        cookie: jest.fn(),
      };

      const calendarUrl = '';

      await cookieHelper.setCalenderCookie(calendarUrl, res);

      // Add assertions for the expected behavior
      expect(configServiceMock.get).not.toHaveBeenCalled(); // Ensure configService.get is not called
      expect(res.cookie).not.toHaveBeenCalled(); // Ensure res.cookie is not called
    });
  });

  describe('setFermiumCookie', () => {
    it('should set a fermium cookie with the correct options when assignmentToken is provided', async () => {
      // Mock dependencies as needed
      const res = {
        cookie: jest.fn(),
      };

      const assignmentToken = 'fermiumToken';
      configServiceMock.get('freemiumAssignmentToken') + assignmentToken.trim();
      configServiceMock.get = jest.fn().mockReturnValueOnce('freemiumAssignmentToken');

      await cookieHelper.setFermiumCookie(assignmentToken, res);

      // Add assertions for the expected behavior
      expect(configServiceMock.get).toHaveBeenCalledWith('freemiumAssignmentToken');
      expect(res.cookie).toHaveBeenCalledWith(
        'freemiumAssignmentToken',
        assignmentToken,
        expect.objectContaining({
          maxAge: expect.any(Date),
          path: '/',
          sameSite: 'Strict',
        }),
      );
    });

    it('should not set a fermium cookie when assignmentToken is not provided', async () => {
      // Mock dependencies as needed
      const res = {
        cookie: jest.fn(),
      };

      const assignmentToken = '';

      await cookieHelper.setFermiumCookie(assignmentToken, res);

      // Add assertions for the expected behavior
      expect(configServiceMock.get).not.toHaveBeenCalled(); // Ensure configService.get is not called
      expect(res.cookie).not.toHaveBeenCalled(); // Ensure res.cookie is not called
    });
  });

  describe('setNpsCookie', () => {
    it('should set NPS cookie with valid redirectUrl', async () => {
      // Mock dependencies
      const res = { cookie: jest.fn() };
      const redirectUrl = 'https://example.com/nps';
      const jwtSecret = 'fakeJwtSecret';
      const authTokenHelperMock = {
        createSignedToken: jest.fn().mockResolvedValueOnce('fakeToken'),
      };

      // Mock config service to return the JWT secret
      configServiceMock.get = jest.fn().mockReturnValueOnce(jwtSecret);

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
        return null;
      });

      // Call the method
      await cookieHelper.setNpsCookie(redirectUrl, res);

      // Assertions
      // expect(authTokenHelperMock.createSignedToken).toHaveBeenCalledWith(
      //   { url: decodeURIComponent(redirectUrl) },
      //   { secret: jwtSecret }
      // );
      // expect(res.cookie).toHaveBeenCalledWith(
      //   'npsRedirectCookie',
      //   'fakeToken',
      //   expect.objectContaining({
      //     expires: expect.any(Date),
      //   }),
      // );
    });

    it('should not set NPS cookie with empty redirectUrl', async () => {
      const res = { cookie: jest.fn() };
      const redirectUrl = '';

      // Call the method
      await cookieHelper.setNpsCookie(redirectUrl, res);

      // Assertions
      expect(helperServiceMock.get).not.toHaveBeenCalled();
      expect(configServiceMock.get).not.toHaveBeenCalled();
      expect(authTokenHelperMock.createSignedToken).not.toHaveBeenCalled();
      expect(res.cookie).not.toHaveBeenCalled();
    });

    it('should not set NPS cookie with invalid redirectUrl', async () => {
      const res = { cookie: jest.fn() };
      const redirectUrl = 'invalid-url';

      // Call the method
      await cookieHelper.setNpsCookie(redirectUrl, res);

      // Assertions
      expect(helperServiceMock.get).not.toHaveBeenCalled();
      expect(configServiceMock.get).not.toHaveBeenCalled();
      expect(authTokenHelperMock.createSignedToken).not.toHaveBeenCalled();
      expect(res.cookie).not.toHaveBeenCalled();
    });

    describe('setCommunityCookie', () => {
      it('should set community cookie with valid redirectUrl', async () => {
        // Mock dependencies
        const res = { cookie: jest.fn() };
        const redirectUrl = 'https://example.com/community';
        const jwtSecret = 'fakeJwtSecret';

        // Mock config service to return the JWT secret
        configServiceMock.get = jest.fn().mockReturnValueOnce(jwtSecret);
        const authTokenHelperMock = {
          createSignedToken: jest.fn().mockResolvedValueOnce('fakeToken'),
        };

        jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
          if (helperName === AuthTokenHelper) {
            return Promise.resolve(authTokenHelperMock);
          }
          return null;
        });

        // Mock Utility methods
        Utility.isEmpty = jest.fn().mockReturnValueOnce(false);
        Utility.validateCommunityUrl = jest.fn().mockReturnValueOnce(true);
        Utility.isTribeCommunityUrl = jest.fn().mockReturnValueOnce(false);

        // Call the method
        await cookieHelper.setCommunityCookie(redirectUrl, res);

        // Assertions
        expect(helperServiceMock.get).toHaveBeenCalledWith(AuthTokenHelper);
        expect(configServiceMock.get).toHaveBeenCalledWith('jwtSecret');
      });

      it('should set tribe community cookie with valid redirectUrl', async () => {
        // Mock dependencies
        const res = { cookie: jest.fn() };
        const redirectUrl = 'https://example.com/tribe/community';
        const jwtSecret = 'fakeJwtSecret';
        configServiceMock.get = jest.fn().mockReturnValueOnce(jwtSecret);
        const authTokenHelperMock = {
          createSignedToken: jest.fn().mockResolvedValueOnce('fakeToken'),
        };

        // Mock config service to return the JWT secret

        jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
          if (helperName === AuthTokenHelper) {
            return Promise.resolve(authTokenHelperMock);
          }
          return null;
        });

        // Mock Utility methods
        Utility.isEmpty = jest.fn().mockReturnValueOnce(false);
        Utility.validateCommunityUrl = jest.fn().mockReturnValueOnce(true);
        Utility.isTribeCommunityUrl = jest.fn().mockReturnValueOnce(true);

        // Call the method
        await cookieHelper.setCommunityCookie(redirectUrl, res);

        // Assertions
        expect(helperServiceMock.get).toHaveBeenCalledWith(AuthTokenHelper);
        expect(configServiceMock.get).toHaveBeenCalledWith('jwtSecret');
      });

      it('should not set community cookie with empty redirectUrl', async () => {
        const res = { cookie: jest.fn() };
        const redirectUrl = '';

        // Call the method
        await cookieHelper.setCommunityCookie(redirectUrl, res);

        // Assertions
        expect(authTokenHelperMock.createSignedToken).not.toHaveBeenCalled();
        expect(res.cookie).not.toHaveBeenCalled();
      });

      it('should not set community cookie with invalid redirectUrl', async () => {
        const res = { cookie: jest.fn() };
        const redirectUrl = 'invalid-url';

        // Mock Utility methods
        Utility.isEmpty = jest.fn().mockReturnValueOnce(false);
        Utility.validateCommunityUrl = jest.fn().mockReturnValueOnce(false);

        // Call the method
        await cookieHelper.setCommunityCookie(redirectUrl, res);

        // Assertions
        expect(authTokenHelperMock.createSignedToken).not.toHaveBeenCalled();
        expect(res.cookie).not.toHaveBeenCalled();
      });

      it('should handle errors and log them', async () => {
        // Mock dependencies
        const res = { cookie: jest.fn() };
        const redirectUrl = 'https://example.com/community';

        // Mock an error scenario
        jest.spyOn(cookieHelper, 'setCookie').mockRejectedValueOnce(new Error('Test error'));

        // Mock Utility methods
        Utility.isEmpty = jest.fn().mockReturnValueOnce(false);
        Utility.validateCommunityUrl = jest.fn().mockReturnValueOnce(true);

        // Call the method
        await cookieHelper.setCommunityCookie(redirectUrl, res);

        // Assertions
        // Mock the Logger
        const loggerErrorSpy = jest.spyOn(Logger, 'error');
        // expect(loggerErrorSpy).toHaveBeenCalled();
        // expect(loggerErrorSpy.mock.calls[0][0]).toEqual('setCommunityCookie');
      });
    });
  });

  describe('setBulkCookie', () => {
    it('should set multiple cookies with the correct options', async () => {
      // Mock dependencies as needed
      const res = { cookie: jest.fn() };
      const data: ResponseCookieType[] = [
        { name: 'cookie1', value: 'value1' },
        { name: 'cookie2', value: 'value2' },
      ];

      configServiceMock.get = jest.fn().mockReturnValue('example.com');

      await cookieHelper.setBulkCookie(res, data, { maxAge: 3600000 });

      // Add assertions for the expected behavior
      expect(res.cookie).toHaveBeenCalledWith(
        'example.com',
        'value1',
        expect.objectContaining({
          maxAge: expect.any(Date),
          path: '/',
          domain: 'example.com',
          sameSite: 'Strict',
        }),
      );

      expect(res.cookie).toHaveBeenCalledWith(
        'example.com',
        'value2',
        expect.objectContaining({
          maxAge: expect.any(Date),
          path: '/',
          domain: 'example.com',
          sameSite: 'Strict',
        }),
      );
    });
  });

  describe('clearBulkCookie', () => {
    it('should clear multiple cookies', () => {
      // Mock dependencies as needed
      const res = { clearCookie: jest.fn() };
      const data: string[] = ['cookie1', 'cookie2'];

      configServiceMock.get = jest.fn().mockReturnValue('example.com');

      // Call the method
      cookieHelper.clearBulkCookie(res, data);

      // Add assertions for the expected behavior
      expect(res.clearCookie).toHaveBeenCalledWith('cookie1', {
        httpOnly: true,
        domain: 'example.com',
      });

      expect(res.clearCookie).toHaveBeenCalledWith('cookie2', {
        httpOnly: true,
        domain: 'example.com',
      });
    });
  });

  // Add similar describe blocks for other methods in CookieHelper

  afterEach(() => {
    jest.clearAllMocks();
  });
});
