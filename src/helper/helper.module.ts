import { DynamicModule, Module, Provider } from '@nestjs/common';
import { HelperService } from './helper.service';
import { sync } from 'glob';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CDPEventListener } from './cdp/helper.cdp.listener';
import { WebengageChannelProviders } from './cdp/helper.cdp.webengage';
import { GoogleAnalytics } from './helper.ga4';
import { UserSignups } from '../db/mysql/entity/user-signups.entity';
import { UserMgmtUtilityHelper } from '../user/helper/user-mgmt-utility.helper';
import { KafkaService } from '../common/services/communication/kafka/kafka.service';
import { LrsService } from '../common/services/communication/lrs/lrs.service';
import { CryptoProviders } from './helper.crypto';
import { Logger } from '../logging/logger';
import { Ic9Service } from './../common/services/communication/ice9/ice9.service';
import { PaperclipService } from './../common/services/communication/paperclip/paperclip.service';
import { Cloud6Service } from './../common/services/communication/cloud6/cloud6.service';
import { EmailService } from './../common/services/email/email.service';
import { EnterpriseService } from './../common/services/communication/enterprise/enterprise.service';
import { DeviantsService } from '../common/services/communication/deviants/deviants.service';
import { S3UploaderService } from './s3Client/s3.client.helper';
import { SentinelUser } from '../db/mysql/entity/sentinel-users.entity';
import { ChangeLogService } from './../common/services/communication/changelog/changelog.service';
import { SentinelRole } from '../db/mysql/entity/sentinel-roles.entity';
import { SentinelUsersRole } from '../db/mysql/entity/sentinel-users-roles.entity';
import { SentinelUserProfessionalData } from '../db/mysql/entity/sentinel_user_professional_data.entity';
import { SentinelUserAcademicData } from '../db/mysql/entity/sentinel_user_academic_data.entity';
import { GdprRequest } from '../db/mysql/entity/gdpr-request.entity';
import { GdprRequestHelper } from '../internal/helper/gdpr-request.helper';
import { EnterpriseLmsPreferences } from '../db/mysql/entity/enterprise-lms-preferences.entity';
import { EnterpriseLmsSetting } from '../db/mysql/entity/enterprise-lms-settings.entity';
import {  NpsFeedbackService } from '../common/services/communication/nps-feedback/nps-feedback.service';

@Module({
  imports: [TypeOrmModule.forFeature([UserSignups,SentinelUser,SentinelRole,SentinelUsersRole,SentinelUserProfessionalData,SentinelUserAcademicData,GdprRequest,EnterpriseLmsPreferences , EnterpriseLmsSetting])],

  providers: [
    HelperService,
    ...WebengageChannelProviders,
    ...CryptoProviders,
    CDPEventListener,
    UserMgmtUtilityHelper,
    GoogleAnalytics,
    LrsService,
    KafkaService,
    Ic9Service,
    PaperclipService,
    Cloud6Service,
    EmailService,
    EnterpriseService,
    DeviantsService,
    S3UploaderService,
    ChangeLogService,
    GdprRequestHelper,
    NpsFeedbackService
  ],
  exports: [HelperService, UserMgmtUtilityHelper, GoogleAnalytics, ...CryptoProviders],
})
export class HelperModule {
  static async registerHelpersAsync(): Promise<DynamicModule> {
    // Feel free to change path if your structure is different
    const helpersPath = sync('src/**/helper/*.helper.ts');
    const relativePathWithoutExt = helpersPath
      // Replace src, because you are probably running the code
      // from dist folder
      .map((path) => path.replace('src/', './../'))
      .map((path) => path.replace('.ts', ''));
    const helperProviders: Provider<any>[] = [];
    const importedHelpers = await Promise.all(relativePathWithoutExt.map((path) => import(path)));
    importedHelpers.forEach((entry) => {
      //Assumption only 1-export
      // Might be different if you are using default export instead
      const helper = entry[Object.keys(entry)[0]];
      const provide_as: string = helper.name.toString();
      helperProviders.push({
        provide: provide_as.toUpperCase(),
        useClass: helper,
      });
    });

    const dyna_mod = {
      module: HelperModule,
      providers: [...helperProviders, HelperService],
      exports: [...helperProviders, HelperService],
      global: true,
    };

    Logger.info(dyna_mod, { depth: null });
    return dyna_mod;
  }
}
