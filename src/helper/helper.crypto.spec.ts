import { Logger } from '../logging/logger';
import { CryptoHelper, CryptoException } from './helper.crypto';
import { createHash, createHmac, createCipheriv, createDecipheriv } from 'crypto';
enum HASHTYPE {
  STANDARD_HASH = 'Standard',
  HMAC = 'HMAC',
}
// Mocking createHash, createHmac, createCipheriv, and createDecipheriv functions
jest.mock('crypto', () => ({
  createHash: jest.fn(),
  createHmac: jest.fn(),
  createCipheriv: jest.fn(),
  createDecipheriv: jest.fn(),
}));

describe('CryptoHelper', () => {
  let cryptoHelper: CryptoHelper;
  const cryptoConfig = {
    algo: 'aes-256-cbc',
    key: 'your_secret_key',
    iv: 'your_initialization_vector',
  };

  beforeEach(() => {
    cryptoHelper = new CryptoHelper(cryptoConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('hash', () => {
    it('should call createHash function and return the hash', () => {
      const input = '<EMAIL>';
      const expectedHash = 'some_hash_value';

      (createHash as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce(expectedHash),
      });

      const hash = cryptoHelper.hash(input, 'sha256');
      expect(hash).toEqual(expectedHash);
      expect(createHash).toHaveBeenCalledWith('sha256');
    });

    it('should throw CryptoException when createHash throws', () => {
      const input = 'Hello, world!';

      (createHash as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Error in createHash');
      });

      expect(() => cryptoHelper.hash(input, 'sha256')).toThrowError(CryptoException);
    });
  });

  describe('createHmac', () => {
    it('should call createHmac function and return the HMAC', () => {
      const input = '<EMAIL>';
      const key = 'your_hmac_key';
      const expectedHmac = 'some_hmac_value';

      (createHmac as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce(expectedHmac),
      });

      const hmac = cryptoHelper.createHmac(input, key, 'sha256');
      expect(hmac).toEqual(expectedHmac);
      expect(createHmac).toHaveBeenCalledWith('sha256', key);
    });

    it('should throw CryptoException when createHmac throws', () => {
      const input = '<EMAIL>';
      const key = 'your_hmac_key';

      (createHmac as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Error in createHmac');
      });

      expect(() => cryptoHelper.createHmac(input, key, 'sha256')).toThrowError(CryptoException);
    });
  });

  describe('encrypt', () => {
    it('should call createCipheriv function and return the encrypted data', () => {
      const data = '<EMAIL>';
      const expectedEncryptedData = 'some_encrypted_data';
      const encryptedInfo = 'c29tZV9lbmNyeXB0ZWRfZGF0YQ==';
      (createCipheriv as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnValueOnce(expectedEncryptedData),
        final: jest.fn().mockReturnValueOnce(''), // Final empty data
      });

      const encryptedData = cryptoHelper.encrypt(data);
      expect(encryptedData).toEqual(encryptedInfo);
    });

    it('should throw CryptoException when createCipheriv throws', () => {
      const data = 'Hello, world!';

      (createCipheriv as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Error in createCipheriv');
      });

      expect(() => cryptoHelper.encrypt(data)).toThrowError(CryptoException);
    });
  });

  describe('decrypt', () => {
    it('should call createDecipheriv function and return the decrypted data', () => {
      const encryptedData = 'some_encrypted_data';
      const expectedDecryptedData = '<EMAIL>';

      (createDecipheriv as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnValueOnce(expectedDecryptedData),
        final: jest.fn().mockReturnValueOnce(''), // Final empty data
      });

      const decryptedData = cryptoHelper.decrypt(encryptedData);
      expect(decryptedData).toEqual(expectedDecryptedData);
    });

    it('should throw CryptoException when createDecipheriv throws', () => {
      const encryptedData = 'some_encrypted_data';

      (createDecipheriv as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Error in createDecipheriv');
      });

      expect(() => cryptoHelper.decrypt(encryptedData)).toThrowError(CryptoException);
    });
  });

  describe('onModuleInit', () => {
    it('should initialize cryptography keys', () => {
      const hashKeyMock = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce('key_digest'),
      };

      const hashIvMock = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce('iv_digest'),
      };

      (createHash as jest.Mock).mockReturnValueOnce(hashKeyMock).mockReturnValueOnce(hashIvMock);

      cryptoHelper.onModuleInit();
      expect(cryptoHelper['__algo']).toBe(cryptoConfig.algo);
      expect(cryptoHelper['__key']).toBe('key_digest'.substring(0, 32));
      expect(cryptoHelper['__iv']).toBe('iv_digest'.substring(0, 16));
      expect(createHash).toHaveBeenCalledTimes(2);
    });

    it('should log error when initializing cryptography keys fails', () => {
      (createHash as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Error in createHash');
      });

      const loggerErrorSpy = jest.spyOn(Logger, 'error');

      cryptoHelper.onModuleInit();

      expect(loggerErrorSpy).toHaveBeenCalled();
    });
  });
});
