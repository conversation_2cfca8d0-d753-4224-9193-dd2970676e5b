import { Test, TestingModule } from '@nestjs/testing';
import { LrsHelper } from './lrs.helper';
import { ConfigService } from '@nestjs/config';
import { LrsService } from '../common/services/communication/lrs/lrs.service';
import { KafkaService } from '../common/services/communication/kafka/kafka.service';

describe('LrsHelper', () => {
  let lrsHelper: LrsHelper;

  const mockConfigService = {
    get: jest.fn(),
  };
  const mockLrsService = {
    sendLrsData: jest.fn(),
  };
  const mockKafkaService = {
    publish: jest.fn(),
  };
  const mockCryptoHelper = {
    createHmac: jest.fn(),
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LrsHelper,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: LrsService,
          useValue: mockLrsService,
        },
        {
          provide: KafkaService,
          useValue: mockKafkaService,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: mockCryptoHelper,
        },
      ],
    }).compile();

    lrsHelper = module.get<LrsHelper>(LrsHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(lrsHelper).toBeDefined();
  });

  it('should send data to LRS when lrsEnabled is 1', async () => {
    mockConfigService.get.mockReturnValue('1');
    await lrsHelper.sendDataToLrs({}, { verb: 'visit', objectType: 'type', objectId: 'id', dataVals: [] });
    expect(mockLrsService.sendLrsData).toHaveBeenCalled();
  });

  it('should not send data to LRS when lrsEnabled is not 1', async () => {
    mockConfigService.get.mockReturnValue('0');
    await lrsHelper.sendDataToLrs({}, { verb: 'visit', objectType: 'type', objectId: 'id', dataVals: [] });
    expect(mockLrsService.sendLrsData).not.toHaveBeenCalled();
  });

  it('should publish to Kafka topics', async () => {
    mockConfigService.get.mockReturnValue('1');
    mockConfigService.get.mockReturnValue('prefix');
    mockCryptoHelper.createHmac.mockReturnValue('fakeKey');
    await lrsHelper.sendDataToLrs({}, { verb: 'visit', objectType: 'type', objectId: 'id', dataVals: [] });
    expect(mockKafkaService.publish).toHaveBeenCalled();
  });

  // Add more test cases to cover other scenarios and edge cases.
});
