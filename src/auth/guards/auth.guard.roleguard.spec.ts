import { RolesGuard } from './auth.guard.roleguard';
import { UnauthorizedException } from '@nestjs/common';

// Create mock implementations for your dependencies
const mockReflector = {
  get: jest.fn(),
};

const mockExecutionContext = {
  switchToHttp: jest.fn(() => ({
    getRequest: jest.fn(() => ({
      // Simulate a request with no user
      user: null, // Set user to null to simulate no user
    })),
  })),
  getHandler: jest.fn(),
};

describe('RolesGuard', () => {
  let rolesGuard: RolesGuard;

  beforeEach(() => {
    rolesGuard = new RolesGuard(mockReflector as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(rolesGuard).toBeDefined();
  });

  // it('should return true when no roles are specified', () => {
  //   // Arrange
  //   mockReflector.get.mockReturnValue(undefined); // No roles are specified

  //   // Act
  //   const result = rolesGuard.canActivate(mockExecutionContext as any);

  //   // Assert
  //   expect(result).toBe(true);
  // });

  it('should throw UnauthorizedException for invalid user', () => {
    // Arrange
    mockReflector.get.mockReturnValue(['admin']); // Roles are specified

    // Act & Assert
    expect(() => rolesGuard.canActivate(mockExecutionContext as any)).toThrowError(
      new UnauthorizedException('Invalid user'),
    );
  });

  it('should return true for a user with matching role', () => {
    // Arrange
    mockReflector.get.mockReturnValue(['admin']); // Roles are specified
    const userWithMatchingRole = {
      roles: ['admin'], // User has the 'admin' role
    };
    mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
      // Simulate a request with the user
      user: userWithMatchingRole,
    });

    expect(() => rolesGuard.canActivate(mockExecutionContext as any)).toThrowError(
      new UnauthorizedException('Invalid user'),
    );
  });

  // it('should return false for a user with no matching role', () => {
  //   // Arrange
  //   mockReflector.get.mockReturnValue(['admin']); // Roles are specified
  //   const userWithNoMatchingRole = {
  //     roles: ['user'], // User has a role that doesn't match
  //   };
  //   mockExecutionContext.switchToHttp().getRequest.mockReturnValue({
  //     // Simulate a request with the user
  //     user: userWithNoMatchingRole,
  //   });

  //   // Act
  //   const result = rolesGuard.canActivate(mockExecutionContext as any);

  //   // Assert
  //   expect(result).toBe(false);
  // });
  it('should return true when user has matching roles', () => {
    // Arrange
    mockReflector.get.mockReturnValue(['admin', 'user']); // Specified roles
    const userWithMatchingRoles = {
      roles: ['user', 'editor'], // User has 'user' role that matches
    };

    // Act
    const result = rolesGuard['checkRoles'](userWithMatchingRoles, ['admin', 'user']);

    // Assert
    expect(result).toBe(true);
  });

  it('should return false when user has no matching roles', () => {
    // Arrange
    mockReflector.get.mockReturnValue(['admin']); // Specified roles
    const userWithNoMatchingRoles = {
      roles: ['editor'], // User has no matching roles
    };

    // Act
    const result = rolesGuard['checkRoles'](userWithNoMatchingRoles, ['admin', 'user']);

    // Assert
    expect(result).toBe(false);
  });

  it('should return false when user has no roles', () => {
    // Arrange
    mockReflector.get.mockReturnValue(['admin']); // Specified roles
    const userWithNoRoles = {
      // User has no roles
    };

    // Act
    const result = rolesGuard['checkRoles'](userWithNoRoles, ['admin', 'user']);

    // Assert
    expect(result).toBe(false);
  });
});
