// google.strategy.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { GoogleStrategy } from './googleStrategy';
import { VerifyCallback } from 'passport-google-oauth20';

describe('GoogleStrategy', () => {
  let strategy: GoogleStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              // Mock the configuration values here
              return {
                googleClientId: 'yourGoogleClientId',
                googleClientSecret: 'yourGoogleClientSecret',
                googleCallback: 'yourGoogleCallbackURL',
              }[key];
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<GoogleStrategy>(GoogleStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should validate and return user information', async () => {
    // Mock profile data for testing
    const mockProfile = {
      id: '123',
      displayName: '<PERSON>',
      emails: [{ value: '<EMAIL>' }],
    };

    // Mock the validate method parameters
    const mockValidateParams: [
      any, // request
      string, // accessToken
      string, // refreshToken
      any, // profile
      VerifyCallback, // done
    ] = [
      {}, // request
      'accessToken123',
      'refreshToken123',
      mockProfile,
      jest.fn() as VerifyCallback, // Mock the done callback
    ];

    // Call the validate method
    const result = await strategy.validate(...mockValidateParams);

    // Validate the result
    expect(mockValidateParams[4]).toHaveBeenCalledWith(
      null,
      expect.objectContaining({
        sub: '123',
        name: 'John Doe',
        email: '<EMAIL>',
        type: 'google',
      }),
    );
  });

  it('should handle validation failure with done callback', async () => {
    // Mock the validate method parameters to simulate an error
    const mockValidateParams: [
      any, // request
      string, // accessToken
      string, // refreshToken
      any, // profile
      VerifyCallback, // done
    ] = [
      null, // request
      'accessToken123',
      'refreshToken123',
      null, // Mocking a scenario where profile is not available
      jest.fn() as VerifyCallback, // Mock the done callback
    ];

    // Invoke the validate method and mock the done callback
    await strategy.validate(...mockValidateParams);

    // Validate that the done callback was called with an error
    expect(mockValidateParams[4]).toHaveBeenCalledWith(
      null,
      expect.objectContaining({
        email: '',
        name: undefined,
        sub: undefined,
        type: 'google',
      }),
    );
  });
});
