import { ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import { getCurrentUser } from './auth.current.user';
interface CustomRequest extends Request {
  user?: any;
}

jest.mock('./auth.current.user', () => ({
  getCurrentUser: jest.fn((req: CustomRequest) => req?.user),
  GetUser: jest.fn((_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  }),
}));

describe('getCurrentUser Function', () => {
  it('should return the user from the request object', () => {
    const mockUser = { id: 1, name: 'Test User' };
    const mockRequest = { user: mockUser } as unknown as Request;

    const result = getCurrentUser(mockRequest);
    expect(result).toEqual(mockUser);
  });

  it('should return undefined if user is not present in the request object', () => {
    const mockRequest = {} as unknown as Request;

    const result = getCurrentUser(mockRequest);
    expect(result).toBeUndefined();
  });
});

describe('GetUser Function', () => {
  it('should return the user from the ExecutionContext', () => {
    const mockUser = { id: 1, name: 'Test User' };
    const mockRequest = { user: mockUser } as unknown as Request;

    const mockContext = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
    } as unknown as ExecutionContext;

    const { GetUser } = require('./auth.current.user');
    const result = GetUser(null, mockContext);
    expect(result).toEqual(mockUser);
  });

  it('should return undefined if user is not present in the ExecutionContext', () => {
    const mockRequest = {} as unknown as Request;

    const mockContext = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
    } as unknown as ExecutionContext;

    const { GetUser } = require('./auth.current.user');
    const result = GetUser(null, mockContext);
    expect(result).toBeUndefined();
  });
});
