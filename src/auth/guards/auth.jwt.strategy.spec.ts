import { JwtStrategy } from './auth.jwt.strategy';
import { UnauthorizedException } from '@nestjs/common';
import { AuthTokenHelper } from '../helper/auth.tokenhelper';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';

describe('JwtStrategy', () => {
  const authHelperMock: AuthTokenHelper = {
    decryptAttribute: jest.fn(),
  } as any;

  let jwtStrategy: JwtStrategy;
  const jwtServiceMock: JwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  } as any;
  const configServiceMock: ConfigService = {
    get: (key) => {
      if (key === 'jwtSecret') {
        return 'your_jwt_secret_key_here'; // Provide a valid JWT secret key for testing
      }
    },
  } as any;

  beforeEach(() => {
    jwtStrategy = new JwtStrategy(jwtServiceMock, configServiceMock, authHelperMock);
  });

  it('should be defined', () => {
    expect(jwtStrategy).toBeDefined();
  });

  describe('validate', () => {
    it('should validate and return user data when payload is valid', async () => {
      // Mock payload and request
      const payload = {
        data: {
          id: 'encrypted_id',
          email: 'encrypted_email',
        },
      };
      const request: Request = {} as any;

      // Mock decryptAttribute function
      authHelperMock.decryptAttribute = jest.fn().mockReturnValue('decrypted_id').mockReturnValue('decrypted_email');

      // Call the validate function
      const result = await jwtStrategy.validate(request, payload);

      // Assertions
      expect(result).toEqual({
        uid: 'decrypted_email',
        email: 'decrypted_email',
      });

      // Verify that decryptAttribute was called with the correct values
      expect(authHelperMock.decryptAttribute).toHaveBeenCalledWith('encrypted_id');
      expect(authHelperMock.decryptAttribute).toHaveBeenCalledWith('encrypted_email');
    });

    it('should throw UnauthorizedException when payload is missing required data', async () => {
      // Mock payload with missing data
      const payload = {};

      // Mock request
      const request: Request = {} as any;

      // Call the validate function and expect it to throw an UnauthorizedException
      await expect(jwtStrategy.validate(request, payload)).rejects.toThrow(UnauthorizedException);
    });
  });
});
