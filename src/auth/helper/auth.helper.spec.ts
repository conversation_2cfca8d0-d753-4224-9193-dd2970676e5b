import { Test, TestingModule } from '@nestjs/testing';
import { AuthHelper } from './auth.helper';
import { HelperService } from '../../helper/helper.service';
import { UserService } from '../../user/services/user.service';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { AuthService } from '../services/auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { EmailService } from '../../common/services/email/email.service';
import { CryptoHelper } from '../../helper/helper.crypto';
import { UserMgmtCommunityService } from '../../user/services/communication/usermgmt.community.service';
import { SocialLoginService } from '../services/social-login/social-login.service';
import { UserMgmtUtilityHelper } from '../../user/helper/user-mgmt-utility.helper';
import { AuthTokenHelper } from './auth.tokenhelper';
import { Logger } from '../../logging/logger';
import { UserRepository } from '../../user/repositories/user/user.repository';
describe('AuthHelper', () => {
  let authHelper: AuthHelper;
  let helperService: HelperService;
  let configService: ConfigService;
  let cryptoHelper: CryptoHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthHelper],
      providers: [
        AuthService,
        ConfigService,
        {
          provide: UserMgmtUtilityHelper,
          useValue: {},
        },
        {
          provide: AuthHelper,
          useValue: {
            getTokenRedirectUrl: jest.fn(),
          },
        },
        {
          provide: UserService,
          useValue: {},
        },

        {
          provide: Cloud6Service,
          useValue: {},
        },
        {
          provide: EmailService,
          useValue: {},
        },
        {
          provide: 'CRYPTO_HELPER', useValue: {
            encrypt: jest.fn(),
            decrypt: jest.fn(),
          }
        },
        {
          provide: PaperclipService,
          useValue: {},
        },
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            decode: jest.fn(),
          },
        },
        {
          provide: HelperService,
          useValue: {
            getHelper: jest.fn(),
            get: jest.fn(),
          },
        },
        {
          provide: UserMgmtCommunityService,
          useValue: {},
        },
        {
          provide: SocialLoginService,
          useValue: {
            generatePassword: jest.fn(),
          },
        },
      ],
    }).compile();

    authHelper = module.get<AuthHelper>(AuthHelper);
    helperService = module.get<HelperService>(HelperService);
    configService = module.get<ConfigService>(ConfigService);
    cryptoHelper = module.get<CryptoHelper>('CRYPTO_HELPER');
  });

  describe('controller should be defined ', () => {
    it('should be defined', () => {
      expect(authHelper).toBeDefined();
    });
  });

  describe('getTokenRedirectUrl', () => {
    let authHelper: AuthHelper;
    // let configService: ConfigService;
    let helperService: HelperService;
    let paperclipServiceMock: any;

    beforeEach(async () => {
      paperclipServiceMock = {
        getSkillupReferralRewardInfo: jest.fn(),
      };

      const moduleRef: TestingModule = await Test.createTestingModule({
        providers: [
          AuthHelper,
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn((key: string) => {
                const map = {
                  freemiumAssignmentBlockRedirectUrl: 'https://blockurl.com/',
                  freemiumAssignmentRedirectUrl: 'https://redirect.com/',
                  skillupEnrollmentCampaign: 'campaignKey',
                };
                return map[key];
              }),
            },
          },
          {
            provide: HelperService,
            useValue: {
              get: jest.fn().mockResolvedValue(paperclipServiceMock),
            },
          },
          {
            provide: 'CRYPTO_HELPER',
            useValue: {},
          },
        ],
      }).compile();

      authHelper = moduleRef.get(AuthHelper);
      // configService = moduleRef.get(ConfigService);
      helperService = moduleRef.get(HelperService);

      // Mock method inside the class
      jest
        .spyOn(authHelper, 'generateRedirectLinkToManageRedirect')
        .mockResolvedValue('https://finalredirect.com');
    });

    it('should return redirect URL with reward info and skillup campaign cookie', async () => {
      const user = { uid: 123 };
      const assignmentToken = 'token123';
      const cookieData = { campaignKey: 'abc123' };
      const calendarUrl = 'https://calendar.com';

      paperclipServiceMock.getSkillupReferralRewardInfo.mockResolvedValue({
        respData: {
          status: 'success',
          skillUpReferralInfo: {
            enrolmentRestricted: true,
            userRefCode: 'ref123',
          },
        },
      });

      const result = await authHelper.getTokenRedirectUrl(cookieData, assignmentToken, user, calendarUrl);

      expect(helperService.get).toHaveBeenCalledWith(PaperclipService);
      expect(paperclipServiceMock.getSkillupReferralRewardInfo).toHaveBeenCalledWith({ user_id: 123 });
      expect(result).toEqual({
        status: true,
        redirectUrl: 'https://finalredirect.com',
        setCookie: true,
        cookieValue: {
          name: 'freemiumAssignmentToken',
          value: 'token123',
        },
      });
    });

    it('should return fallback redirect URL if reward data is missing', async () => {
      const user = { uid: 123 };
      const assignmentToken = 'token123';
      const cookieData = {};
      const calendarUrl = 'https://calendar.com';

      paperclipServiceMock.getSkillupReferralRewardInfo.mockResolvedValue({});

      const result = await authHelper.getTokenRedirectUrl(cookieData, assignmentToken, user, calendarUrl);

      expect(result).toEqual({
        status: true,
        redirectUrl: 'https://finalredirect.com',
        setCookie: true,
        cookieValue: {
          name: 'freemiumAssignmentToken',
          value: 'token123',
        },
      });
    });

    it('should return failure object if exception occurs', async () => {
      jest.spyOn(helperService, 'get').mockRejectedValue(new Error('mock error'));

      const result = await authHelper.getTokenRedirectUrl({}, 'token123', { uid: 1 }, 'https://calendar.com');

      expect(result).toEqual({
        status: false,
        redirectUrl: '',
        setCookie: false,
      });
    });
  });

  describe('getNpsRedirectUrl', () => {
    const cookieData = { npsCookieKey: 'jwt-token' };
    const npsCookie = 'nps-cookie-value';
    const user = { uid: 101, email: '<EMAIL>' };
    const redirectUrl = 'https://redirect.com';
    const decodedToken = { url: 'https://multi-account.com' };

    beforeEach(() => {
      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'npsRedirectCookie') return 'npsCookieKey';
        return '';
      });

      jest.spyOn(helperService, 'getHelper').mockImplementation(async (token) => {
        if (token === AuthTokenHelper) {
          return {
            decodeJWTToken: jest.fn().mockResolvedValue(decodedToken),
          };
        }
        return undefined;
      });

      jest.spyOn(authHelper, 'generateRedirectLinkToManageRedirect').mockResolvedValue('https://final-redirect.com');
    });

    it('should return redirect URL and cookie value when token is decoded', async () => {
      const result = await authHelper.getNpsRedirectUrl(cookieData, npsCookie, user, redirectUrl);

      expect(result).toEqual({
        status: true,
        redirectUrl: 'https://final-redirect.com',
        setCookie: true,
        cookieValue: {
          name: 'npsRedirectCookie',
          value: npsCookie,
        },
      });
    });

    it('should handle error and return default response', async () => {
      jest.spyOn(helperService, 'getHelper').mockRejectedValue(new Error('fail'));

      const result = await authHelper.getNpsRedirectUrl(cookieData, npsCookie, user, redirectUrl);

      expect(result).toEqual({
        status: false,
        redirectUrl: '',
        setCookie: false,
      });
    });
  });

  describe('handleUserSignup', () => {
    const utm_source = 'google';
    const referralCode = 'ref123';
    const signupDetail = {
      email: '<EMAIL>',
      uid: 1001,
      first_name: 'Test',
    };

    const userCommunity = {
      saveUserSignup: jest.fn(),
    };

    const paperclipService = {
      saveSkillupReferral: jest.fn(),
    };

    beforeEach(() => {
      jest.spyOn(helperService, 'getHelper').mockImplementation(async (key: any) => {
        if (key === 'UsermgmtCommunityHelper') return userCommunity;
        return undefined;
      });

      jest.spyOn(helperService, 'get').mockImplementation(async (token: any) => {
        if (token === PaperclipService) return paperclipService;
        if (token === UserMgmtCommunityService) return {}; // unused in current implementation
        return undefined;
      });

      userCommunity.saveUserSignup.mockResolvedValue({ status: 'success' });
      paperclipService.saveSkillupReferral.mockResolvedValue({});
    });

    it('should save user signup and referral', async () => {
      await authHelper.handleUserSignup(utm_source, referralCode, signupDetail);

      expect(userCommunity.saveUserSignup).toHaveBeenCalledWith({
        ...signupDetail,
        utm_source,
      });
      expect(paperclipService.saveSkillupReferral).toHaveBeenCalledWith({
        email: signupDetail.email,
        user_id: signupDetail.uid,
        first_name: signupDetail.first_name,
        refcode: referralCode,
      });
    });

    it('should log error if exception is thrown', async () => {
      const logErrorSpy = jest.spyOn(authHelper as any, 'logError').mockImplementation(() => { });
      userCommunity.saveUserSignup.mockRejectedValue(new Error('fail'));

      await authHelper.handleUserSignup(utm_source, referralCode, signupDetail);

      expect(logErrorSpy).toHaveBeenCalledWith(
        'AuthHelper',
        signupDetail,
        'fail'
      );
    });
  });

  describe('authenticateUser', () => {
    it('should return user if email and password are valid', async () => {
      const loginDto = { email: '<EMAIL>', password: 'password123' };
      const hashedPassword = '$S$validHash';
      const user = { uid: 123, email: loginDto.email, password: hashedPassword, status: 1, country_code: 'IN' };

      const userRepo = {
        getUserByEmail: jest.fn().mockResolvedValue(user),
      };

      const userMgmtUtilityHelper = {
        updateUserTimezone: jest.fn(),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(userRepo);
      jest.spyOn(helperService, 'getHelper').mockResolvedValue(userMgmtUtilityHelper);
      jest.spyOn(require('drupal-hash'), 'checkPassword').mockResolvedValue(true);

      const result = await authHelper.authenticateUser(loginDto);
      expect(result).toEqual(user);
    });

    it('should throw BadRequestException for invalid password', async () => {
      const loginDto = { email: '<EMAIL>', password: 'wrong' };
      const user = { uid: 123, email: loginDto.email, password: 'hashed', status: 1 };

      jest.spyOn(helperService, 'get').mockResolvedValue({
        getUserByEmail: jest.fn().mockResolvedValue(user),
      });

      jest.spyOn(require('drupal-hash'), 'checkPassword').mockResolvedValue(false);

      await expect(authHelper.authenticateUser(loginDto)).rejects.toThrow('InvalidCredentials');
    });
  });

  describe('getLoginRedirectUrl', () => {
    it('should return URL with query params when redirectUrl is valid', async () => {
      const cookieData = {
        freemiumAssignmentToken: '',
        npsRedirectCookie: '',
      };

      const loginDto: any = {
        redirect_url: 'https://valid.com',
        calendar_url: '',
        isB2BAndB2C: false,
        domainGid: '',
        urlDomain: '',
      };

      const user = { uid: 123, email: '<EMAIL>' };

      const userHelper = {
        updateUserLoginTime: jest.fn(),
      };

      jest.spyOn(helperService, 'getHelper').mockResolvedValue(userHelper);

      const result = await authHelper.getLoginRedirectUrl(cookieData, loginDto, user);
      expect(result.url).toContain('/auth/multi-account');
    });

    it('should return liveClassRedirectUrl if calendarUrl exists', async () => {
      const cookieData = {
        freemiumAssignmentToken: '',
        npsRedirectCookie: '',
      };

      const loginDto: any = {
        redirect_url: 'https://valid.com',
        calendar_url: 'https://calendar.com',
        isB2BAndB2C: false,
        domainGid: '',
        urlDomain: '',
      };

      const user = { uid: 123, email: '<EMAIL>' };

      const userHelper = { updateUserLoginTime: jest.fn() };
      const userMgmtUtilityHelper = {
        liveClassRedirectUrl: jest.fn().mockResolvedValue('https://calendarRedirect.com'),
      };

      jest.spyOn(helperService, 'getHelper').mockImplementation(async (token) => {
        if (token === 'UserHelper') return userHelper;
        return userMgmtUtilityHelper;
      });

      const result = await authHelper.getLoginRedirectUrl(cookieData, loginDto, user);
      expect(result.url).toBe('https://calendarRedirect.com');
    });
  });

  describe('generateRedirectLinkToManageRedirect', () => {
    it('should return redirect URL with signed token', async () => {
      const mockUser = {
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        phone_no: '**********',
      };

      const mockToken = 'signed.jwt.token';
      const jwtSecret = 'test-secret';
      const baseRedirectUrl = 'https://manage.auth/';

      const mockAuthTokenHelper = {
        createSignedToken: jest.fn().mockResolvedValue(mockToken),
      };

      jest.spyOn(helperService, 'get').mockImplementation(async (token) => {
        if (token === AuthTokenHelper) return mockAuthTokenHelper;
        return {};
      });

      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'jwtSecret') return jwtSecret;
        if (key === 'manageAuthRedirectUrl') return baseRedirectUrl;
        return '';
      });

      const result = await authHelper.generateRedirectLinkToManageRedirect(
        mockUser,
        'login',
        'email',
        'https://target.url',
        'https://calendar.url',
      );

      expect(result).toBe(baseRedirectUrl + mockToken);

      expect(mockAuthTokenHelper.createSignedToken).toHaveBeenCalledWith(
        {
          data: {
            email: mockUser.email,
            firstName: mockUser.first_name,
            lastName: mockUser.last_name,
            phoneNumber: mockUser.phone_no,
            authType: 'Login',
            loginMethodType: 'email',
            redirectUrl: 'https://target.url',
            calendar_url: 'https://calendar.url',
          },
        },
        {
          secret: jwtSecret,
        },
      );
    });

    it('should log error and return undefined on failure', async () => {
      jest.spyOn(helperService, 'get').mockRejectedValue(new Error('TokenHelper error'));

      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      const result = await authHelper.generateRedirectLinkToManageRedirect(
        {},
        'signup',
        'google',
        'https://url',
        'https://calendar',
      );

      expect(result).toBeUndefined();
      expect(loggerSpy).toHaveBeenCalledWith(
        'generateRedirectLinkToManageRedirect',
        expect.objectContaining({
          METHOD: expect.stringContaining('generateRedirectLinkToManageRedirect'),
          MESSAGE: 'TokenHelper error',
        }),
      );
    });
  });

  describe('forgotPasswordAttemptLimit', () => {
    it('should return limit_exceeded if attempts exceed fpResetCount', async () => {
      const mockEmail = '<EMAIL>';
      const mockFpResetCount = 3;
      const mockFpResetWaitTime = 10;
      const mockFpEmailInCacheTime = 15;

      const cacheServiceMock = {
        get: jest.fn().mockResolvedValue(mockFpResetCount),
        set: jest.fn(),
      };

      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        const map = {
          fpResetCount: mockFpResetCount,
          fpResetWaitTime: mockFpResetWaitTime,
          fpEmailInCacheTime: mockFpEmailInCacheTime,
        };
        return map[key];
      });

      jest.spyOn(helperService, 'get').mockResolvedValue(cacheServiceMock);

      const result = await authHelper.forgotPasswordAttemptLimit(mockEmail);

      expect(cacheServiceMock.set).toHaveBeenCalledWith(
        `${mockEmail}_reset_timeout`,
        expect.any(Number),
        60 * mockFpResetWaitTime
      );
      expect(result).toEqual({
        status: 'limit_exceeded',
        msg: `Attempt limit exceeded. Please try again after  ${mockFpResetWaitTime} minutes.`,
      });
    });

    it('should increment attempt count if under limit', async () => {
      const mockEmail = '<EMAIL>';
      const currentCount = 1;
      const fpResetCount = 3;
      const fpResetWaitTime = 10;
      const fpEmailInCacheTime = 15;

      const cacheServiceMock = {
        get: jest.fn().mockResolvedValue(currentCount),
        set: jest.fn(),
      };

      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        const map = {
          fpResetCount,
          fpResetWaitTime,
          fpEmailInCacheTime,
        };
        return map[key];
      });

      jest.spyOn(helperService, 'get').mockResolvedValue(cacheServiceMock);

      const result = await authHelper.forgotPasswordAttemptLimit(mockEmail);

      expect(cacheServiceMock.set).toHaveBeenCalledWith(
        `${mockEmail}_reset`,
        currentCount + 1,
        60 * fpEmailInCacheTime
      );
      expect(result).toBeUndefined(); // Because no return statement in this path
    });
  });

  describe('getTokenByEmail', () => {
    it('should throw BadRequestException if user is not found', async () => {
      const email = '<EMAIL>';
      const clientKey = 'client-key';
      const userRepoMock = {
        getUserByEmail: jest.fn().mockResolvedValue(null),
      };

      jest.spyOn(helperService, 'get').mockImplementation(async (token) => {
        if (token === UserRepository) return userRepoMock;
        return {};
      });

      await expect(authHelper.getTokenByEmail(email, clientKey)).rejects.toThrow(
        'No active account associated with this email address.'
      );
    });

    it('should throw BadRequestException if user is inactive', async () => {
      const email = '<EMAIL>';
      const clientKey = 'client-key';
      const userRepoMock = {
        getUserByEmail: jest.fn().mockResolvedValue({ status: 0 }),
      };

      jest.spyOn(helperService, 'get').mockImplementation(async (token) => {
        if (token === UserRepository) return userRepoMock;
        return {};
      });

      await expect(authHelper.getTokenByEmail(email, clientKey)).rejects.toThrow(
        'Try using a different email address to create an account.'
      );
    });

    it('should return tokens for valid user', async () => {
      const email = '<EMAIL>';
      const clientKey = 'client-key';
      const user = {
        uid: 1,
        email,
        first_name: 'John',
        last_name: 'Doe',
        phone_no: '**********',
        status: 1,
      };

      const tokenResult = {
        idToken: 'jwt.token.here',
        userData: { uid: 1 },
      };

      const userRepoMock = {
        getUserByEmail: jest.fn().mockResolvedValue(user),
      };

      const authTokenHelperMock = {
        generateSessionTokens: jest.fn().mockResolvedValue(tokenResult),
      };

      jest.spyOn(helperService, 'get').mockImplementation(async (token) => {
        if (token === UserRepository) return userRepoMock;
        if (token === AuthTokenHelper) return authTokenHelperMock;
        return {};
      });

      const result = await authHelper.getTokenByEmail(email, clientKey);
      expect(result).toEqual(tokenResult);
      expect(authTokenHelperMock.generateSessionTokens).toHaveBeenCalledWith(
        expect.objectContaining({ uid: 1 }),
        clientKey
      );
    });
  });

  describe('getUserPassResetUrl', () => {
    it('should return encrypted reset URL and token', async () => {
      const uid = 123;
      const encryptedUid = 'enc-uid';
      const encryptedToken = 'enc-token';
      const baseUrl = 'https://example.com';

      let cryptoHelperMock: any = async (input: string) => {
        return input.includes(',') ? encryptedToken : encryptedUid;
      }

      jest.spyOn(cryptoHelper, 'encrypt').mockImplementation(cryptoHelperMock);

      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'baseUrl') return baseUrl;
        return '';
      });

      const result = await authHelper.getUserPassResetUrl(uid);

      expect(result).toEqual({
        url: `${baseUrl}/auth/reset/${encryptedUid}/${encryptedToken}`,
        token: encryptedToken,
      });

      expect(cryptoHelper.encrypt).toHaveBeenCalledTimes(2);
      expect(configService.get).toHaveBeenCalledWith('baseUrl');
    });
  });

  describe('getSubDomain', () => {
    it('should return subdomain when valid domain is received', async () => {
      const groupId = 'gid-001';
      const domain = 'company.example.com';

      const enterpriseServiceMock = {
        getGroupDomainByGid: jest.fn().mockResolvedValue({
          data: [{ lmsSiteUrl: domain }],
        }),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(enterpriseServiceMock);

      const result = await authHelper.getSubDomain(groupId);

      expect(result).toBe('company');
      expect(enterpriseServiceMock.getGroupDomainByGid).toHaveBeenCalledWith({ gid: groupId });
    });

    it('should return empty string if no domain found', async () => {
      const groupId = 'gid-002';
      const enterpriseServiceMock = {
        getGroupDomainByGid: jest.fn().mockResolvedValue({
          data: [{}], // No lmsSiteUrl
        }),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(enterpriseServiceMock);

      const result = await authHelper.getSubDomain(groupId);

      expect(result).toBe('');
    });

    it('should throw BadRequestException on error', async () => {
      const groupId = 'gid-003';
      const error = new Error('Service unavailable');

      const enterpriseServiceMock = {
        getGroupDomainByGid: jest.fn().mockRejectedValue(error),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(enterpriseServiceMock);

      await expect(authHelper.getSubDomain(groupId)).rejects.toThrow('Service unavailable');
    });
  });


});
