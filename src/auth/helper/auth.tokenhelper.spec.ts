import { Test, TestingModule } from '@nestjs/testing';
import { AuthTokenHelper } from './auth.tokenhelper';
import { JwtService } from '@nestjs/jwt';
import { CryptoHelper } from '../../helper/helper.crypto';
import { ConfigService } from '@nestjs/config';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Logger } from '../../logging/logger';

class JwtServiceMock {
  signAsync = jest.fn().mockResolvedValue('legacyToken');
  verify = jest.fn().mockReturnValue({ payload: { data: {} } });
  decode = jest.fn();
}
class CryptoHelperMock {
  encrypt = jest.fn();
  decrypt = jest.fn();
}

class ConfigServiceMock {
  get = jest.fn();
}

describe('AuthTokenHelper', () => {
  let authTokenHelper: AuthTokenHelper;
  let jwtService: JwtService;
  let cryptoHelper: CryptoHelper;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthTokenHelper,
        {
          provide: JwtService,
          useClass: JwtServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useClass: CryptoHelperMock,
        },
        {
          provide: ConfigService,
          useClass: ConfigServiceMock,
        },
      ],
    }).compile();

    authTokenHelper = module.get<AuthTokenHelper>(AuthTokenHelper);
    jwtService = module.get<JwtService>(JwtService);
    cryptoHelper = module.get<CryptoHelper>('CRYPTO_HELPER');
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSignedToken', () => {
    it('should create a signed token without sign options', async () => {
      const payload = { data: 'testPayload' };
      const expectedToken = 'signedToken';

      jest.spyOn(jwtService, 'signAsync').mockResolvedValue(expectedToken);

      const result = await authTokenHelper.createSignedToken(payload);

      expect(jwtService.signAsync).toHaveBeenCalledWith(payload);
      expect(result).toBe(expectedToken);
    });

    it('should create a signed token with sign options', async () => {
      const payload = { data: 'testPayload' };
      const signOptions = { expiresIn: '1h' };
      const expectedToken = 'signedTokenWithOptions';

      jest.spyOn(jwtService, 'signAsync').mockResolvedValue(expectedToken);

      const result = await authTokenHelper.createSignedToken(payload, signOptions);

      expect(jwtService.signAsync).toHaveBeenCalledWith(payload, signOptions);
      expect(result).toBe(expectedToken);
    });
  });

  describe('decryptAttribute', () => {
    it('should decrypt attribute using crypto helper', () => {
      const data = 'encryptedData';

      const result = authTokenHelper.decryptAttribute(data);

      expect(cryptoHelper.decrypt).toHaveBeenCalledWith(data);
      // expect(result).toBe('decryptedData');
    });

    it('should return original data if decryption fails', () => {
      const data = 'invalidData';

      jest.spyOn(cryptoHelper, 'decrypt').mockReturnValue(data);

      const result = authTokenHelper.decryptAttribute(data);

      expect(cryptoHelper.decrypt).toHaveBeenCalledWith(data);
      expect(result).toBe(data);
    });
  });
  describe('generateSessionTokens', () => {
    it('should generate session tokens with valid user and client key', async () => {
      // Mock the necessary methods (getUserInfoForJwt and createSignedToken)
      authTokenHelper.getUserInfoForJwt = jest.fn().mockResolvedValue({
        id: '12345',
        email: '<EMAIL>',
        sessionId: 'session123',
        roles: ['user'],
      });

      authTokenHelper.createSignedToken = jest.fn().mockResolvedValue('mocked_token');

      // Define the user and clientKey
      const user: Partial<User> = {
        // Provide user data as needed for your test case
      };
      const clientKey = 'your_client_key';

      // Call the generateSessionTokens method
      const result = await authTokenHelper.generateSessionTokens(user, clientKey);

      // Verify that the getUserInfoForJwt and createSignedToken methods were called with the correct parameters
      expect(authTokenHelper.getUserInfoForJwt).toHaveBeenCalledWith(user, clientKey);
      expect(authTokenHelper.createSignedToken).toHaveBeenCalledWith({
        id: '12345',
        email: '<EMAIL>',
        sessionId: 'session123',
        roles: ['user'],
      });

      // Verify that the result contains the expected idToken and userData
      expect(result).toEqual({
        idToken: 'mocked_token',
        userData: {
          id: '12345',
          email: '<EMAIL>',
          sessionId: 'session123',
          roles: ['user'],
        },
      });
    });
  });
});
