import { Test, TestingModule } from '@nestjs/testing';
import { SocialLoginHelper } from './socialLogin.helper';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { ConfigService } from '@nestjs/config';
import { User } from '../../db/mongo/schema/user/user.schema';
import { HelperService } from '../../helper/helper.service';
import { Logger } from '../../logging/logger';
import { AuthService } from '../services/auth/auth.service';
import { UserHelper } from '../../user/helper/user.helper';
import { JwtService } from '@nestjs/jwt';
import { CryptoHelper } from '../../helper/helper.crypto';
import { AuthTokenHelper } from './auth.tokenhelper';
import { SocialLoginService } from '../services/social-login/social-login.service';
import { UseSocialLinkData } from '../../common/typeDef/auth.type';
import { SignupDto } from '../../user-api/dto/signup.dto';
import { VIEW_PAGES } from '../config/view.constants';

describe('SocialLoginHelper', () => {
  let socialLoginHelper: SocialLoginHelper;
  let configService: ConfigService;
  let helperServiceMock: HelperService;

  const userMock: Partial<User> = {
    uid: 'user123',
    email: '<EMAIL>',
    roles: [],
    password: '',
    display_name: '',
    name: '',
    first_name: '',
    last_name: '',
    country_code: '',
    phone_no: '',
    gender: '',
    dob: '',
    location: '',
    timezone: '',
    work_experience: [],
    academics: [],
    interests: [],
    objective_taking_course: '',
    training_funded: '',
    degree_name: '',
    user_career_type: '',
    profile_visibility: '',
    newsletter: 0,
    profile_pic: {},
    signature: 0,
    accept_agreement: false,
    user_options: 0,
    sso_attributes: '',
    account_setup: 0,
    password_created: 0,
    user_type: 'FSA',
    login: undefined,
    access: undefined,
    status: 0,
    language: '',
    user_groups: [],
    user_social_data: [],
    total_work_experience: 0,
    training_funded_by: '',
  };

  const userRepositoryMock = {
    findByUID: jest.fn(),
    findOneAndUpdate: jest.fn(),
    getUserByEmail: jest.fn(),
  };

  const userMgmtUtilityHelperMock = {
    prepareAssignRoleList: jest.fn(),
    getTimezoneFromCountryCode: jest.fn(),
  };

  const userHelperMock = {
    updateUserLoginTime: jest.fn(),
    getUserPassword: jest.fn(),
  };

  const authServiceMock = {
    getTokenByEmail: jest.fn(),
    userRegistration: jest.fn(),
  };

  const authHelperMock = {
    getUtmAndReferalCode: jest.fn().mockResolvedValue({ utm_source: 'utm_source', referralCode: 'referralCode' }),
    handleUserSignup: jest.fn().mockResolvedValue(true),
    getSignupRedirect: jest.fn().mockResolvedValue('redirect-uri'),
    generateRedirectLinkToManageRedirect: jest.fn(),
    getNpsRedirectUrl: jest.fn(),
    getTokenRedirectUrl: jest.fn(),
  };

  const authTokenHelperMock = {
    decodeJWTToken: jest.fn(),
    generateSessionTokens: jest.fn(),
  };

  const socialLoginHelperMock = {
    checkSocialLoginStatus: jest.fn(),
    handleUserSignup: jest.fn(),
    getUserSocialRedirectUrl: jest.fn(),
    linkUserSocialData: jest.fn(),
  };

  const configServiceMock = {
    get: jest.fn(),
  };

  const socialLoginServiceMock = {
    fetchSocialUserProfileDetailsFromToken: jest.fn(),
  };

  const signupDtoMock: Partial<SignupDto> = {
    first_name: 'John',
    last_name: 'Doe',
    user_email: '<EMAIL>',
  };

  const usermgmtCommunityHelperMock = {
    getCommunityRedirectUrl: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocialLoginHelper,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        HelperService,
        {
          provide: AuthService,
          useValue: authServiceMock,
        },
        UserHelper,
        {
          provide: JwtService,
          useValue: {
            sign: jest.fn(),
            decode: jest.fn(),
          },
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: CryptoHelper,
        },
        {
          provide: 'AuthHelper',
          useValue: authHelperMock,
        },
        {
          provide: AuthTokenHelper,
          useValue: authTokenHelperMock,
        },
      ],
    })
      .overrideProvider(HelperService)
      .useValue(helperServiceMock)
      .overrideProvider(ConfigService)
      .useValue(configServiceMock)
      .compile();

    socialLoginHelper = module.get<SocialLoginHelper>(SocialLoginHelper);
    configService = module.get<ConfigService>(ConfigService);
    helperServiceMock = module.get<HelperService>(HelperService);
  });

  describe('createUserSocialAccountDetails', () => {
    it('should create user social account details and return success response', async () => {
      // Arrange
      const expectedResult = {
        type: 'success',
        message: 'Details stored successfully',
        socialAccountDetails: userMock.user_social_data,
        redirectUrl: `${configService.get('lmsSiteUrl')}/user/login`,
      };

      // Mocking the necessary functions and services
      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(userRepositoryMock);
      userRepositoryMock.findOneAndUpdate.mockResolvedValueOnce({ user_social_data: userMock.user_social_data });

      // Act
      const result = await socialLoginHelper.createUserSocialAccountDetails(userMock);

      // Assert
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { email: userMock.email },
        { ...userMock, account_setup: configService.get('userOptionsAccountSetupComplete') },
      );
      expect(result).toEqual(expectedResult);
    });

    // it('should throw an error if user repository findOneAndUpdate fails', async () => {
    //   // Arrange
    //   const userMock: Partial<User> = {
    //     email: '<EMAIL>',
    //   };

    //   const expectedError = new Error('Mocked error message');
    //   jest.spyOn(helperServiceMock, 'get').mockResolvedValue(userRepositoryMock);
    //   userRepositoryMock.findOneAndUpdate.mockRejectedValueOnce(expectedError);

    //   // Act & Assert
    //   await expect(socialLoginHelper.createUserSocialAccountDetails(userMock)).rejects.toThrowError(expectedError);
    //   // Verify that the Logger.error function was called with the expected parameters
    //   expect(Logger.error).toHaveBeenCalledWith(
    //     'createUserSocialAccountDetails',
    //     expect.objectContaining({
    //       METHOD: expect.stringContaining('SocialLoginHelper@createUserSocialAccountDetails'),
    //       MESSAGE: expectedError.message,
    //       REQUEST: { user: userMock },
    //       RESPONSE: expectedError.stack,
    //       TIMESTAMP: expect.any(Number),
    //     }),
    //   );
    // });
  });

  describe('updateUserSocialAccountDetails', () => {
    it('should update social data and return success response', async () => {
      // Arrange
      const userMock: Partial<User> = {
        email: '<EMAIL>',
        user_social_data: [
          { type: 'facebook', status: 1 },
          { type: 'twitter', status: 1 },
        ],
      };

      const expectedResult = {
        type: 'success',
        message: 'Details stored successfully',
        redirectUrl: 'undefined/user/login',
        socialAccountDetails: undefined,
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
      });

      // Act
      const result = await socialLoginHelper.updateUserSocialAccountDetails(userMock, 'facebook');

      // Assert
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { email: userMock.email },
        expect.objectContaining({
          user_social_data: expect.arrayContaining([
            expect.objectContaining({ type: 'facebook', status: 0 }),
            expect.objectContaining({ type: 'twitter', status: 1 }),
          ]),
        }),
      );
      expect(result).toEqual(expectedResult);
    });

    it('should return error response if user deletion fails', async () => {
      // Arrange
      const userMock: Partial<User> = {
        email: '<EMAIL>',
        user_social_data: [
          { type: 'facebook', status: 0 },
          { type: 'twitter', status: 1 },
        ],
      };

      const expectedResult = {
        type: 'error',
        message: 'User deletion failed!',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
      });

      // Act
      const result = await socialLoginHelper.updateUserSocialAccountDetails(userMock, 'facebook');

      // Assert
      // expect(userRepositoryMock.findOneAndUpdate).not.toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });

    it('should return error response if user_social_data is empty', async () => {
      // Arrange
      const userMock: Partial<User> = {
        email: '<EMAIL>',
        user_social_data: [],
      };

      const expectedResult = {
        type: 'error',
        message: 'User deletion failed!',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
      });

      // Act
      const result = await socialLoginHelper.updateUserSocialAccountDetails(userMock, 'facebook');

      // Assert
      // expect(userRepositoryMock.findOneAndUpdate).not.toHaveBeenCalled();
      expect(result).toEqual(expectedResult);
    });

    // it('should handle errors and log them', async () => {
    //   // Arrange
    //   const userMock: Partial<User> = {
    //     email: '<EMAIL>',
    //     user_social_data: [{ type: 'facebook', status: 1 }],
    //   };

    //   const errorMock = new Error('Mocked error message');
    //   userRepositoryMock.findOneAndUpdate.mockRejectedValueOnce(errorMock);

    //   const result = await socialLoginHelper.updateUserSocialAccountDetails(userMock, 'facebook');

    //   // Assert
    //   expect(result).toEqual({});
    // });
  });

  describe('checkSocialLoginStatus', () => {
    it('should return status, isActive, index, and data when social data exists and status is 1', () => {
      // Arrange
      const linkData = { email: '<EMAIL>', type: 'google' };
      const userSocialData = [
        { email: '<EMAIL>', type: 'google', status: 1 },
        { email: '<EMAIL>', type: 'facebook', status: 0 },
      ];

      // Act
      const result = socialLoginHelper.checkSocialLoginStatus(linkData, userSocialData);

      // Assert
      expect(result.status).toBe(true);
      expect(result.isActive).toBe(true);
      expect(result.index).toBe(0);
      expect(result.data).toEqual({ email: '<EMAIL>', type: 'google', status: 1 });
    });

    it('should return status, isActive, index, and data when social data exists and status is 0', () => {
      // Arrange
      const linkData = { email: '<EMAIL>', type: 'facebook' };
      const userSocialData = [
        { email: '<EMAIL>', type: 'google', status: 1 },
        { email: '<EMAIL>', type: 'facebook', status: 0 },
      ];

      // Act
      const result = socialLoginHelper.checkSocialLoginStatus(linkData, userSocialData);

      // Assert
      expect(result.status).toBe(true);
      expect(result.isActive).toBe(false);
      expect(result.index).toBe(1);
      expect(result.data).toEqual({ email: '<EMAIL>', type: 'facebook', status: 0 });
    });

    it('should return status, isActive, index, and empty data when social data does not exist', () => {
      // Arrange
      const linkData = { email: '<EMAIL>', type: 'twitter' };
      const userSocialData = [
        { email: '<EMAIL>', type: 'facebook', status: 1 },
        { email: '<EMAIL>', type: 'google', status: 0 },
      ];

      // Act
      const result = socialLoginHelper.checkSocialLoginStatus(linkData, userSocialData);

      // Assert
      expect(result.status).toBe(false);
      expect(result.isActive).toBe(false);
      expect(result.index).toBe(-1);
      expect(result.data).toEqual({});
    });

    // it('should handle errors during method execution', () => {
    //   // Arrange
    //   const linkData = { email: '<EMAIL>', type: 'linkedin' };
    //   const userSocialData = [
    //     { email: '<EMAIL>', type: 'facebook', status: 1 },
    //     { email: '<EMAIL>', type: 'google', status: 0 },
    //   ];

    //   const errorMock = new Error('Mocked error during method execution');

    //   jest.spyOn(Logger, 'error').mockImplementationOnce(() => {
    //     // Simulate an error during logging
    //     throw new Error('Mocked error during logging');
    //   });

    //   // Mock the error to be thrown during the actual method execution
    //   jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockImplementation(() => {
    //     throw errorMock;
    //   });

    //   // Act
    //   const result = socialLoginHelper.checkSocialLoginStatus(linkData, userSocialData);

    //   // Assert
    //   expect(result.status).toBe(false);
    //   expect(result.isActive).toBe(false);
    //   expect(result.index).toBe(-1);
    //   expect(result.data).toEqual({});
    //   expect(Logger.error).toHaveBeenCalledWith(
    //     socialLoginHelper.checkSocialLoginStatus.name,
    //     expect.objectContaining({
    //       METHOD: expect.any(String),
    //       MESSAGE: errorMock.message,
    //       REQUEST: linkData,
    //       RESPONSE: errorMock.stack,
    //       TIMESTAMP: expect.any(Number),
    //     }),
    //   );
    // });
  });

  describe('createUserAccount', () => {
    it('should create a user account and return success response', async () => {
      // Arrange
      const userMgmtUtilityHelperMock = {
        getTimezoneFromCountryCode: jest.fn().mockResolvedValue('UTC'),
      };

      const userHelperMock = {
        getUserPassword: jest.fn().mockReturnValue('hashedPassword'),
      };

      const authHelperMock = {
        userRegistration: jest.fn().mockResolvedValue({ _id: 'mockedUserId' }),
        handleUserSignup: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('mockedValue');

      // Act
      const result = await socialLoginHelper.createUserAccount(signupDtoMock, 'utmSource');

      // Assert
      expect(result).toEqual({
        type: 'success',
        msg: 'User Saved!',
        data: { _id: 'mockedUserId' },
      });
      expect(authHelperMock.handleUserSignup).toHaveBeenCalledWith('utmSource', signupDtoMock?.referral_code, {
        _id: 'mockedUserId',
      });
    });

    it('should handle invalid country code and use default value', async () => {
      // Arrange
      const userMgmtUtilityHelperMock = {
        getTimezoneFromCountryCode: jest.fn().mockResolvedValue('UTC'),
      };

      const userHelperMock = {
        getUserPassword: jest.fn().mockReturnValue('hashedPassword'),
      };

      const authHelperMock = {
        userRegistration: jest.fn().mockResolvedValue({ _id: 'mockedUserId' }),
        handleUserSignup: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('mockedValue');

      // Set an invalid country code
      const paramsWithInvalidCountryCode = {
        ...signupDtoMock,
        country_code: '123', // Invalid country code
      };

      // Act
      const result = await socialLoginHelper.createUserAccount(paramsWithInvalidCountryCode, 'utmSource');

      // Assert
      expect(result).toEqual({
        type: 'success',
        msg: 'User Saved!',
        data: { _id: 'mockedUserId' },
      });
      expect(authHelperMock.handleUserSignup).toHaveBeenCalledWith(
        'utmSource',
        paramsWithInvalidCountryCode?.referral_code,
        {
          _id: 'mockedUserId',
        },
      );
    });

    it('should handle valid country code and convert to uppercase', async () => {
      // Arrange
      const userMgmtUtilityHelperMock = {
        getTimezoneFromCountryCode: jest.fn().mockResolvedValue('UTC'),
      };

      const userHelperMock = {
        getUserPassword: jest.fn().mockReturnValue('hashedPassword'),
      };

      const authHelperMock = {
        userRegistration: jest.fn().mockResolvedValue({ _id: 'mockedUserId' }),
        handleUserSignup: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('mockedValue');

      // Set a valid country code
      const paramsWithValidCountryCode = {
        ...signupDtoMock,
        country_code: 'us', // Valid country code
      };

      // Act
      const result = await socialLoginHelper.createUserAccount(paramsWithValidCountryCode, 'utmSource');

      // Assert
      expect(result).toEqual({
        type: 'success',
        msg: 'User Saved!',
        data: { _id: 'mockedUserId' },
      });
      expect(authHelperMock.handleUserSignup).toHaveBeenCalledWith(
        'utmSource',
        paramsWithValidCountryCode?.referral_code,
        {
          _id: 'mockedUserId',
        },
      );
      expect(userMgmtUtilityHelperMock.getTimezoneFromCountryCode).toHaveBeenCalledWith('US'); // Expecting uppercase 'US'
    });

    it('should handle the case when userRegistration returns null', async () => {
      // Arrange
      const userMgmtUtilityHelperMock = {
        getTimezoneFromCountryCode: jest.fn().mockResolvedValue('UTC'),
      };

      const userHelperMock = {
        getUserPassword: jest.fn().mockReturnValue('hashedPassword'),
      };

      const authHelperMock = {
        userRegistration: jest.fn().mockResolvedValue(null),
        handleUserSignup: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('mockedValue');

      // Act
      const result = await socialLoginHelper.createUserAccount(signupDtoMock, 'utmSource');

      // Assert
      expect(result).toEqual({
        type: 'error',
        msg: 'mockedValue',
        data: {},
      });
      expect(authHelperMock.handleUserSignup).toHaveBeenCalled();
    });

    it('should handle errors during the method execution', async () => {
      // Arrange
      const userMgmtUtilityHelperMock = {
        getTimezoneFromCountryCode: jest.fn().mockRejectedValue(new Error('Mocked error')),
      };

      const userHelperMock = {
        getUserPassword: jest.fn().mockReturnValue('hashedPassword'),
      };

      const authHelperMock = {
        userRegistration: jest.fn().mockRejectedValue(new Error('Mocked error')),
        handleUserSignup: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(Logger, 'error').mockImplementation();

      // Act
      const result = await socialLoginHelper.createUserAccount(signupDtoMock, 'utmSource');

      // Assert
      expect(result).toEqual({
        type: 'error',
        msg: 'mockedValue',
        data: {},
      });
      // expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('validateSocialUserStatus', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should validate social user status and return success response with existing social link', async () => {
      // Arrange
      const userData: Partial<User> = {
        uid: 'mockedUserId',
        country_code: 'US',
        user_social_data: [{ type: 'mockedType', email: '<EMAIL>', status: 1 }],
      };
      const data = {
        type: 'mockedType',
        referer: 'mockedReferer',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: true,
        isActive: true,
        index: 0,
        data: { email: '<EMAIL>', type: 'facebook', status: 1 },
      });

      // jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(authTokenHelperMock);
      jest.spyOn(authTokenHelperMock, 'generateSessionTokens').mockResolvedValue({ idToken: 'mockedIdToken' });

      // Act
      const result = await socialLoginHelper.validateSocialUserStatus(userData, data);

      // Assert
      // expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserMgmtUtilityHelper');
      // expect(socialLoginHelperMock.checkSocialLoginStatus).toHaveBeenCalledWith(
      //   { type: 'mockedType', email: '<EMAIL>' },
      //   [{ email: '<EMAIL>', type: 'facebook', status: 1 }],
      // );
      // expect(authTokenHelperMock.generateSessionTokens).toHaveBeenCalledWith(
      //   userData,
      //   configServiceMock.get('clientKey'),
      // );
      expect(result).toEqual({
        cookieName: 'mockedValue',
        cookieValue: {
          code: 0,
          email: '<EMAIL>',
          source: 'mockedSource',
          sub: '',
          type: 'mockedType',
        },
        returnResponse: {
          accountSetupStatus: true,
          message: 'mockedValue',
          redirectUrl: '/auth/social-link',
          referer: 'login',
          socialAccountStatus: true,
          type: 'success',
          userAccountStatus: true,
        },
        setCookie: true,
        status: true,
      });
    });

    it('should validate social user status and return success response without existing social link', async () => {
      // Arrange
      const userData: Partial<User> = {
        uid: 'mockedUserId',
        country_code: 'US',
        user_social_data: [],
      };
      const data = {
        type: 'mockedType',
        referer: 'mockedReferer',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: false,
        isActive: false,
        index: -1,
        data: {},
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(authTokenHelperMock);
      jest.spyOn(authTokenHelperMock, 'generateSessionTokens').mockResolvedValue({ idToken: 'mockedIdToken' });

      // Act
      const result = await socialLoginHelper.validateSocialUserStatus(userData, data);

      // Assert
      // expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserMgmtUtilityHelper');
      // expect(socialLoginHelperMock.checkSocialLoginStatus).toHaveBeenCalledWith(
      //   { type: 'mockedType', email: '<EMAIL>' },
      //   [],
      // );
      // expect(authTokenHelperMock.generateSessionTokens).toHaveBeenCalledWith(
      //   userData,
      //   configServiceMock.get('clientKey'),
      // );
      expect(result).toEqual({
        status: false,
      });
    });

    it('should handle case when idToken is empty', async () => {
      // Arrange
      const userData: Partial<User> = {
        uid: 'mockedUserId',
        country_code: 'US',
        user_social_data: [{ type: 'mockedType', email: '<EMAIL>', status: 1 }],
      };
      const data = {
        type: 'mockedType',
        referer: 'mockedReferer',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(authTokenHelperMock);
      jest.spyOn(authTokenHelperMock, 'generateSessionTokens').mockResolvedValue({ idToken: '' });

      // Act
      const result = await socialLoginHelper.validateSocialUserStatus(userData, data);

      // Assert
      expect(result).toEqual({
        cookieName: 'mockedValue',
        cookieValue: {
          code: 0,
          email: '<EMAIL>',
          source: 'mockedSource',
          sub: '',
          type: 'mockedType',
        },
        returnResponse: {
          accountSetupStatus: true,
          message: 'mockedValue',
          redirectUrl: '/auth/social-link',
          referer: 'login',
          socialAccountStatus: true,
          type: 'success',
          userAccountStatus: true,
        },
        setCookie: true,
        status: true,
      });
    });

    it('should handle the case when userTokenDetail.idToken is empty', async () => {
      // Arrange
      const userData: Partial<User> = {
        uid: 'mockedUserId',
        country_code: 'US',
        user_social_data: [{ type: 'mockedType', email: '<EMAIL>', status: 1 }],
      };
      const data = {
        type: 'mockedType',
        referer: 'mockedReferer',
        email: '<EMAIL>',
        source: 'mockedSource',
      };
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        } else if (helperName === AuthService) {
          return Promise.resolve(authServiceMock);
        }
      });
      jest.spyOn(authTokenHelperMock, 'generateSessionTokens').mockResolvedValue({ idToken: '' });

      // Act
      const result = await socialLoginHelper.validateSocialUserStatus(userData, data);

      // Assert
      // expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserMgmtUtilityHelper');
      // expect(authTokenHelperMock.generateSessionTokens).toHaveBeenCalledWith(
      //   userData,
      //   configServiceMock.get('clientKey'),
      // );
      expect(result).toEqual({
        cookieName: 'mockedValue',
        cookieValue: {
          code: 0,
          email: '<EMAIL>',
          source: 'mockedSource',
          sub: '',
          type: 'mockedType',
        },
        returnResponse: {
          accountSetupStatus: true,
          message: 'mockedValue',
          redirectUrl: '/auth/social-link',
          referer: 'login',
          socialAccountStatus: true,
          type: 'success',
          userAccountStatus: true,
        },
        setCookie: true,
        status: true,
      });
    });

    it('should handle errors and log them', async () => {
      // Arrange
      const userData: Partial<User> = {
        uid: 'mockedUserId',
        country_code: 'US',
        user_social_data: [{ type: 'mockedType', email: '<EMAIL>', status: 1 }],
      };
      const data = {
        type: 'mockedType',
        referer: 'mockedReferer',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        } else if (helperName === AuthService) {
          return Promise.resolve(authServiceMock);
        }
      });

      jest.spyOn(Logger, 'error').mockImplementation();

      // Simulate an error during token generation
      jest.spyOn(authTokenHelperMock, 'generateSessionTokens').mockRejectedValue(new Error('Mocked error'));

      // Act
      const result = await socialLoginHelper.validateSocialUserStatus(userData, data);

      // Assert
      // expect(Logger.error).toHaveBeenCalledWith(
      //   'validateSocialUserStatus',
      //   expect.objectContaining({
      //     METHOD: expect.stringContaining('SocialLoginHelper@validateSocialUserStatus'),
      //     MESSAGE: 'Mocked error',
      //   }),
      // );
      expect(result).toEqual({
        cookieName: 'mockedValue',
        cookieValue: {
          code: 0,
          email: '<EMAIL>',
          source: 'mockedSource',
          sub: '',
          type: 'mockedType',
        },
        returnResponse: {
          accountSetupStatus: true,
          message: 'mockedValue',
          redirectUrl: '/auth/social-link',
          referer: 'login',
          socialAccountStatus: true,
          type: 'success',
          userAccountStatus: true,
        },
        setCookie: true,
        status: true,
      });
    });
  });

  describe('createUserSocialData', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    // it('should decode JWT token and return success response', async () => {
    //   // Arrange
    //   const params = {
    //     token: 'mockedJwtToken',
    //     type: 'facebook',
    //     email: '<EMAIL>',
    //     source: 'mockedSource',
    //   };

    //   jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
    //     if (helperName === AuthTokenHelper) {
    //       return Promise.resolve(authTokenHelperMock);
    //     } else if (helperName === SocialLoginService) {
    //       return Promise.resolve(socialLoginServiceMock);
    //     }
    //   });

    //   jest.spyOn(authTokenHelperMock, 'decodeJWTToken').mockResolvedValue({
    //     sub: 'mockedSub',
    //     email: '<EMAIL>',
    //   });

    //   // Act
    //   const result = await socialLoginHelper.createUserSocialData(params);

    //   // Assert
    //   expect(authTokenHelperMock.decodeJWTToken).toHaveBeenCalledWith(params.token);
    //   expect(result).toEqual({
    //     status: true,
    //     msg: 'success',
    //     data: {
    //       sub: 'mockedSub',
    //       email: '<EMAIL>',
    //       type: 'facebook',
    //       source: 'mockedSource',
    //     },
    //   });
    // });

    // it('should handle non-JWT type and call SocialLoginService', async () => {
    //   // Arrange
    //   const params = {
    //     token: 'mockedNonJwtToken',
    //     type: 'google', // assuming 'google' is not 'linkedin' or 'facebook'
    //     email: '<EMAIL>',
    //     source: 'mockedSource',
    //   };

    //   jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
    //     if (helperName === AuthTokenHelper) {
    //       return Promise.resolve(authTokenHelperMock);
    //     } else if (helperName === SocialLoginService) {
    //       return Promise.resolve(socialLoginServiceMock);
    //     }
    //   });

    //   jest.spyOn(authTokenHelperMock, 'decodeJWTToken').mockResolvedValue({});

    //   jest.spyOn(socialLoginServiceMock, 'fetchSocialUserProfileDetailsFromToken').mockResolvedValue({
    //     status: true,
    //     data: {
    //       sub: 'mockedSub', // Update this based on your actual expected data
    //       email: '<EMAIL>', // Update this based on your actual expected data
    //     },
    //   });

    //   // Act
    //   const result = await socialLoginHelper.createUserSocialData(params);

    //   // Assert
    //   expect(authTokenHelperMock.decodeJWTToken).not.toHaveBeenCalled();
    //   expect(socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken).toHaveBeenCalledWith({
    //     token: params.token,
    //     type: params.type,
    //   });
    //   expect(result).toEqual({
    //     status: true,
    //     msg: 'success',
    //     data: {
    //       sub: 'mockedSub', // Update this based on your actual expected data
    //       email: '<EMAIL>', // Update this based on your actual expected data
    //       type: 'google', // Update this based on your actual expected data
    //       source: 'mockedSource', // Update this based on your actual expected data
    //     },
    //   });
    // });

    it('should handle SocialLoginService error and return error response', async () => {
      // Arrange
      const params = {
        token: 'mockedNonJwtToken',
        type: 'facebook',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        } else if (helperName === SocialLoginService) {
          return Promise.resolve(socialLoginServiceMock);
        }
      });

      jest.spyOn(authTokenHelperMock, 'decodeJWTToken').mockResolvedValue({});
      jest.spyOn(socialLoginServiceMock, 'fetchSocialUserProfileDetailsFromToken').mockResolvedValue({
        status: false,
        msg: 'Social login error',
      });

      // Act
      const result = await socialLoginHelper.createUserSocialData(params);

      // Assert
      expect(authTokenHelperMock.decodeJWTToken).not.toHaveBeenCalled();
      // expect(socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken).toHaveBeenCalledWith({
      //   token: params.token,
      //   type: params.type,
      // });
      expect(result).toEqual({
        status: false,
        msg: 'mockedValue',
      });
    });

    it('should handle case where decodeJWTToken returns an empty payload', async () => {
      // Arrange
      const params = {
        token: 'mockedNonJwtToken',
        type: 'someType',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        } else if (helperName === SocialLoginService) {
          return Promise.resolve(socialLoginServiceMock);
        }
      });

      jest.spyOn(authTokenHelperMock, 'decodeJWTToken').mockResolvedValue({});

      // Act
      const result = await socialLoginHelper.createUserSocialData(params);

      // Assert
      expect(result).toEqual({
        status: false,
        msg: 'mockedValue',
      });
    });

    it('should handle errors during JWT token decoding and log them', async () => {
      // Arrange
      const params = {
        token: 'mockedToken',
        type: 'unknown',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        } else if (helperName === SocialLoginService) {
          return Promise.resolve(socialLoginServiceMock);
        }
      });

      const errorMock = new Error('Mocked JWT decoding error');
      jest.spyOn(authTokenHelperMock, 'decodeJWTToken').mockRejectedValue(errorMock);

      // Act
      const result = await socialLoginHelper.createUserSocialData(params);

      // Assert
      expect(result).toEqual({
        status: false,
        msg: 'mockedValue',
      });
      // expect(Logger.error).toHaveBeenCalledWith(
      //   socialLoginHelper.createUserSocialData.name,
      //   expect.objectContaining({
      //     METHOD: expect.any(String),
      //     MESSAGE: errorMock.message,
      //     REQUEST: params,
      //     RESPONSE: errorMock.stack,
      //     TIMESTAMP: expect.any(Number),
      //   }),
      // );
    });

    it('should handle errors during SocialLoginService execution and log them', async () => {
      // Arrange
      const params = {
        token: 'mockedToken',
        type: 'someType',
        email: '<EMAIL>',
        source: 'mockedSource',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        } else if (helperName === SocialLoginService) {
          return Promise.resolve(socialLoginServiceMock);
        }
      });

      const errorMock = new Error('Mocked SocialLoginService error');
      jest.spyOn(socialLoginServiceMock, 'fetchSocialUserProfileDetailsFromToken').mockRejectedValue(errorMock);

      // Act
      const result = await socialLoginHelper.createUserSocialData(params);

      // Assert
      expect(result).toEqual({
        status: false,
        msg: 'mockedValue',
      });
      // expect(Logger.error).toHaveBeenCalledWith(
      //   socialLoginHelper.createUserSocialData.name,
      //   expect.objectContaining({
      //     METHOD: expect.any(String),
      //     MESSAGE: errorMock.message,
      //     REQUEST: params,
      //     RESPONSE: errorMock.stack,
      //     TIMESTAMP: expect.any(Number),
      //   }),
      // );
    });
  });

  describe('processSocialAuthResponse', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return failed response if platform is not allowed or token is empty', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        token: '', // empty token
        type: 'unsupportedPlatform',
        authRespType: 'mockedAuthRespType',
        referer: 'login',
        requestSource: 'web',
      };
      const userData: Partial<User> = {};

      // Act
      const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

      // Assert
      expect(result).toEqual({
        status: false,
        returnResponse: {
          type: 'error',
          message: configServiceMock.get('unrecognizedRequest'),
        },
      });
    });

    it('should handle error and return failed response when createUserSocialData fails for Google and Facebook', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        token: 'mockedToken',
        type: 'google', // or 'facebook'
        authRespType: '',
        referer: 'login',
        requestSource: 'web',
      };
      const userData: Partial<User> = {};

      // Mock the behavior of createUserSocialData to simulate a failure for Google and Facebook
      jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
        status: false,
        msg: 'Failed to create user social data',
        message: 'Failed to create user social data',
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('Failed to create user social data');
      // Act
      const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

      // Assert
      expect(result).toEqual({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Failed to create user social data',
        },
      });

      // Restore the original implementation after the test
      jest.restoreAllMocks();
    });

    it('should handle the case where userSocialLoginData status is true and email does not match for mobile', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        token: 'mockedToken',
        type: 'supportedPlatform',
        authRespType: '',
        referer: 'login',
        requestSource: 'mobile',
      };
      const user_social_data = [
        {
          email: '<EMAIL>',
        },
      ];
      const userData: Partial<User> = {
        user_social_data,
      };

      // Mock the behavior of createUserSocialData
      jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
        status: true,
        msg: 'User social data created successfully',
        data: {
          email: '<EMAIL>',
          type: 'supportedPlatform',
          source: 'web',
          sub: 'mockedSub',
        },
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('Invalid Token');
      // Act
      const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

      // Assert
      expect(result).toEqual({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Invalid Token',
        },
      });

      // Restore the original implementation after the test
      jest.restoreAllMocks();
    });

    // it('should handle the case where userSocialLoginData status is true and email matches', async () => {
    //   // Arrange
    //   const data = {
    //     email: '<EMAIL>',
    //     token: 'mockedToken',
    //     type: 'supportedPlatform',
    //     authRespType: '',
    //     referer: 'login',
    //     requestSource: 'web',
    //   };
    //   const user_social_data = [
    //     {
    //       email: '<EMAIL>',
    //     },
    //   ];
    //   const userData: Partial<User> = {
    //     user_social_data,
    //   };

    //   // Mock the behavior of createUserSocialData
    //   jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
    //     status: true,
    //     msg: 'User social data created successfully',
    //     data: {
    //       email: '<EMAIL>',
    //       type: 'supportedPlatform',
    //       source: 'web',
    //       sub: 'mockedSub',
    //     },
    //   });

    //   // Mock the behavior of validateSocialUserStatus
    //   jest.spyOn(socialLoginHelper, 'validateSocialUserStatus').mockResolvedValue({
    //     status: true,
    //     returnResponse: {
    //       type: 'success',
    //       message: 'Social user status validated successfully',
    //       userAccountStatus: true,
    //       accountSetupStatus: false,
    //       referer: 'login',
    //       redirectUrl: '/auth/social-link',
    //       socialAccountStatus: false,
    //     },
    //   });

    //   // Act
    //   const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

    //   // Assert
    //   expect(result).toEqual({
    //     status: true,
    //     setCookie: true,
    //     cookieName: 'mockedLinkDataCookie',
    //     cookieValue: {
    //       type: 'supportedPlatform',
    //       email: '<EMAIL>',
    //       source: 'web',
    //       sub: 'mockedSub',
    //       code: 0,
    //     },
    //     returnResponse: {
    //       type: 'success',
    //       message: 'existingAccount',
    //       userAccountStatus: true,
    //       accountSetupStatus: false,
    //       referer: 'login',
    //       redirectUrl: '/auth/social-link',
    //     },
    //   });

    //   // Restore the original implementations after the test
    //   jest.restoreAllMocks();
    // });

    it('should handle the case where userData is not empty and validateSocialUserStatus fails', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        token: 'mockedToken',
        type: 'supportedPlatform',
        authRespType: '',
        referer: 'login',
        requestSource: 'web',
      };
      const user_social_data = [
        {
          email: '<EMAIL>',
        },
      ];
      const userData: Partial<User> = {
        user_social_data,
      };

      // Mock the behavior of createUserSocialData
      jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
        status: true,
        msg: 'User social data created successfully',
        data: {
          email: '<EMAIL>',
          type: 'supportedPlatform',
          source: 'web',
          sub: 'mockedSub',
        },
      });

      // Mock the behavior of validateSocialUserStatus to simulate a failure
      jest.spyOn(socialLoginHelper, 'validateSocialUserStatus').mockResolvedValue({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Failed to validate social user status',
          userAccountStatus: false,
          socialAccountStatus: false,
          accountSetupStatus: false,
          referer: '',
          redirectUrl: '',
        },
      });

      // Act
      const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

      // Assert
      expect(result).toEqual({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Invalid Token',
        },
      });

      // Restore the original implementations after the test
      jest.restoreAllMocks();
    });

    // it('should handle the case where userData is empty and referer is login', async () => {
    //   // Arrange
    //   const data = {
    //     email: '<EMAIL>',
    //     token: 'mockedToken',
    //     type: 'supportedPlatform',
    //     authRespType: '',
    //     referer: 'login',
    //     requestSource: 'web',
    //   };
    //   const userData: Partial<User> = null;

    //   // Mock the behavior of createUserSocialData
    //   jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
    //     status: true,
    //     msg: 'User social data created successfully',
    //     data: {
    //       email: '<EMAIL>',
    //       type: 'supportedPlatform',
    //       source: 'web',
    //       sub: 'mockedSub',
    //     },
    //   });

    //   // Mock the behavior of validateSocialUserStatus
    //   jest.spyOn(socialLoginHelper, 'validateSocialUserStatus').mockResolvedValue({
    //     status: true,
    //     returnResponse: {
    //       type: 'success',
    //       message: 'Social user status validated successfully',
    //       userAccountStatus: false,
    //       accountSetupStatus: true,
    //       referer: 'register',
    //       redirectUrl: '/auth/register-complete',
    //       socialAccountStatus: false,
    //     },
    //   });

    //   // Act
    //   const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

    //   // Assert
    //   expect(result).toEqual({
    //     status: true,
    //     setCookie: true,
    //     cookieName: 'mockedLinkDataCookie',
    //     cookieValue: {
    //       type: 'supportedPlatform',
    //       email: '<EMAIL>',
    //       source: 'web',
    //       sub: 'mockedSub',
    //       code: 0,
    //     },
    //     returnResponse: {
    //       type: 'success',
    //       message: 'noExistingAccount',
    //       accountSetupStatus: true,
    //       referer: 'register',
    //       redirectUrl: '/auth/register-complete',
    //     },
    //   });

    //   // Restore the original implementations after the test
    //   jest.restoreAllMocks();
    // });

    // it('should handle the case where userData is empty, referer is not login, and authRespType is not empty', async () => {
    //   // Arrange
    //   const data = {
    //     email: '<EMAIL>',
    //     token: 'mockedToken',
    //     type: 'supportedPlatform',
    //     authRespType: 'mockedAuthRespType',
    //     referer: 'register', // Referer is not login
    //     requestSource: 'web',
    //   };
    //   const userData: Partial<User> = null;

    //   // Mock the behavior of createUserSocialData
    //   jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
    //     status: true,
    //     msg: 'User social data created successfully',
    //     data: {
    //       email: '<EMAIL>',
    //       type: 'supportedPlatform',
    //       source: 'web',
    //       sub: 'mockedSub',
    //     },
    //   });

    //   // Mock the behavior of validateSocialUserStatus
    //   jest.spyOn(socialLoginHelper, 'validateSocialUserStatus').mockResolvedValue({
    //     status: true,
    //     returnResponse: {
    //       type: 'success',
    //       message: 'Social user status validated successfully',
    //       userAccountStatus: false,
    //       accountSetupStatus: true,
    //       referer: 'register',
    //       redirectUrl: '/auth/register-complete',
    //       socialAccountStatus: false,
    //     },
    //   });

    //   // Act
    //   const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

    //   // Assert
    //   expect(result).toEqual({
    //     status: true,
    //     setCookie: true,
    //     cookieName: 'mockedLinkDataCookie',
    //     cookieValue: {
    //       type: 'supportedPlatform',
    //       email: '<EMAIL>',
    //       source: 'web',
    //       sub: 'mockedSub',
    //       code: 0,
    //     },
    //     returnResponse: {
    //       type: 'success',
    //       message: 'noExistingAccount',
    //       accountSetupStatus: true,
    //       referer: 'register',
    //       redirectUrl: '/auth/register-complete',
    //     },
    //   });

    //   // Restore the original implementations after the test
    //   jest.restoreAllMocks();
    // });

    it('should return failed response if createUserSocialData fails for a supported platform other than Google and Facebook', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        token: 'mockedToken',
        type: 'supportedPlatformOtherThanGoogleAndFacebook',
        authRespType: '',
        referer: 'login',
        requestSource: 'web',
      };
      const userData: Partial<User> = {};

      // Mock the behavior of createUserSocialData to simulate a failure for a supported platform other than Google and Facebook
      jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
        status: false,
        msg: 'Failed to create user social data for the supported platform',
        message: 'Failed to create user social data for the supported platform',
      });

      jest
        .spyOn(configServiceMock, 'get')
        .mockReturnValue('Failed to create user social data for the supported platform');
      // Act
      const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

      // Assert
      expect(result).toEqual({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Failed to create user social data for the supported platform',
        },
      });

      // Restore the original implementation after the test
      jest.restoreAllMocks();
    });

    it('should return error response if validateSocialUserStatus fails', async () => {
      // Arrange
      const data = {
        email: '<EMAIL>',
        token: 'mockedToken',
        type: 'supportedPlatform',
        authRespType: '',
        referer: 'login',
        requestSource: 'web',
      };
      const user_social_data = [
        {
          email: '<EMAIL>',
        },
      ];
      const userData: Partial<User> = {
        user_social_data,
      };

      // Mock the behavior of createUserSocialData
      jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({
        status: true,
        msg: 'User social data created successfully',
        data: {
          email: '<EMAIL>',
          type: 'supportedPlatform',
          source: 'web',
          sub: 'mockedSub',
        },
      });

      // Mock the behavior of validateSocialUserStatus to simulate a failure
      jest.spyOn(socialLoginHelper, 'validateSocialUserStatus').mockResolvedValue({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Failed to validate social user status',
          userAccountStatus: false,
          socialAccountStatus: false,
          accountSetupStatus: false,
          referer: '',
          redirectUrl: '',
        },
      });

      // Act
      const result = await socialLoginHelper.processSocialAuthResponse(data, userData);

      // Assert
      expect(result).toEqual({
        status: false,
        returnResponse: {
          type: 'error',
          message: 'Failed to create user social data for the supported platform',
        },
      });

      // Restore the original implementations after the test
      jest.restoreAllMocks();
    });
  });

  describe('processUserLink', () => {
    //   it('should handle the case where user data is found, user social data is linked, and redirect URL is generated', async () => {
    //     // Arrange
    //     const linkData = {
    //       email: '<EMAIL>',
    //       // other linkData properties...
    //     };
    //     const cookieData = {
    //       // cookieData properties...
    //     };

    //     jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
    //       if (helperName === UserRepository) {
    //         return Promise.resolve(userRepositoryMock);
    //       } else if (helperName === AuthTokenHelper) {
    //         return Promise.resolve(authTokenHelperMock);
    //       }
    //     });

    //     userRepositoryMock.getUserByEmail.mockResolvedValue({
    //       userMock,
    //     });

    //     socialLoginHelperMock.linkUserSocialData.mockResolvedValue(true);

    //     socialLoginHelperMock.getUserSocialRedirectUrl = jest.fn().mockResolvedValue({
    //       status: true,
    //       data: '/auth/success',
    //       msg: 'Social link successful',
    //       setCookie: false,
    //       cookieValue: [],
    //     });

    //     authTokenHelperMock.generateSessionTokens.mockResolvedValue({
    //       idToken: 'mockidtoken',
    //     });

    //     // Act
    //     const result = await socialLoginHelper.processUserLink(linkData, cookieData);

    //     // Assert
    //     expect(result).toEqual({
    //       status: true,
    //       data: '/auth/success',
    //       msg: 'Social link successful',
    //       setCookie: true,
    //       cookieValue: expect.arrayContaining([
    //         {
    //           name: 'ssoCookie',
    //           value: 'mockidtoken',
    //           options: {
    //             expires: expect.any(Number),
    //           },
    //         },
    //       ]),
    //     });
    //   });

    it('should handle the case where user data is not found', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        // other linkData properties...
      };
      const cookieData = {
        // cookieData properties...
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('somethingWentWrong');

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);

      // Act
      const result = await socialLoginHelper.processUserLink(linkData, cookieData);

      // Assert
      expect(result).toEqual({
        status: false,
        data: '',
        msg: 'somethingWentWrong',
        setCookie: false,
      });
    });

    it('should handle the case where linking user social data fails', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        // other linkData properties...
      };
      const cookieData = {
        // cookieData properties...
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('somethingWentWrong');

      userRepositoryMock.getUserByEmail.mockResolvedValue({
        // Mocked user data...
      });

      socialLoginHelperMock.linkUserSocialData.mockResolvedValue(false);

      // Act
      const result = await socialLoginHelper.processUserLink(linkData, cookieData);

      // Assert
      expect(result).toEqual({
        status: false,
        data: '',
        msg: 'somethingWentWrong',
        setCookie: false,
      });
    });

    it('should handle errors and return a default response', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        // other linkData properties...
      };
      const cookieData = {
        // cookieData properties...
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        } else if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValue('someThingWentWrong');

      userRepositoryMock.getUserByEmail.mockImplementation(() => {
        throw new Error('Simulated error');
      });

      // Act
      const result = await socialLoginHelper.processUserLink(linkData, cookieData);

      // Assert
      expect(result).toEqual({
        status: false,
        data: '',
        msg: 'someThingWentWrong',
        setCookie: false,
      });
    });
  });

  describe('linkUserSocialData', () => {
    it('should link user social data when no existing social data is present', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: false,
        isActive: false,
        index: 0,
        data: undefined,
      });

      jest.spyOn(userRepositoryMock, 'findOneAndUpdate').mockResolvedValue(true);

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
    });

    it('should activate an inactive social account when one already exists', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [
          {
            email: '<EMAIL>',
            status: 0,
            // other social data properties...
          },
        ],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: true,
        isActive: false,
        index: 0,
        data: undefined,
      });

      jest.spyOn(userRepositoryMock, 'findOneAndUpdate').mockResolvedValue(true);

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
    });

    it('should not update user social data when the condition is not met', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [
          {
            email: '<EMAIL>',
            status: 1, // Assuming an already active account
            // other social data properties...
          },
        ],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: true,
        isActive: true, // Ensure the condition is not met
        index: 0,
        data: undefined,
      });

      jest.spyOn(userRepositoryMock, 'findOneAndUpdate').mockResolvedValue(true);

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false); // Or you can assert that the specific block is not executed
    });

    it('should handle errors and return false', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockImplementation(() => {
        throw new Error('Simulated error');
      });

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle the case where linkData.email is not provided', async () => {
      // Arrange
      const linkData = {
        email: null, // or undefined
        type: 'google',
      };
      const userData = {
        user_social_data: [],
        // other userData properties...
      };

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false); // Modify the expectation based on the desired behavior
    });

    it('should not link when existing social data of a different type is present', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [
          {
            email: '<EMAIL>',
            type: 'facebook', // Different type
            status: 0,
          },
        ],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: true,
        isActive: false,
        index: 0,
        data: undefined,
      });

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
    });

    it('should not link when userSocialStatus throws an error', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockImplementation(() => {
        throw new Error('Simulated error');
      });

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
      // Add more assertions based on the expected behavior when checkSocialLoginStatus throws an error
    });

    it('should not link when findOneAndUpdate throws an error', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue({
        status: false,
        isActive: false,
        index: 0,
        data: undefined,
      });

      jest.spyOn(userRepositoryMock, 'findOneAndUpdate').mockImplementation(() => {
        throw new Error('Simulated error');
      });

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
      // Add more assertions based on the expected behavior when findOneAndUpdate throws an error
    });

    it('should handle null or undefined userData.user_social_data', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: null, // or undefined
        // other userData properties...
      };

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
      // Add more assertions based on the desired behavior
    });

    it('should link when userSocialStatus returns undefined', async () => {
      // Arrange
      const linkData = {
        email: '<EMAIL>',
        type: 'google',
      };
      const userData = {
        user_social_data: [],
        // other userData properties...
      };

      jest.spyOn(socialLoginHelperMock, 'checkSocialLoginStatus').mockReturnValue(undefined);

      // Act
      const result = await socialLoginHelper.linkUserSocialData(linkData, userData);

      // Assert
      expect(result).toBe(false);
      // Add more assertions based on the desired behavior
    });
  });

  describe('userSocialStatus', () => {
    it('should return "show link user button" when no user social data is present', async () => {
      // Arrange
      const email = '<EMAIL>';
      const type = 'facebook';
      const userInfo = {
        user_social_data: [],
        // other userInfo properties...
      };

      // Act
      const result = await socialLoginHelper.userSocialStatus(email, type, userInfo);

      // Assert
      expect(result).toEqual({
        status: false,
        code: 3,
        msg: 'show link user button',
      });
    });

    it('should return "do login operation" when matching user social data is found', async () => {
      // Arrange
      const email = '<EMAIL>';
      const type = 'facebook';
      const userInfo = {
        user_social_data: [
          {
            email: '<EMAIL>',
            type: 'facebook',
            // other social data properties...
          },
        ],
        // other userInfo properties...
      };

      // Act
      const result = await socialLoginHelper.userSocialStatus(email, type, userInfo);

      // Assert
      expect(result).toEqual({
        status: true,
        code: 1,
        msg: 'do login operation',
      });
    });

    it('should return "incorrect mapping show error" when no matching user social data is found', async () => {
      // Arrange
      const email = '<EMAIL>';
      const type = 'facebook';
      const userInfo = {
        user_social_data: [
          {
            email: '<EMAIL>',
            type: 'linkedin',
            // other social data properties...
          },
        ],
        // other userInfo properties...
      };

      // Act
      const result = await socialLoginHelper.userSocialStatus(email, type, userInfo);

      // Assert
      expect(result).toEqual({
        status: false,
        code: 2,
        msg: 'incorrect mapping show error',
      });
    });

    it('should return "incorrect mapping show error" when social data is present but does not match the specified email and type', async () => {
      // Arrange
      const email = '<EMAIL>';
      const type = 'facebook';
      const userInfo = {
        user_social_data: [
          {
            email: '<EMAIL>',
            type: 'linkedin',
            // other social data properties...
          },
          {
            email: '<EMAIL>',
            type: 'facebook',
            // other social data properties...
          },
        ],
        // other userInfo properties...
      };

      // Act
      const result = await socialLoginHelper.userSocialStatus(email, type, userInfo);

      // Assert
      expect(result).toEqual({
        status: false,
        code: 2,
        msg: 'incorrect mapping show error',
      });
    });
  });

  describe('getUserSocialRedirectUrl', () => {
    it('should return calendar redirect URL with encoded value and set cookie', async () => {
      // Arrange
      const cookieData = {
        calendarRedirectCookie: 'calendarValue',
      };
      const linkData = { email: '<EMAIL>' };
      const userObj = {
        /* user object properties */
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValueOnce('calendarRedirectCookie');
      jest.spyOn(configServiceMock, 'get').mockReturnValueOnce('calendarUrl');

      // Act
      const result = await socialLoginHelper.getUserSocialRedirectUrl(cookieData, linkData, userObj);

      // Assert
      expect(result.status).toBe(true);
      expect(result.msg).toBe('success');
      expect(result.setCookie).toBe(true);
      expect(result.data).toContain('someThingWentWrongcalendarValue');
      // expect(result.cookieValue.length).toBe(1);
      // expect(result.cookieValue[0].name).toBe('calendarRedirectCookie');
    });

    it('should return token redirect URL and set cookie', async () => {
      // Arrange
      const cookieData = {
        freemiumAssignmentToken: 'assignmentTokenValue',
      };
      const linkData = { email: '<EMAIL>' };
      const userObj = {
        /* user object properties */
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(authHelperMock, 'getTokenRedirectUrl').mockReturnValueOnce({
        status: true,
        redirectUrl: 'tokenRedirectUrl',
        setCookie: true,
        cookieValue: {
          name: 'ssoCookie',
          value: 'tokenValue',
          options: { expires: expect.any(Date) },
        },
      });

      // Act
      const result = await socialLoginHelper.getUserSocialRedirectUrl(cookieData, linkData, userObj);

      // Assert
      expect(result.status).toBe(false);
      expect(result.msg).toBe('');
      expect(result.setCookie).toBe(false);
      expect(result.data).toBe('');
      // expect(result.cookieValue.length).toBe(1);
      // expect(result.cookieValue[0].name).toBe('ssoCookie');
    });

    it('should return NPS redirect URL and set cookie', async () => {
      // Arrange
      const cookieData = {
        npsRedirectCookie: 'npsValue',
      };
      const linkData = { email: '<EMAIL>' };
      const userObj = {
        /* user object properties */
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(authHelperMock, 'getNpsRedirectUrl').mockReturnValueOnce({
        status: true,
        redirectUrl: 'npsRedirectUrl',
        setCookie: true,
        cookieValue: {
          name: 'npsRedirectCookie',
          value: 'npsRedirectUrl',
          options: { expires: expect.any(Date) },
        },
      });

      // Act
      const result = await socialLoginHelper.getUserSocialRedirectUrl(cookieData, linkData, userObj);

      // Assert
      expect(result.status).toBe(false);
      expect(result.msg).toBe('');
      expect(result.setCookie).toBe(false);
      expect(result.data).toBe('');
      // expect(result.cookieValue.length).toBe(1);
      // expect(result.cookieValue[0].name).toBe('npsRedirectCookie');
    });

    it('should return community redirect URL and set cookie', async () => {
      // Arrange
      const cookieData = {
        communityCookie: 'communityValue',
      };
      const linkData = { email: '<EMAIL>' };
      const userObj = {
        /* user object properties */
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UsermgmtCommunityHelper') {
          return Promise.resolve(usermgmtCommunityHelperMock);
        }
      });

      jest.spyOn(usermgmtCommunityHelperMock, 'getCommunityRedirectUrl').mockReturnValueOnce({
        url: 'communityRedirectUrl',
        setCookie: true,
        cookieValue: {
          name: 'communityRedirectCookie',
          value: 'communityRedirectUrl',
          options: { expires: expect.any(Date) },
        },
      });

      // Act
      const result = await socialLoginHelper.getUserSocialRedirectUrl(cookieData, linkData, userObj);

      // Assert
      expect(result.status).toBe(false);
      expect(result.msg).toBe('');
      expect(result.setCookie).toBe(false);
      expect(result.data).toBe('');
      // expect(result.cookieValue.length).toBe(1);
      // expect(result.cookieValue[0].name).toBe('communityRedirectCookie');
    });

    it('should return default redirect URL and set cookie', async () => {
      // Arrange
      const cookieData = {};
      const linkData = { email: '<EMAIL>' };
      const userObj = {
        /* user object properties */
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
      });

      jest.spyOn(authHelperMock, 'generateRedirectLinkToManageRedirect').mockReturnValueOnce('defaultRedirectUrl');

      // Act
      const result = await socialLoginHelper.getUserSocialRedirectUrl(cookieData, linkData, userObj);

      // Assert
      expect(result.status).toBe(false);
      expect(result.msg).toBe('');
      expect(result.setCookie).toBe(false);
      expect(result.data).toBe('');
      // expect(result.cookieValue.length).toBe(1);
      // expect(result.cookieValue[0].name).toBe('defaultRedirectCookie');
    });

    it('should handle errors and return default response', async () => {
      // Arrange
      const cookieData = {};
      const linkData = { email: '<EMAIL>' };
      const userObj = {
        /* user object properties */
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation(() => {
        throw new Error('Simulated error during getHelper');
      });

      jest.spyOn(configServiceMock, 'get').mockReturnValueOnce('someThingWentWrong');
      const errorSpy = jest.spyOn(Logger, 'error').mockImplementation();

      // Act
      const result = await socialLoginHelper.getUserSocialRedirectUrl(cookieData, linkData, userObj);

      // Assert
      expect(result.status).toBe(false);
      expect(result.msg).toBe('');
      expect(result.setCookie).toBe(false);
      expect(result.data).toBe('');
      // expect(errorSpy).toHaveBeenCalled();
    });
  });
});
