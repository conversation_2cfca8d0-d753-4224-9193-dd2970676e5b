import { Communication } from '../../../common/services/communication/communication';
import { Inject, Injectable } from '@nestjs/common';
import { Logger } from '../../../logging/logger';
import { ConfigService } from '@nestjs/config';
import { CustomResponse } from '../../../common/typeDef/auth.type';
import { Utility } from './../../../common/util/utility';

@Injectable()
export class SocialLoginService extends Communication {
  @Inject() private configService: ConfigService;
  constructor() {
    super();
    this.url = '';
  }
  /**
   * reset the value of URL to empty string
   */
  private resetUri(): void {
    this.url = '';
  }

  async fetchSocialUserProfileDetailsFromToken(data: { token: string; type: string }) {
    const response: CustomResponse = {
      status: false,
      msg: 'failed',
    };
    try {
      if (Utility.isEmpty(data?.token)) {
        return response;
      }
      this.resetUri();
      if ('facebook' === data?.type?.toLowerCase()) {
        const facebookData: any = await this.get(this.configService.get('socialAuthFacebook') + data?.token);
        if (facebookData.hasOwnProperty('id')) {
          return { status: true, msg: 'success', data: { sub: facebookData?.id } };
        }
        return { status: false, msg: 'Something went wrong, please try again.' };
      }
      if ('linkedin' === data?.type?.toLowerCase()) {
        this.setRequestHeader('Authorization', 'Bearer ' + data?.token);
        const linkedinData: any = await this.get(this.configService.get('socialAuthLinkedin'));
        if (linkedinData?.status === 200) {
          return { status: true, msg: 'success', data: { sub: linkedinData?.data?.sub } };
        }
        return { status: false, msg: 'Something went wrong, please try again.' };
      }
      if ('google' === data?.type?.toLowerCase()) {
        const response: any = await this.get(this.configService.get('socialAuthGoogle') + data?.token);
        if (response?.status === 200) {
          return { status: true, msg: 'success', data: response };
        }
        return { status: false, msg: 'Something went wrong, please try again.' };
      }
    } catch (error: any) {
      Logger.error(this.fetchSocialUserProfileDetailsFromToken?.name, {
        METHOD: this.constructor?.name + '@' + this.fetchSocialUserProfileDetailsFromToken?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      response.msg = 'something went wrong while getting the token details';
      response.status = false;
      return response;
    }
  }
}
