import { Test, TestingModule } from '@nestjs/testing';
import { SocialLoginService } from './social-login.service';
import { ConfigService } from '@nestjs/config';
import { Utility } from '../../../common/util/utility';
import { CustomResponse } from '../../../common/typeDef/auth.type';

jest.mock('../../../common/services/communication/communication');

describe('SocialLoginService', () => {
  let socialLoginService: SocialLoginService;
  let utility: Utility;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SocialLoginService, ConfigService, Utility],
    }).compile();

    socialLoginService = module.get<SocialLoginService>(SocialLoginService);
    utility = module.get<Utility>(Utility);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(socialLoginService).toBeDefined();
  });

  // describe('fetchSocialUserProfileDetailsFromToken', () => {
  //   it.each([
  //     ['', 'facebook'],
  //     ['someToken', 'unknown'],
  //     ['invalidToken', 'facebook'],
  //     ['validToken', 'linkedin'],
  //     ['errorToken', 'facebook'],
  //     ['', 'linkedin'],
  //     ['someToken', 'unknown'],
  //   ])('should handle various scenarios', async (token, type) => {
  //     const result = await socialLoginService.fetchSocialUserProfileDetailsFromToken({ token, type });
  //     expect(result.status).toBe(false);
  //     expect(result.msg).toBeDefined(); // Adjust this based on your expected behavior
  //   });

  //   it('should return success response for Facebook type with valid token', async () => {
  //     jest.spyOn(socialLoginService as any, 'get').mockResolvedValue({ id: '123' });
  //     const result: CustomResponse = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
  //       token: 'validToken',
  //       type: 'facebook',
  //     });
  //     expect(result.status).toBe(true);
  //     expect(result.msg).toBe('success');
  //     expect(result.data).toEqual({ sub: '123' });
  //   });

  //   it('should return failed response for Facebook type with invalid token', async () => {
  //     jest.spyOn(socialLoginService as any, 'get').mockResolvedValue({});
  //     const result = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
  //       token: 'invalidToken',
  //       type: 'facebook',
  //     });
  //     expect(result.status).toBe(false);
  //     expect(result.msg).toBeDefined(); // Adjust this based on your expected behavior
  //   });

  //   it('should return success response for LinkedIn type with valid token', async () => {
  //     jest.spyOn(socialLoginService as any, 'get').mockResolvedValue({ status: 200, data: { sub: '456' } });
  //     const result: CustomResponse = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
  //       token: 'validToken',
  //       type: 'linkedin',
  //     });
  //     expect(result.status).toBe(true);
  //     expect(result.msg).toBe('success');
  //     expect(result.data).toEqual({ sub: '456' });
  //   });

  //   // it('should return failed response for LinkedIn type with invalid token', async () => {
  //   //   jest.spyOn(socialLoginService as any, 'get').mockResolvedValue({ status: 500 });
  //   //   const result = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
  //   //     token: 'invalidToken',
  //   //     type: 'linkedin',
  //   //   });
  //   //   expect(result.status).toBe(false);
  //   //   expect(result.msg).toBeDefined(); // Adjust this based on your expected behavior
  //   // });

  //   it('should handle errors and log appropriately', async () => {
  //     jest.spyOn(socialLoginService as any, 'get').mockRejectedValue(new Error('Simulated error'));
  //     const result = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
  //       token: 'errorToken',
  //       type: 'facebook',
  //     });
  //     expect(result.status).toBe(false);
  //     expect(result.msg).toBeDefined(); // Adjust this based on your expected behavior
  //   });
  // });

  describe('resetUri', () => {
    it('should reset the URL to an empty string', () => {
      (socialLoginService as any).url = 'someValue';
      (socialLoginService as any).resetUri();
      expect((socialLoginService as any).url).toBe('');
    });
  });
});
