import { Test } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../../../user/services/user.service';
import { Cloud6Service } from '../../../common/services/communication/cloud6/cloud6.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { CachingService } from '../../../caching/caching.service';
import { EmailService } from '../../../common/services/email/email.service';
import { BadRequestException } from '@nestjs/common';
import { AuthTokenHelper } from '../../../auth/helper/auth.tokenhelper';
import { LoginDto } from '../../../auth/dtos/login.dto';
import { User } from '../../../db/mongo/schema/user/user.schema';
import { UserRepository } from '../../../user/repositories/user/user.repository';
import { PaperclipService } from '../../../common/services/communication/paperclip/paperclip.service';
import { Logger } from '../../../logging/logger';


describe('AuthService', () => {
  let authService: AuthService;
  let configServiceMock: any;
  let helperServiceMock: HelperService;
  let userServiceMock: any;

  beforeEach(async () => {
    configServiceMock = {
      get: jest.fn(),
    };

    userServiceMock = {
      userRegistration: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        AuthService,
        HelperService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: CachingService,
          useValue: {},
        },
        {
          provide: UserService,
          useValue: userServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: {},
        },
        {
          provide: Cloud6Service,
          useValue: {},
        },
        {
          provide: EmailService,
          useValue: {},
        },
      ],
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
    configServiceMock = moduleRef.get<ConfigService>(ConfigService);
    helperServiceMock = moduleRef.get<HelperService>(HelperService);
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
  });

  describe('signUp', () => {
    let mockAuthTokenHelper: any;
    let mockAuthHelper: any;
    let mockCookieHelper: any;
    let mockUserHelper: any;
    let mockLrsHelper: any;
    let mockRes: any;

    beforeEach(() => {
      mockAuthTokenHelper = {
        generateSessionTokens: jest.fn(),
      };
      mockAuthHelper = {
        handleUserSignup: jest.fn(),
        getSignupRedirect: jest.fn(),
      };
      mockCookieHelper = {
        setBulkCookie: jest.fn(),
        setCookie: jest.fn(),
      };
      mockUserHelper = {
        updateUserLoginTime: jest.fn(),
      };
      mockLrsHelper = {
        sendDataToLrs: jest.fn(),
      };
      mockRes = {
        cookie: jest.fn(),
        redirect: jest.fn(),
      };
    });

    it('should successfully sign up a user and return redirectUri', async () => {
      // Mock data
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        phone_no: '1234567890',
        country_code: 'US',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {
        sl_su_utmz: 'utm_source_test',
        skillup_referral_code: 'REF123',
      };
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockTokenDetail = { idToken: 'mockIdToken', userData: {} };
      const mockSignupRedirectData = {
        redirectUrl: 'https://example.com/final-redirect',
        setCookie: true,
        cookieValue: [{ name: 'testCookie', value: 'testValue' }],
      };

      // Mock config service
      configServiceMock.get = jest.fn().mockImplementation((key) => {
        switch (key) {
          case 'clientKey': return 'mockClientKey';
          case 'defaultFreemiumSignupUtm': return 'default_utm';
          case 'ssoCookie': return 'ssoCookie';
          default: return 'mockValue';
        }
      });

      // Mock dependencies
      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);
      mockAuthHelper.getSignupRedirect.mockResolvedValue(mockSignupRedirectData);

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(mockAuthTokenHelper);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          default: return null;
        }
      });

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, mockRes);

      // Assertions
      expect(result).toBe('https://example.com/final-redirect');
      expect(userServiceMock.userRegistration).toHaveBeenCalledWith(signupDetail);
      expect(mockAuthTokenHelper.generateSessionTokens).toHaveBeenCalledWith(mockUserInfo, 'mockClientKey');
      expect(mockLrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        mockUserInfo,
        expect.objectContaining({
          verb: 'register',
          objectType: 'accounts',
          objectId: mockUserInfo.uid,
        })
      );
      expect(mockAuthHelper.handleUserSignup).toHaveBeenCalledWith('utm_source_test', 'REF123', mockUserInfo);
      expect(mockAuthHelper.getSignupRedirect).toHaveBeenCalledWith(cookieData, mockUserInfo, redirectUri);
      expect(mockCookieHelper.setBulkCookie).toHaveBeenCalledWith(mockRes, [
        { name: 'testCookie', value: 'testValue' },
        { name: 'ssoCookie', value: 'mockIdToken' }
      ]);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(mockRes, 'ssoCookie', 'mockIdToken');
      expect(mockUserHelper.updateUserLoginTime).toHaveBeenCalledWith(mockUserInfo);
    });

    it('should handle signup with default UTM source when cookie data is empty', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {}; // Empty cookie data
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockTokenDetail = { idToken: 'mockIdToken', userData: {} };

      configServiceMock.get = jest.fn().mockImplementation((key) => {
        switch (key) {
          case 'clientKey': return 'mockClientKey';
          case 'defaultFreemiumSignupUtm': return 'default_utm_source';
          case 'ssoCookie': return 'ssoCookie';
          default: return 'mockValue';
        }
      });

      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);
      mockAuthHelper.getSignupRedirect.mockResolvedValue({
        redirectUrl: redirectUri,
        setCookie: false,
        cookieValue: [],
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          default: return null;
        }
      });

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, mockRes);

      expect(result).toBe(redirectUri);
      expect(mockAuthHelper.handleUserSignup).toHaveBeenCalledWith('default_utm_source', '', mockUserInfo);
    });

    it('should handle signup without idToken', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockTokenDetail = { userData: {} }; // No idToken

      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);
      jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(mockLrsHelper);

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, mockRes);

      expect(result).toBe(redirectUri);
      expect(mockAuthHelper.handleUserSignup).not.toHaveBeenCalled();
      expect(mockCookieHelper.setCookie).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when userRegistration fails', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockError = new Error('User registration failed');

      userServiceMock.userRegistration.mockRejectedValue(mockError);

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, mockRes))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when token generation fails', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockError = new Error('Token generation failed');

      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockRejectedValue(mockError);

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, mockRes))
        .rejects.toThrow(BadRequestException);
    });

    it('should log error details when signup fails', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockError = new Error('Signup failed');

      userServiceMock.userRegistration.mockRejectedValue(mockError);
      const loggerSpy = jest.spyOn(Logger, 'error');

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, mockRes))
        .rejects.toThrow(BadRequestException);

      expect(loggerSpy).toHaveBeenCalledWith('signUp', expect.objectContaining({
        METHOD: expect.stringContaining('AuthService@signUp'),
        MESSAGE: 'Signup failed',
        REQUEST: signupDetail,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('manageAuthRedirect', () => {
    it('should successfully manage authentication redirect', async () => {
      // Mock dependencies and required methods
      const cookieBody = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      const token = 'mockToken';
      const gid = 'mockGid';
      configServiceMock.get = jest.fn().mockResolvedValue('client_id');

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            decodeJWTToken: jest.fn().mockResolvedValueOnce({
              data: {
                /* Mock decoded data */
              },
            }),
          });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve({ setBulkCookie: jest.fn(), setCookie: jest.fn(), clearBulkCookie: jest.fn() });
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({
            handleB2bB2cRedirect: jest.fn().mockResolvedValueOnce('mockFinalRedirectUrl'),
            handleSkillupCookieActions: jest.fn().mockResolvedValueOnce({
              redirectUrl: 'mockRedirectUrl',
              cookieValue: [],
              clearCookie: true,
              clearCookieData: [], // Provide mock clearCookieData here
            }),
          });
        }
        return null;
      });

      const result = await authService.manageAuthRedirect(cookieBody, res as any, token, gid);

      expect(result).toBe('mockFinalRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const cookieBody = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      const token = 'mockToken';
      const gid = 'mockGid';

      jest.spyOn(authService['helperService'], 'get').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.manageAuthRedirect(cookieBody, res as any, token, gid)).rejects.toThrow(
        BadRequestException,
      );
      // Add more assertions based on your specific use case
    });
  });

  describe('handleUserSsoCookie', () => {
    it('should successfully handle user SSO cookie and return redirectUrl', async () => {
      // Mock dependencies and required methods
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      const ssoCookie = 'mockSsoCookie';

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            decodeJWTToken: jest.fn().mockResolvedValueOnce({
              data: {
                /* Mock decoded data */
              },
            }),
          });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve({
            setBulkCookie: jest.fn(),
            setCookie: jest.fn(),
            clearBulkCookie: jest.fn(),
            getRedirectUrlFromCookie: jest.fn().mockResolvedValueOnce('mockFinalRedirectUrl'),
          });
        }
        return null;
      });

      const result = await authService.handleUserSsoCookie(queryParam, ssoCookie);

      expect(result).toBe('mockFinalRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      const ssoCookie = 'mockSsoCookie';

      jest.spyOn(authService['helperService'], 'get').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.handleUserSsoCookie(queryParam, ssoCookie)).rejects.toThrow(BadRequestException);
      // Add more assertions based on your specific use case
    });
  });

  describe('getRedirectUrl', () => {
    it('should successfully get redirect URL and set cookies', async () => {
      // Mock dependencies and required methods
      const res: Response = {} as Response;
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      configServiceMock.get = jest.fn().mockResolvedValue('lmsSiteUrl');
      const cookieHelpers = {
        setFermiumCookie: jest.fn(),
        setCalenderCookie: jest.fn(),
        setNpsCookie: jest.fn(),
        setCommunityCookie: jest.fn(),
      };
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve(cookieHelpers);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({
            getDomainUrlInfo: jest.fn().mockResolvedValue({
              redirectUrl: 'mockValidRedirectUrl',
              calendarUrl: 'mockCalendarUrl',
              isB2BAndB2C: true,
              domainGid: 'mockDomainGid',
              domainUrl: 'domainUrl',
            }),
          });
        }
        return null;
      });

      const result = await authService.getRedirectUrl(res as any, queryParam);

      expect(result.calendarUrl).toEqual('mockCalendarUrl');
      // Ensure that setFermiumCookie, setCalenderCookie, setNpsCookie, and setCommunityCookie are called
      expect(cookieHelpers.setFermiumCookie).toHaveBeenCalledWith('mockAssignmentToken', res);
      expect(cookieHelpers.setCalenderCookie).toHaveBeenCalledWith('mockCalendarUrl', res);
      // expect(cookieHelpers.setNpsCookie).toHaveBeenCalledWith('mockValidRedirectUrl', res);
      // expect(cookieHelpers.setCommunityCookie).toHaveBeenCalledWith('mockValidRedirectUrl', res);
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const res: Response = {} as Response;
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };

      jest.spyOn(authService['helperService'], 'getHelper').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.getRedirectUrl(res as any, queryParam)).rejects.toThrow(BadRequestException);
      // Add more assertions based on your specific use case
    });
  });

  describe('authenticateUser', () => {
    it('should successfully authenticate user and set SSO cookie', async () => {
      // Mock dependencies and required methods
      const res: Response = {} as Response;
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: '2342453',
      };
      const clientKey = 'mockClientKey';
      const cookieHelper = { setCookie: jest.fn() };
      const lrsHelper = { sendDataToLrs: jest.fn() };
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') {
          return Promise.resolve({
            authenticateUser: jest.fn().mockResolvedValueOnce({ uid: 'mockUserId', userData: {} } as unknown as User),
          });
        } else if (helperName === 'CookieHelper') {
          return Promise.resolve(cookieHelper);
        } else if (helperName === 'lrsHelper') {
          return Promise.resolve(lrsHelper); // Use the mocked lrsHelper here
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            generateSessionTokens: jest.fn().mockResolvedValueOnce({ idToken: 'mockIdToken', userData: {} }),
          });
        }
        return null;
      });

      configServiceMock.get = jest.fn().mockResolvedValue('ssocCookie');

      const result = await authService.authenticateUser(res as any, loginDto, clientKey);

      expect(result).toEqual({ uid: 'mockUserId', userData: {} });
      configServiceMock.get = jest.fn().mockResolvedValue('maxAge');

      // Ensure that setCookie and sendDataToLrs are called
      // expect(await cookieHelper.setCookie).toHaveBeenCalledWith(res, 'ssoCookie', 'mockIdToken',{"expires":  Promise.resolve({})});

      expect(lrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        {},
        {
          verb: 'login',
          objectType: 'accounts',
          objectId: 'mockUserId',
          dataVals: {
            client_id: 'mockClientKey',
            redirect_url: loginDto?.redirect_url,
          },
        },
      );
    });

    it('should return an error if authentication fails', async () => {
      // Mock dependencies and required methods to throw an error
      const res: Response = {} as Response;
      const loginDto: LoginDto = {
        email: '',
        password: '',
      };
      const clientKey = 'mockClientKey';

      jest.spyOn(authService['helperService'], 'getHelper').mockResolvedValueOnce({
        authenticateUser: jest.fn().mockRejectedValueOnce(new Error('Mocked authentication error')),
      });

      const result = await authService.authenticateUser(res as any, loginDto, clientKey);

      expect(result).toBeInstanceOf(Error);
      // expect(result.message).toBe('Mocked authentication error');
      // Add more assertions based on your specific use case
    });
  });

});
