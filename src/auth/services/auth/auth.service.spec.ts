import { Test } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../../../user/services/user.service';
import { Cloud6Service } from '../../../common/services/communication/cloud6/cloud6.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { CachingService } from '../../../caching/caching.service';
import { EmailService } from '../../../common/services/email/email.service';
import { BadRequestException } from '@nestjs/common';
import { AuthTokenHelper } from '../../../auth/helper/auth.tokenhelper';
import { LoginDto } from '../../../auth/dtos/login.dto';
import { User } from '../../../db/mongo/schema/user/user.schema';
import { Logger } from '../../../logging/logger';


describe('AuthService', () => {
  let authService: AuthService;
  let configServiceMock: any;
  let helperServiceMock: HelperService;
  let userServiceMock: any;

  beforeEach(async () => {
    configServiceMock = {
      get: jest.fn(),
    };

    userServiceMock = {
      userRegistration: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        AuthService,
        HelperService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: CachingService,
          useValue: {},
        },
        {
          provide: UserService,
          useValue: userServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: {},
        },
        {
          provide: Cloud6Service,
          useValue: {},
        },
        {
          provide: EmailService,
          useValue: {},
        },
      ],
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
    configServiceMock = moduleRef.get<ConfigService>(ConfigService);
    helperServiceMock = moduleRef.get<HelperService>(HelperService);
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
  });

  describe('signUp', () => {
    let mockAuthTokenHelper: any;
    let mockAuthHelper: any;
    let mockCookieHelper: any;
    let mockUserHelper: any;
    let mockLrsHelper: any;
    let mockRes: any;

    beforeEach(() => {
      mockAuthTokenHelper = {
        generateSessionTokens: jest.fn(),
      };
      mockAuthHelper = {
        handleUserSignup: jest.fn(),
        getSignupRedirect: jest.fn(),
      };
      mockCookieHelper = {
        setBulkCookie: jest.fn(),
        setCookie: jest.fn(),
      };
      mockUserHelper = {
        updateUserLoginTime: jest.fn(),
      };
      mockLrsHelper = {
        sendDataToLrs: jest.fn(),
      };
      mockRes = {
        cookie: jest.fn(),
        redirect: jest.fn(),
      };
    });

    it('should successfully sign up a user and return redirectUri', async () => {
      // Mock data
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        phone_no: '**********',
        country_code: 'US',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {
        sl_su_utmz: 'utm_source_test',
        skillup_referral_code: 'REF123',
      };
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockTokenDetail = { idToken: 'mockIdToken', userData: {} };
      const mockSignupRedirectData = {
        redirectUrl: 'https://example.com/final-redirect',
        setCookie: true,
        cookieValue: [{ name: 'testCookie', value: 'testValue' }],
      };

      // Mock config service
      configServiceMock.get = jest.fn().mockImplementation((key) => {
        switch (key) {
          case 'clientKey': return 'mockClientKey';
          case 'defaultFreemiumSignupUtm': return 'default_utm';
          case 'ssoCookie': return 'ssoCookie';
          default: return 'mockValue';
        }
      });

      // Mock dependencies
      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);
      mockAuthHelper.getSignupRedirect.mockResolvedValue(mockSignupRedirectData);

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(mockAuthTokenHelper);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          default: return null;
        }
      });

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, mockRes);

      // Assertions
      expect(result).toBe('https://example.com/final-redirect');
      expect(userServiceMock.userRegistration).toHaveBeenCalledWith(signupDetail);
      expect(mockAuthTokenHelper.generateSessionTokens).toHaveBeenCalledWith(mockUserInfo, 'mockClientKey');
      expect(mockLrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        mockTokenDetail.userData,
        expect.objectContaining({
          verb: 'register',
          objectType: 'accounts',
          objectId: mockUserInfo.uid,
          dataVals: {
            client_id: 'mockClientKey',
            redirect_url: redirectUri,
          },
        })
      );
      expect(mockAuthHelper.handleUserSignup).toHaveBeenCalledWith('utm_source_test', 'REF123', mockUserInfo);
      expect(mockAuthHelper.getSignupRedirect).toHaveBeenCalledWith(cookieData, mockUserInfo, redirectUri);
      expect(mockCookieHelper.setBulkCookie).toHaveBeenCalledWith(mockRes, [
        { name: 'testCookie', value: 'testValue' },
        { name: 'ssoCookie', value: 'mockIdToken' }
      ]);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(mockRes, 'ssoCookie', 'mockIdToken');
      expect(mockUserHelper.updateUserLoginTime).toHaveBeenCalledWith(mockUserInfo);
    });

    it('should handle signup with default UTM source when cookie data is empty', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {}; // Empty cookie data
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockTokenDetail = { idToken: 'mockIdToken', userData: {} };

      configServiceMock.get = jest.fn().mockImplementation((key) => {
        switch (key) {
          case 'clientKey': return 'mockClientKey';
          case 'defaultFreemiumSignupUtm': return 'default_utm_source';
          case 'ssoCookie': return 'ssoCookie';
          default: return 'mockValue';
        }
      });

      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);
      mockAuthHelper.getSignupRedirect.mockResolvedValue({
        redirectUrl: redirectUri,
        setCookie: false,
        cookieValue: [],
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          default: return null;
        }
      });

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, mockRes);

      expect(result).toBe(redirectUri);
      expect(mockAuthHelper.handleUserSignup).toHaveBeenCalledWith('default_utm_source', '', mockUserInfo);
    });

    it('should handle signup without idToken', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockTokenDetail = { userData: {} }; // No idToken

      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);
      jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(mockLrsHelper);

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, mockRes);

      expect(result).toBe(redirectUri);
      expect(mockAuthHelper.handleUserSignup).not.toHaveBeenCalled();
      expect(mockCookieHelper.setCookie).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when userRegistration fails', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockError = new Error('User registration failed');

      userServiceMock.userRegistration.mockRejectedValue(mockError);

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, mockRes))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when token generation fails', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockUserInfo = { uid: 12345, email: '<EMAIL>' };
      const mockError = new Error('Token generation failed');

      userServiceMock.userRegistration.mockResolvedValue(mockUserInfo);
      mockAuthTokenHelper.generateSessionTokens.mockRejectedValue(mockError);

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, mockRes))
        .rejects.toThrow(BadRequestException);
    });

    it('should log error details when signup fails', async () => {
      const signupDetail: Partial<User> = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const redirectUri = 'https://example.com/redirect';
      const cookieData = {};
      const mockError = new Error('Signup failed');

      userServiceMock.userRegistration.mockRejectedValue(mockError);
      const loggerSpy = jest.spyOn(Logger, 'error');

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, mockRes))
        .rejects.toThrow(BadRequestException);

      expect(loggerSpy).toHaveBeenCalledWith('signUp', expect.objectContaining({
        METHOD: expect.stringContaining('AuthService@signUp'),
        MESSAGE: 'Signup failed',
        REQUEST: signupDetail,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('manageAuthRedirect', () => {
    it('should successfully manage authentication redirect', async () => {
      // Mock dependencies and required methods
      const cookieBody = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      const token = 'mockToken';
      const gid = 'mockGid';
      configServiceMock.get = jest.fn().mockResolvedValue('client_id');

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            decodeJWTToken: jest.fn().mockResolvedValueOnce({
              data: {
                /* Mock decoded data */
              },
            }),
          });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve({ setBulkCookie: jest.fn(), setCookie: jest.fn(), clearBulkCookie: jest.fn() });
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({
            handleB2bB2cRedirect: jest.fn().mockResolvedValueOnce('mockFinalRedirectUrl'),
            handleSkillupCookieActions: jest.fn().mockResolvedValueOnce({
              redirectUrl: 'mockRedirectUrl',
              cookieValue: [],
              clearCookie: true,
              clearCookieData: [], // Provide mock clearCookieData here
            }),
          });
        }
        return null;
      });

      const result = await authService.manageAuthRedirect(cookieBody, res as any, token, gid);

      expect(result).toBe('mockFinalRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const cookieBody = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      const token = 'mockToken';
      const gid = 'mockGid';

      jest.spyOn(authService['helperService'], 'get').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.manageAuthRedirect(cookieBody, res as any, token, gid)).rejects.toThrow(
        BadRequestException,
      );
      // Add more assertions based on your specific use case
    });
  });

  describe('handleUserSsoCookie', () => {
    it('should successfully handle user SSO cookie and return redirectUrl', async () => {
      // Mock dependencies and required methods
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      const ssoCookie = 'mockSsoCookie';

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            decodeJWTToken: jest.fn().mockResolvedValueOnce({
              data: {
                /* Mock decoded data */
              },
            }),
          });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve({
            setBulkCookie: jest.fn(),
            setCookie: jest.fn(),
            clearBulkCookie: jest.fn(),
            getRedirectUrlFromCookie: jest.fn().mockResolvedValueOnce('mockFinalRedirectUrl'),
          });
        }
        return null;
      });

      const result = await authService.handleUserSsoCookie(queryParam, ssoCookie);

      expect(result).toBe('mockFinalRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      const ssoCookie = 'mockSsoCookie';

      jest.spyOn(authService['helperService'], 'get').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.handleUserSsoCookie(queryParam, ssoCookie)).rejects.toThrow(BadRequestException);
      // Add more assertions based on your specific use case
    });
  });

  describe('getRedirectUrl', () => {
    it('should successfully get redirect URL and set cookies', async () => {
      // Mock dependencies and required methods
      const res: Response = {} as Response;
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      configServiceMock.get = jest.fn().mockResolvedValue('lmsSiteUrl');
      const cookieHelpers = {
        setFermiumCookie: jest.fn(),
        setCalenderCookie: jest.fn(),
        setNpsCookie: jest.fn(),
        setCommunityCookie: jest.fn(),
      };
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve(cookieHelpers);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({
            getDomainUrlInfo: jest.fn().mockResolvedValue({
              redirectUrl: 'mockValidRedirectUrl',
              calendarUrl: 'mockCalendarUrl',
              isB2BAndB2C: true,
              domainGid: 'mockDomainGid',
              domainUrl: 'domainUrl',
            }),
          });
        }
        return null;
      });

      const result = await authService.getRedirectUrl(res as any, queryParam);

      expect(result.calendarUrl).toEqual('mockCalendarUrl');
      // Ensure that setFermiumCookie, setCalenderCookie, setNpsCookie, and setCommunityCookie are called
      expect(cookieHelpers.setFermiumCookie).toHaveBeenCalledWith('mockAssignmentToken', res);
      expect(cookieHelpers.setCalenderCookie).toHaveBeenCalledWith('mockCalendarUrl', res);
      // expect(cookieHelpers.setNpsCookie).toHaveBeenCalledWith('mockValidRedirectUrl', res);
      // expect(cookieHelpers.setCommunityCookie).toHaveBeenCalledWith('mockValidRedirectUrl', res);
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const res: Response = {} as Response;
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };

      jest.spyOn(authService['helperService'], 'getHelper').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.getRedirectUrl(res as any, queryParam)).rejects.toThrow(BadRequestException);
      // Add more assertions based on your specific use case
    });
  });

  describe('authenticateUser', () => {
    let mockAuthHelper: any;
    let mockUserHelper: any;
    let mockAuthTokenHelper: any;
    let mockCookieHelper: any;
    let mockLrsHelper: any;
    let mockRes: any;

    beforeEach(() => {
      mockAuthHelper = {
        authenticateUser: jest.fn(),
      };
      mockUserHelper = {
        updateUserLoginTime: jest.fn(),
      };
      mockAuthTokenHelper = {
        generateSessionTokens: jest.fn(),
      };
      mockCookieHelper = {
        setCookie: jest.fn(),
      };
      mockLrsHelper = {
        sendDataToLrs: jest.fn(),
      };
      mockRes = {
        cookie: jest.fn(),
        redirect: jest.fn(),
      };

      configServiceMock.get = jest.fn().mockImplementation((key) => {
        switch (key) {
          case 'ssoCookie': return 'ssoCookie';
          case 'maxAge': return new Date(Date.now() + 3600000);
          default: return 'mockValue';
        }
      });
    });

    it('should successfully authenticate user and set SSO cookie', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
        redirect_url: 'https://example.com/redirect',
      };
      const clientKey = 'mockClientKey';
      const mockUser = {
        uid: 12345,
        email: '<EMAIL>',
        status: 1,
        first_name: 'John',
        last_name: 'Doe'
      };
      const mockTokenDetail = {
        idToken: 'mockIdToken',
        userData: { id: 12345, email: '<EMAIL>' }
      };

      // Mock all helpers
      mockAuthHelper.authenticateUser.mockResolvedValue(mockUser);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          default: return null;
        }
      });

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(mockAuthTokenHelper);
        }
        return null;
      });

      const result = await authService.authenticateUser(mockRes, loginDto, clientKey);

      // Assertions
      expect(result).toEqual(mockUser);
      expect(mockAuthHelper.authenticateUser).toHaveBeenCalledWith(loginDto);
      expect(mockAuthTokenHelper.generateSessionTokens).toHaveBeenCalledWith(mockUser, clientKey);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(
        mockRes,
        'ssoCookie',
        'mockIdToken',
        { expires: expect.any(Date) }
      );
      expect(mockUserHelper.updateUserLoginTime).toHaveBeenCalledWith(mockUser);
      expect(mockLrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        mockTokenDetail.userData,
        expect.objectContaining({
          verb: 'login',
          objectType: 'accounts',
          objectId: mockUser.uid,
          dataVals: {
            client_id: clientKey,
            redirect_url: loginDto.redirect_url,
          },
        })
      );
    });

    it('should return an error when authentication fails', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };
      const clientKey = 'mockClientKey';
      const mockError = new Error('InvalidCredentials');

      mockAuthHelper.authenticateUser.mockRejectedValue(mockError);

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          default: return null;
        }
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      const result = await authService.authenticateUser(mockRes, loginDto, clientKey);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('InvalidCredentials');
      expect(mockAuthHelper.authenticateUser).toHaveBeenCalledWith(loginDto);
      expect(mockAuthTokenHelper.generateSessionTokens).not.toHaveBeenCalled();
      expect(mockCookieHelper.setCookie).not.toHaveBeenCalled();
    });

    it('should return an error when token generation fails', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const clientKey = 'mockClientKey';
      const mockUser = { uid: 12345, email: '<EMAIL>', status: 1 };
      const mockError = new Error('Token generation failed');

      mockAuthHelper.authenticateUser.mockResolvedValue(mockUser);
      mockAuthTokenHelper.generateSessionTokens.mockRejectedValue(mockError);

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          default: return null;
        }
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      const result = await authService.authenticateUser(mockRes, loginDto, clientKey);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Token generation failed');
      expect(mockAuthHelper.authenticateUser).toHaveBeenCalledWith(loginDto);
      expect(mockAuthTokenHelper.generateSessionTokens).toHaveBeenCalledWith(mockUser, clientKey);
    });

    it('should return an error when cookie setting fails', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };
      const clientKey = 'mockClientKey';
      const mockUser = { uid: 12345, email: '<EMAIL>', status: 1 };
      const mockTokenDetail = { idToken: 'mockIdToken', userData: {} };
      const mockError = new Error('Cookie setting failed');

      mockAuthHelper.authenticateUser.mockResolvedValue(mockUser);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);
      mockCookieHelper.setCookie.mockRejectedValue(mockError);

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          default: return null;
        }
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      const result = await authService.authenticateUser(mockRes, loginDto, clientKey);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Cookie setting failed');
    });

    it('should log error details when authentication fails', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };
      const clientKey = 'mockClientKey';
      const mockError = new Error('Authentication failed');

      mockAuthHelper.authenticateUser.mockRejectedValue(mockError);
      const loggerSpy = jest.spyOn(Logger, 'error');

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          default: return null;
        }
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      const result = await authService.authenticateUser(mockRes, loginDto, clientKey);

      expect(result).toBeInstanceOf(Error);
      expect(loggerSpy).toHaveBeenCalledWith('authenticateUser', expect.objectContaining({
        METHOD: expect.stringContaining('AuthService@authenticateUser'),
        MESSAGE: 'Authentication failed',
        REQUEST: loginDto,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

    it('should handle authentication without redirect_url', async () => {
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: 'password123',
        // No redirect_url
      };
      const clientKey = 'mockClientKey';
      const mockUser = { uid: 12345, email: '<EMAIL>', status: 1 };
      const mockTokenDetail = { idToken: 'mockIdToken', userData: {} };

      mockAuthHelper.authenticateUser.mockResolvedValue(mockUser);
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(mockTokenDetail);

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'UserHelper': return Promise.resolve(mockUserHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          case 'lrsHelper': return Promise.resolve(mockLrsHelper);
          default: return null;
        }
      });

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockAuthTokenHelper);

      const result = await authService.authenticateUser(mockRes, loginDto, clientKey);

      expect(result).toEqual(mockUser);
      expect(mockLrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        mockTokenDetail.userData,
        expect.objectContaining({
          verb: 'login',
          objectType: 'accounts',
          objectId: mockUser.uid,
          dataVals: {
            client_id: clientKey,
            redirect_url: undefined,
          },
        })
      );
    });
  });

  describe('getTermsAndConditions', () => {
    it('should return termsAndConditions, styles, and source when Cloud6Service returns data', async () => {
      const mockCloud6Service = {
        getTermsAndConditions: jest.fn().mockResolvedValue({
          termsAndConditions: '<p>Terms</p>',
          styles: '<style>body {}</style>',
          source: 'cms',
        }),
      };

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockCloud6Service);

      const result = await authService.getTermsAndConditions();

      expect(result).toEqual({
        termsAndConditions: '<p>Terms</p>',
        styles: '<style>body {}</style>',
        source: 'cms',
      });
      expect(mockCloud6Service.getTermsAndConditions).toHaveBeenCalled();
    });

    it('should return default values if Cloud6Service returns undefined', async () => {
      const mockCloud6Service = {
        getTermsAndConditions: jest.fn().mockResolvedValue(undefined),
      };

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(mockCloud6Service);

      const result = await authService.getTermsAndConditions();

      expect(result).toEqual({
        termsAndConditions: '',
        styles: '',
        source: 'api',
      });
    });

    it('should return default values with source "error" if an exception is thrown', async () => {
      jest.spyOn(helperServiceMock, 'get').mockRejectedValue(new Error('Service failure'));

      const result = await authService.getTermsAndConditions();

      expect(result).toEqual({
        termsAndConditions: '',
        styles: '',
        source: 'error',
      });
    });
  });

  describe('redirectMultiAccount', () => {

  it('should return custom LMS URL if different from default', async () => {
    const user = { email: '<EMAIL>' };
    const lmsInfo = { url: 'custom.lms.com', gid: '123' };

    const mockAuthHelper = {
      generateRedirectLinkToManageRedirect: jest.fn().mockResolvedValue('default-token-url'),
    };

    jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(mockAuthHelper);

    let mockConfigService : any = (key: string) => {
      const config = {
        lmsSiteUrl: 'default.lms.com',
        httpRedirectPrefix: 'https://',
      };
      return config[key];
    }

    jest.spyOn(configServiceMock, 'get').mockImplementation(mockConfigService);

    const result = await authService.redirectMultiAccount(lmsInfo, user as any);
    expect(result).toBe('https://custom.lms.com');
  });

  it('should return token URL if LMS URL is default', async () => {
    const user = { email: '<EMAIL>' };
    const lmsInfo = { url: 'default.lms.com', gid: '123' };

    const mockAuthHelper = {
      generateRedirectLinkToManageRedirect: jest.fn().mockResolvedValue('token-url'),
    };

    jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(mockAuthHelper);
    let mockConfigService : any = (key: string) => {
      const config = {
        lmsSiteUrl: 'default.lms.com',
        httpRedirectPrefix: 'https://',
      };
      return config[key];
    }
    
    jest.spyOn(configServiceMock, 'get').mockImplementation(mockConfigService);

    const result = await authService.redirectMultiAccount(lmsInfo, user as any);
    expect(result).toBe('token-url');
  });
});

  describe('getLoginRedirectUrl', () => {
    let mockAuthHelper: any;
    let mockCookieHelper: any;
    let mockRes: any;

    beforeEach(() => {
      mockAuthHelper = {
        getLoginRedirectUrl: jest.fn(),
      };
      mockCookieHelper = {
        getCalendarCookie: jest.fn(),
        setCookie: jest.fn(),
      };
      mockRes = {
        cookie: jest.fn(),
        redirect: jest.fn(),
      };

      configServiceMock.get = jest.fn().mockImplementation((key) => {
        switch (key) {
          case 'calendarRedirect': return 'calendarRedirect';
          default: return 'mockValue';
        }
      });
    });

    it('should return calendar redirect URL if calendar redirect cookie exists', async () => {
      const cookieBody = { calendarRedirect: 'yes' };
      const loginInfo = { calendar_url: 'https://calendar.example.com' };
      const userResponse = { uid: 12345, email: '<EMAIL>' };

      mockCookieHelper.getCalendarCookie.mockResolvedValue('https://calendar-redirect.example.com');

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          default: return null;
        }
      });

      const result = await authService.getLoginRedirectUrl(cookieBody, mockRes, loginInfo, userResponse);

      expect(result).toBe('https://calendar-redirect.example.com');
      expect(mockCookieHelper.getCalendarCookie).toHaveBeenCalledWith('https://calendar.example.com', mockRes);
      expect(mockAuthHelper.getLoginRedirectUrl).not.toHaveBeenCalled();
    });

    it('should call AuthHelper and return URL when no calendar redirect cookie', async () => {
      const cookieBody = {};
      const loginInfo = { calendar_url: '', redirect_url: 'https://example.com/redirect' };
      const userResponse = { uid: 12345, email: '<EMAIL>' };

      mockAuthHelper.getLoginRedirectUrl.mockResolvedValue({
        url: 'https://redirect.example.com',
        setCookie: false,
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          default: return null;
        }
      });

      const result = await authService.getLoginRedirectUrl(cookieBody, mockRes, loginInfo, userResponse);

      expect(result).toBe('https://redirect.example.com');
      expect(mockAuthHelper.getLoginRedirectUrl).toHaveBeenCalledWith(cookieBody, loginInfo, userResponse);
      expect(mockCookieHelper.setCookie).not.toHaveBeenCalled();
    });

    it('should call AuthHelper and set cookie when setCookie is true', async () => {
      const cookieBody = {};
      const loginInfo = { calendar_url: '', redirect_url: 'https://example.com/redirect' };
      const userResponse = { uid: 12345, email: '<EMAIL>' };

      mockAuthHelper.getLoginRedirectUrl.mockResolvedValue({
        url: 'https://redirect.example.com',
        setCookie: true,
        cookieValue: { name: 'testCookie', value: 'testValue' },
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          default: return null;
        }
      });

      const result = await authService.getLoginRedirectUrl(cookieBody, mockRes, loginInfo, userResponse);

      expect(result).toBe('https://redirect.example.com');
      expect(mockAuthHelper.getLoginRedirectUrl).toHaveBeenCalledWith(cookieBody, loginInfo, userResponse);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(mockRes, 'testCookie', 'testValue');
    });

    it('should handle calendar URL with whitespace', async () => {
      const cookieBody = { calendarRedirect: 'yes' };
      const loginInfo = { calendar_url: '  https://calendar.example.com  ' };
      const userResponse = { uid: 12345, email: '<EMAIL>' };

      mockCookieHelper.getCalendarCookie.mockResolvedValue('https://calendar-redirect.example.com');

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          default: return null;
        }
      });

      const result = await authService.getLoginRedirectUrl(cookieBody, mockRes, loginInfo, userResponse);

      expect(result).toBe('https://calendar-redirect.example.com');
      expect(mockCookieHelper.getCalendarCookie).toHaveBeenCalledWith('https://calendar.example.com', mockRes);
    });

    it('should handle empty calendar URL', async () => {
      const cookieBody = { calendarRedirect: 'yes' };
      const loginInfo = { calendar_url: '' };
      const userResponse = { uid: 12345, email: '<EMAIL>' };

      mockCookieHelper.getCalendarCookie.mockResolvedValue('https://default-redirect.example.com');

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        switch (helperName) {
          case 'AuthHelper': return Promise.resolve(mockAuthHelper);
          case 'CookieHelper': return Promise.resolve(mockCookieHelper);
          default: return null;
        }
      });

      const result = await authService.getLoginRedirectUrl(cookieBody, mockRes, loginInfo, userResponse);

      expect(result).toBe('https://default-redirect.example.com');
      expect(mockCookieHelper.getCalendarCookie).toHaveBeenCalledWith('', mockRes);
    });
  });
describe('getAccountSetupAndOOBUrl', () => {
  it('should return B2C URL when group is not B2B', async () => {
    const user = {
      account_setup: 0,
      email: '<EMAIL>',
      display_name: 'Test User',
      phone_no: '**********',
      country_code: 'IN',
    };

    jest.spyOn(configServiceMock, 'get').mockImplementation((key : nu) => {
      const map = {
        lmsSiteUrl: 'https://lms.test.com',
        accountSetupUrl: 'https://accountsetup.com',
        b2bB2cGroupId: [999],
      };
      return map[key];
    });

    const result = await authService.getAccountSetupAndOOBUrl(user, 555); // B2C
    expect(result).toContain('setupAccount=true');
    expect(result).not.toContain('isB2b=true');
  });

  it('should include B2B params if group is in b2bB2cGroupId', async () => {
    const user = {
      account_setup: 0,
      email: '<EMAIL>',
      display_name: 'Test User',
      phone_no: '**********',
      country_code: 'IN',
    };

    const mockEnterpriseService = {
      getGroupDomainByGid: jest.fn().mockResolvedValue({
        data: [{ lmsSiteUrl: 'https://b2bsite.com' }],
      }),
    };

    jest.spyOn(configServiceMock, 'get').mockImplementation((key) => {
      const map = {
        lmsSiteUrl: 'https://lms.test.com',
        accountSetupUrl: 'https://accountsetup.com',
        b2bB2cGroupId: [123],
      };
      return map[key];
    });

    (helperServiceMock.get as jest.Mock).mockResolvedValue(mockEnterpriseService);

    const result = await authService.getAccountSetupAndOOBUrl(user, 123); // B2B
    expect(result).toContain('&isB2b=true');
    expect(result).toContain('b2bLmsUrl=https://b2bsite.com');
  });
});
describe('socialLoginRedirect', () => {
  it('should return a script to redirect parent window and close popup', () => {
    const target = 'dashboard';
    const expectedScript = `<script>
        if (typeof window.opener == 'object' && window.opener != null && !window.opener.closed) {
          window.opener.location.href = '../${target}';
          window.close();
        } else {
          window.location.href = '../${target}';
        }
      </script>`;

    const result = authService.socialLoginRedirect(target);
    expect(result.trim()).toBe(expectedScript.trim());
  });
});

});
