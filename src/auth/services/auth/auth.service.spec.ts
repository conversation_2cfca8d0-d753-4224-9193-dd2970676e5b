import { Test } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../../../user/services/user.service';
import { Cloud6Service } from '../../../common/services/communication/cloud6/cloud6.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { CachingService } from '../../../caching/caching.service';
import { EmailService } from '../../../common/services/email/email.service';
import { BadRequestException } from '@nestjs/common';
import { AuthTokenHelper } from '../../../auth/helper/auth.tokenhelper';
import { LoginDto } from '../../../auth/dtos/login.dto';
import { User } from '../../../db/mongo/schema/user/user.schema';
import { UserRepository } from '../../../user/repositories/user/user.repository';
import { PaperclipService } from '../../../common/services/communication/paperclip/paperclip.service';


describe('AuthService', () => {
  let authService: AuthService;
  let configServiceMock: ConfigService;
  let helperServiceMock: HelperService;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AuthService,
        HelperService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: CachingService,
          useValue: {},
        },
        {
          provide: UserService,
          useValue: {},
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: {},
        },
        {
          provide: Cloud6Service,
          useValue: {},
        },
        {
          provide: EmailService,
          useValue: {},
        },
      ],
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
    configServiceMock = moduleRef.get<ConfigService>(ConfigService);
    helperServiceMock = moduleRef.get<HelperService>(HelperService);
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
  });

  describe('signUp', () => {
    it('should successfully sign up a user and return redirectUri', async () => {
      // Mock dependencies and required methods
      const signupDetail: Partial<User> = {
        /* Provide mock data here */
      };
      const redirectUri = 'mockRedirectUri';
      const cookieData = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      configServiceMock.get = jest.fn().mockResolvedValue('client_id');
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'lrsHelper') {
          return Promise.resolve({ sendDataToLrs: jest.fn() });
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve({
            userRegistration: jest.fn().mockResolvedValueOnce({ uid: 'mockUserId' }),
            handleUserSignup: jest.fn(),
            getSignupRedirect: jest
              .fn()
              .mockResolvedValueOnce({ redirectUrl: 'mockRedirectUrl', setCookie: true, cookieValue: [] }),
          });
        } else if (helperName === 'CookieHelper') {
          return Promise.resolve({ setBulkCookie: jest.fn(), setCookie: jest.fn() });
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({ updateUserLoginTime: jest.fn() });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            generateSessionTokens: jest.fn().mockResolvedValueOnce({ idToken: 'mockIdToken', userData: {} }),
          });
        }
        return null;
      });

      const result = await authService.signUp(signupDetail, redirectUri, cookieData, res as any);

      expect(result).toBe('mockRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const signupDetail: Partial<User> = {
        /* Provide mock data here */
      };
      const redirectUri = 'mockRedirectUri';
      const cookieData = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;

      jest.spyOn(authService['helperService'], 'getHelper').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.signUp(signupDetail, redirectUri, cookieData, res as any)).rejects.toThrow(
        BadRequestException,
      );
      // Add more assertions based on your specific use case
    });
  });
  
  describe('manageAuthRedirect', () => {
    it('should successfully manage authentication redirect', async () => {
      // Mock dependencies and required methods
      const cookieBody = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      const token = 'mockToken';
      const gid = 'mockGid';
      configServiceMock.get = jest.fn().mockResolvedValue('client_id');

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            decodeJWTToken: jest.fn().mockResolvedValueOnce({
              data: {
                /* Mock decoded data */
              },
            }),
          });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve({ setBulkCookie: jest.fn(), setCookie: jest.fn(), clearBulkCookie: jest.fn() });
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({
            handleB2bB2cRedirect: jest.fn().mockResolvedValueOnce('mockFinalRedirectUrl'),
            handleSkillupCookieActions: jest.fn().mockResolvedValueOnce({
              redirectUrl: 'mockRedirectUrl',
              cookieValue: [],
              clearCookie: true,
              clearCookieData: [], // Provide mock clearCookieData here
            }),
          });
        }
        return null;
      });

      const result = await authService.manageAuthRedirect(cookieBody, res as any, token, gid);

      expect(result).toBe('mockFinalRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const cookieBody = {
        /* Provide mock cookie data here */
      };
      const res: Response = {} as Response;
      const token = 'mockToken';
      const gid = 'mockGid';

      jest.spyOn(authService['helperService'], 'get').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.manageAuthRedirect(cookieBody, res as any, token, gid)).rejects.toThrow(
        BadRequestException,
      );
      // Add more assertions based on your specific use case
    });
  });

  describe('handleUserSsoCookie', () => {
    it('should successfully handle user SSO cookie and return redirectUrl', async () => {
      // Mock dependencies and required methods
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      const ssoCookie = 'mockSsoCookie';

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            decodeJWTToken: jest.fn().mockResolvedValueOnce({
              data: {
                /* Mock decoded data */
              },
            }),
          });
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve({
            setBulkCookie: jest.fn(),
            setCookie: jest.fn(),
            clearBulkCookie: jest.fn(),
            getRedirectUrlFromCookie: jest.fn().mockResolvedValueOnce('mockFinalRedirectUrl'),
          });
        }
        return null;
      });

      const result = await authService.handleUserSsoCookie(queryParam, ssoCookie);

      expect(result).toBe('mockFinalRedirectUrl');
      // Add more assertions based on your specific use case
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      const ssoCookie = 'mockSsoCookie';

      jest.spyOn(authService['helperService'], 'get').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.handleUserSsoCookie(queryParam, ssoCookie)).rejects.toThrow(BadRequestException);
      // Add more assertions based on your specific use case
    });
  });

  describe('getRedirectUrl', () => {
    it('should successfully get redirect URL and set cookies', async () => {
      // Mock dependencies and required methods
      const res: Response = {} as Response;
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };
      configServiceMock.get = jest.fn().mockResolvedValue('lmsSiteUrl');
      const cookieHelpers = {
        setFermiumCookie: jest.fn(),
        setCalenderCookie: jest.fn(),
        setNpsCookie: jest.fn(),
        setCommunityCookie: jest.fn(),
      };
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'CookieHelper') {
          return Promise.resolve(cookieHelpers);
        } else if (helperName === 'UserHelper') {
          return Promise.resolve({
            getDomainUrlInfo: jest.fn().mockResolvedValue({
              redirectUrl: 'mockValidRedirectUrl',
              calendarUrl: 'mockCalendarUrl',
              isB2BAndB2C: true,
              domainGid: 'mockDomainGid',
              domainUrl: 'domainUrl',
            }),
          });
        }
        return null;
      });

      const result = await authService.getRedirectUrl(res as any, queryParam);

      expect(result.calendarUrl).toEqual('mockCalendarUrl');
      // Ensure that setFermiumCookie, setCalenderCookie, setNpsCookie, and setCommunityCookie are called
      expect(cookieHelpers.setFermiumCookie).toHaveBeenCalledWith('mockAssignmentToken', res);
      expect(cookieHelpers.setCalenderCookie).toHaveBeenCalledWith('mockCalendarUrl', res);
      // expect(cookieHelpers.setNpsCookie).toHaveBeenCalledWith('mockValidRedirectUrl', res);
      // expect(cookieHelpers.setCommunityCookie).toHaveBeenCalledWith('mockValidRedirectUrl', res);
    });

    it('should throw BadRequestException on error', async () => {
      // Mock dependencies and required methods to throw an error
      const res: Response = {} as Response;
      const queryParam = {
        calendar_url: 'mockCalendarUrl',
        assignmentToken: 'mockAssignmentToken',
        redirect_url: 'mockRedirectUrl',
      };

      jest.spyOn(authService['helperService'], 'getHelper').mockRejectedValueOnce(new Error('Mocked error'));

      await expect(authService.getRedirectUrl(res as any, queryParam)).rejects.toThrow(BadRequestException);
      // Add more assertions based on your specific use case
    });
  });

  describe('authenticateUser', () => {
    it('should successfully authenticate user and set SSO cookie', async () => {
      // Mock dependencies and required methods
      const res: Response = {} as Response;
      const loginDto: LoginDto = {
        email: '<EMAIL>',
        password: '2342453',
      };
      const clientKey = 'mockClientKey';
      const cookieHelper = { setCookie: jest.fn() };
      const lrsHelper = { sendDataToLrs: jest.fn() };
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') {
          return Promise.resolve({
            authenticateUser: jest.fn().mockResolvedValueOnce({ uid: 'mockUserId', userData: {} } as unknown as User),
          });
        } else if (helperName === 'CookieHelper') {
          return Promise.resolve(cookieHelper);
        } else if (helperName === 'lrsHelper') {
          return Promise.resolve(lrsHelper); // Use the mocked lrsHelper here
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve({
            generateSessionTokens: jest.fn().mockResolvedValueOnce({ idToken: 'mockIdToken', userData: {} }),
          });
        }
        return null;
      });

      configServiceMock.get = jest.fn().mockResolvedValue('ssocCookie');

      const result = await authService.authenticateUser(res as any, loginDto, clientKey);

      expect(result).toEqual({ uid: 'mockUserId', userData: {} });
      configServiceMock.get = jest.fn().mockResolvedValue('maxAge');

      // Ensure that setCookie and sendDataToLrs are called
      // expect(await cookieHelper.setCookie).toHaveBeenCalledWith(res, 'ssoCookie', 'mockIdToken',{"expires":  Promise.resolve({})});

      expect(lrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        {},
        {
          verb: 'login',
          objectType: 'accounts',
          objectId: 'mockUserId',
          dataVals: {
            client_id: 'mockClientKey',
            redirect_url: loginDto?.redirect_url,
          },
        },
      );
    });

    it('should return an error if authentication fails', async () => {
      // Mock dependencies and required methods to throw an error
      const res: Response = {} as Response;
      const loginDto: LoginDto = {
        email: '',
        password: '',
      };
      const clientKey = 'mockClientKey';

      jest.spyOn(authService['helperService'], 'getHelper').mockResolvedValueOnce({
        authenticateUser: jest.fn().mockRejectedValueOnce(new Error('Mocked authentication error')),
      });

      const result = await authService.authenticateUser(res as any, loginDto, clientKey);

      expect(result).toBeInstanceOf(Error);
      // expect(result.message).toBe('Mocked authentication error');
      // Add more assertions based on your specific use case
    });
  });

  describe('forgetPassword', () => {
    const configServiceMock = {
      get: jest.fn(),
    };
    it('should successfully handle forget password flow and send email', async () => {
      // Mock dependencies and required methods
      const email = '<EMAIL>';
      const client_id = 'mock-client-id';
      const user = {
        uid: 'mockUserId',
        email: '<EMAIL>',
        display_name: 'Test User',
        roles: ['learner'],
        status: true,
      };
      
    
      // const resetResponse = { url: 'mockResetUrl', token: 'mockResetToken' };
      const accountSetupUrl = 'mockAccountSetupUrl';
      const emailHelper = { sendEmail: jest.fn().mockResolvedValueOnce(true) };
      const authHelper = {
        forgotPasswordAttemptLimit: jest.fn().mockResolvedValueOnce({ status: 'success' }),
      };
      const lrsHelper = { sendDataToLrs: jest.fn() };
      const userRepository = {
        getUserByEmail: jest.fn().mockResolvedValueOnce(user),
      };
      const cachingService = {
        set: jest.fn(),
      };
      const paperclipService = {
        invalidateTokenForAppUser: jest.fn().mockResolvedValueOnce(true),
      };
      configServiceMock.get.mockReturnValue(2)
      jest.spyOn(helperServiceMock, 'get').mockImplementation((service: any) => {
        if (service === UserRepository) {
          return Promise.resolve(userRepository);
        } else if (service === CachingService) {
          return Promise.resolve(cachingService);
        }else if (service === PaperclipService) return Promise.resolve(paperclipService);
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'lrsHelper') {
          return Promise.resolve(lrsHelper);
        } else if (helperName === 'EmailHelper') {
          return Promise.resolve(emailHelper);
        } else if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelper);
        }
        return null;
      });

      jest.spyOn(authService, 'getAccountSetupAndOOBUrl').mockResolvedValueOnce(accountSetupUrl);
     
      const result = await authService.forgetPassword(email);

      expect(result).toBe(true);

      // Ensure that methods are called with the expected parameters
      expect(userRepository.getUserByEmail).toHaveBeenCalledWith(email);
      expect(authService['getAccountSetupAndOOBUrl']).toHaveBeenCalledWith(user, 2);
      // expect(authService['getUserPassResetUrl']).toHaveBeenCalledWith(user?.uid);
      // expect(cachingService.set).toHaveBeenCalledWith(`${user?.uid}`, resetResponse?.token, expect.any(Number));
      expect(lrsHelper.sendDataToLrs).toHaveBeenCalledWith(
        {
          id: user?.uid,
          email: user?.email,
          name: user?.display_name || '',
          roles: user?.roles,
        },
        {
          verb: 'forgot-password',
          objectType: 'accounts',
          objectId: user?.uid,
          dataVals: {
            client_id: client_id,
          },
        },
      );
      expect(emailHelper.sendEmail).toHaveBeenCalledWith(email, 'newLearnerAccountSetupEmail', {
        name: user.display_name,
        redirect_link: accountSetupUrl,
      });
      expect(paperclipService.invalidateTokenForAppUser).toHaveBeenCalledWith(user.uid)
    });
    it('should throw UserNotFoundException if user is not found', async () => {
      const userRepository = { getUserByEmail: jest.fn().mockResolvedValue(null) };
      jest.spyOn(helperServiceMock, 'get').mockResolvedValueOnce(userRepository);
  
      await expect(authService.forgetPassword('<EMAIL>')).rejects.toThrow('UserNotFoundException');
    });
    it('should throw UserDisabled if user is inactive', async () => {
      const inactiveUser = { ...User, status: false };
      const userRepository = { getUserByEmail: jest.fn().mockResolvedValueOnce(inactiveUser) };
      jest.spyOn(helperServiceMock, 'get').mockResolvedValueOnce(userRepository);
  
      await expect(authService.forgetPassword('<EMAIL>')).rejects.toThrow('UserDisabled');
    });
    it('should throw AttemptLimitExceed if limit exceeded', async () => {
      const email = '<EMAIL>';
      const user = {
        uid: 'mockUserId',
        email,
        display_name: 'Test User',
        roles: ['learner'],
        status: true,
      };
      const userRepository = { getUserByEmail: jest.fn().mockResolvedValueOnce(user) };
      const authHelper = {
        forgotPasswordAttemptLimit: jest.fn().mockResolvedValueOnce({ status: 'limit_exceeded' }),
      };
      jest.spyOn(helperServiceMock, 'get').mockImplementation(async (token) => {
        if (token === UserRepository) return userRepository;
        return null;
      });
      jest.spyOn(helperServiceMock, 'getHelper').mockResolvedValue(authHelper);
  
      await expect(authService.forgetPassword(email)).rejects.toThrow('AttemptLimitExceed');
    });
    it('should use reset password token flow and cache it if no accountSetupUrl is found', async () => {
      const email = '<EMAIL>';
      const user = {
        uid: 'mockUserId',
        email,
        display_name: 'Test User',
        roles: ['learner'],
        status: true,
      };
      const resetResponse = { url: 'reset-url', token: 'reset-token' };
      const param = { fm: 1, appType: 'web', client_id: 'client-123' };
    
      const authHelper = {
        forgotPasswordAttemptLimit: jest.fn().mockResolvedValue({ status: 'success' }),
        getUserPassResetUrl: jest.fn().mockResolvedValue({ url: 'reset-url', token: 'reset-token' }),
      };
      const emailHelper = {
        sendEmail: jest.fn().mockResolvedValue(true),
      };
      const cachingService = {
        set: jest.fn(),
      };
      const paperclipService = {
        invalidateTokenForAppUser: jest.fn().mockResolvedValue(true),
      };
  
      const userRepository = { getUserByEmail: jest.fn().mockResolvedValueOnce(user) };
  
      jest.spyOn(helperServiceMock, 'get').mockImplementation((token) => {
        switch (token) {
          case UserRepository: return Promise.resolve(userRepository);
          case CachingService: return Promise.resolve(cachingService);
          case PaperclipService: return Promise.resolve(paperclipService);
        }
      });
  
      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((name) => {
        switch (name) {
          case 'AuthHelper': return Promise.resolve(authHelper);
          case 'EmailHelper': return Promise.resolve(emailHelper);
          case 'lrsHelper': return Promise.resolve({ sendDataToLrs: jest.fn() });
        }
      });
    

      jest.spyOn(authService, 'getAccountSetupAndOOBUrl').mockResolvedValueOnce(null); // simulate no OOB URL
  
      const result = await authService.forgetPassword(email, param);
  
      expect(result).toBe(true);
      expect(emailHelper.sendEmail).toHaveBeenCalledWith(email, 'forgotPassword', {
        resetPasswordUrl: `${resetResponse.url}?fm=1&appType=web`,
        displayName: user.display_name,
      });
      expect(authHelper.getUserPassResetUrl).toHaveBeenCalledWith(user.uid);
      expect(emailHelper.sendEmail).toHaveBeenCalledWith(email, 'forgotPassword', expect.any(Object));
      expect(cachingService.set).toHaveBeenCalledWith(`${user.uid}`, 'reset-token', expect.any(Number));
      expect(paperclipService.invalidateTokenForAppUser).toHaveBeenCalledWith(user.uid);
    });
  });
});
