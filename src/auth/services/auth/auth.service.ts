import { Injectable, Inject, BadRequestException, Res, Query, Body } from '@nestjs/common';
import { Logger } from '../../../logging/logger';
import { ConfigService } from '@nestjs/config';
import { AuthTokenHelper } from '../../helper/auth.tokenhelper';
import { Crypto<PERSON>elper } from '../../../helper/helper.crypto';
import { HelperService } from '../../../helper/helper.service';
import { User } from '../../../db/mongo/schema/user/user.schema';
import { Response } from 'express';
import { IUserRepository, UserRepository } from '../../../user/repositories/user/user.repository';
import { LoginDto } from '../../../auth/dtos/login.dto';
import { CachingService } from '../../../caching/caching.service';
import * as drupalHash from 'drupal-hash';
import { PaperclipService } from '../../../common/services/communication/paperclip/paperclip.service';
import { CurrentUser, CustomResponse, ResponseCookieType } from '../../../common/typeDef/auth.type';
import { Utility } from '../../../common/util/utility';
import { UserService } from '../../../user/services/user.service';
import { EnterpriseService } from '../../../common/services/communication/enterprise/enterprise.service';
import { Cloud6Service } from '../../../common/services/communication/cloud6/cloud6.service';


@Injectable()

export class AuthService {
  @Inject() private readonly configService: ConfigService;
  @Inject(HelperService) private readonly helperService: HelperService;
  @Inject(UserService) private readonly userService: UserService;
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;

  /**
   * Sign up a user in mongo and sync the user in cloud6.
   * @returns The user data saved in MongoDB.
   */
  async signUp(signupDetail: Partial<User>, redirectUri: string, cookieData, @Res() res: Response): Promise<string> {
    try {
      const userInfo = await this.userService.userRegistration(signupDetail);
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const userTokenDetail = await authTokenHelper.generateSessionTokens(
        userInfo,
        this.configService.get('clientKey'),
      );
      //LRS Logging
      const lrsData = {
        verb: 'register',
        objectType: 'accounts',
        objectId: userInfo?.uid,
        dataVals: {
          client_id: this.configService.get('clientKey'),
          redirect_url: redirectUri,
        },
      };
      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(userTokenDetail['userData'], lrsData);
      //LRS Logging ends

      // Get AuthTokenHelper instance
      if (userTokenDetail?.idToken) {
        const utm_source: string = Utility.isEmpty(cookieData?.sl_su_utmz)
          ? this.configService.get<string>('defaultFreemiumSignupUtm')
          : cookieData?.sl_su_utmz;
        const referralCode: string = Utility.isEmpty(cookieData?.skillup_referral_code)
          ? ''
          : cookieData?.skillup_referral_code;

        const authHelper = await this.helperService.getHelper('AuthHelper');
        authHelper.handleUserSignup(utm_source, referralCode, userInfo);

        const signupRedirectData = await authHelper.getSignupRedirect(cookieData, userInfo, redirectUri);
        redirectUri = signupRedirectData?.redirectUrl;
        // Set authentication cookie
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        if (signupRedirectData.setCookie) {
          signupRedirectData.cookieValue.push({ name: 'ssoCookie', value: userTokenDetail?.idToken });
          cookieHelper.setBulkCookie(res, signupRedirectData.cookieValue);
        }
        cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenDetail?.idToken);
        const userHelper = await this.helperService.getHelper('UserHelper');
        await userHelper.updateUserLoginTime(userInfo);
      }
      return redirectUri;
    } catch (error: any) {
      // Log and handle errors
      Logger.error('signUp', {
        METHOD: `${this.constructor?.name}@${this.signUp?.name}`,
        MESSAGE: error.message,
        REQUEST: signupDetail,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  /**
   * this method manage auth redirection
   */
  async manageAuthRedirect(cookieBody, @Res() res: Response, token: string, gid: string): Promise<string> {
    try {
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const decodedData = await authTokenHelper.decodeJWTToken(token);
      const userHelper = await this.helperService.getHelper('UserHelper');
      const data = decodedData?.['data'] || {};
      let redirectUrl = Utility.getValidRedirectUrl(data, this.configService.get('lmsSiteUrl'));
      data.redirect_url = redirectUrl;
      const skillUpCookieData = await userHelper.handleSkillupCookieActions(cookieBody, data);
      redirectUrl = skillUpCookieData.redirectUrl;
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      cookieHelper.setBulkCookie(res, skillUpCookieData.cookieValue);

      if (skillUpCookieData?.clearCookie) {
        cookieHelper.clearBulkCookie(res, skillUpCookieData?.clearCookieData);
      }

      redirectUrl = await userHelper.handleB2bB2cRedirect(gid, redirectUrl, res);
      return redirectUrl;
    } catch (error: any) {
      Logger.error('manageAuthRedirect', {
        METHOD: `${this.constructor?.name}@${this.manageAuthRedirect?.name}`,
        MESSAGE: error.message,
        REQUEST: { token, gid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  async handleUserSsoCookie(
    queryParam: { calendar_url: string; assignmentToken: string; redirect_url: string },
    ssoCookie: string,
  ): Promise<string> {
    try {
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      const authTokenHelper: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const userSsoCookieDecode = await authTokenHelper.decodeJWTToken(ssoCookie);
      const redirectUrl = await cookieHelper.getRedirectUrlFromCookie(queryParam, {
        id: userSsoCookieDecode?.data?.id,
        email: userSsoCookieDecode?.data?.email,
        name: userSsoCookieDecode?.data?.name,
      });
      return redirectUrl;
    } catch (error: any) {
      Logger.log('handleUserSsoCookie', {
        METHOD: `${this.constructor?.name}@${this.manageAuthRedirect?.name}`,
        MESSAGE: error.message,
        REQUEST: { queryParam },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  async getRedirectUrl(
    @Res() res: Response,
    queryParam: { calendar_url: string; assignmentToken: string; redirect_url: string },
  ): Promise<{
    redirectUrl: string;
    calendarUrl: string;
    isB2BAndB2C: boolean;
    domainGid: string;
    domainUrl: string;
  }> {
    try {
      const calendarUrl: string = queryParam?.calendar_url || '';
      let redirectUrl: string = queryParam?.redirect_url || '';
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      redirectUrl = Utility.validateRedirectUrl(redirectUrl);

      cookieHelper.setFermiumCookie(queryParam?.assignmentToken, res);
      cookieHelper.setCalenderCookie(calendarUrl, res);
      cookieHelper.setNpsCookie(redirectUrl, res);
      cookieHelper.setCommunityCookie(redirectUrl, res);

      const userHelper = await this.helperService.getHelper('UserHelper');
      const { isB2BAndB2C, domainGid, domainUrl } = await userHelper.getDomainUrlInfo(redirectUrl);

      redirectUrl = redirectUrl || this.configService.get('lmsSiteUrl');
      return { redirectUrl, calendarUrl, isB2BAndB2C, domainGid, domainUrl };
    } catch (error: any) {
      Logger.error('getRedirectUrl', {
        METHOD: `${this.constructor?.name}@${this.getRedirectUrl?.name}`,
        MESSAGE: error.message,
        REQUEST: queryParam,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  /**
   *
   * @param loginDetail
   * @returns
   */
  async authenticateUser(@Res() res: Response, loginDto: LoginDto, clientKey: string): Promise<Partial<User> | Error> {
    try {
      const [authHelper, userHelper, authTokenHelper, cookieHelper] = await Promise.all([
        this.helperService.getHelper('AuthHelper'),
        this.helperService.getHelper('UserHelper'),
        this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.getHelper('CookieHelper'),
      ]);
      const user = await authHelper.authenticateUser(loginDto);
      const userTokenDetail = await authTokenHelper.generateSessionTokens(user, clientKey);
      await Promise.all([
        cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenDetail['idToken'], {
          expires: this.configService.get<Date>('maxAge'),
        }),
        userHelper.updateUserLoginTime(user),
      ]);

      
      //Send data to lrs start
      const lrsData = {
        verb: 'login',
        objectType: 'accounts',
        objectId: user?.uid,
        dataVals: {
          client_id: clientKey,
          redirect_url: loginDto?.redirect_url,
        },
      };

      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(userTokenDetail['userData'], lrsData);
      //Send data to lrs end

      return user;
    } catch (error: any) {
      Logger.error('authenticateUser', {
        METHOD: `${this.constructor?.name}@${this.authenticateUser?.name}`,
        MESSAGE: error.message,
        REQUEST: loginDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return new Error(error.message);
    }
  }

  /**
   *
   * @param email
   * @returns
   */
  async forgetPassword(email: string, param?: { fm?: number; appType?: string }): Promise<boolean | Error> {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(email);
      if (!user || user === null) {
        throw new BadRequestException('UserNotFoundException');
      }
      if (!user.status) {
        throw new BadRequestException('UserDisabled');
      }
      const authHelper = await this.helperService.getHelper('AuthHelper');
      const attemptResponse = await authHelper.forgotPasswordAttemptLimit(email);
      if (attemptResponse?.status === 'limit_exceeded') {
        throw new BadRequestException('AttemptLimitExceed');
      }
      // sending to data to lrs
      const lrsData = {
        verb: 'visit',
        objectType: 'forgot-password',
        dataVals: {
          client_id: this.configService.get('ssoClientId'),
        },
      };
      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(null, lrsData);
      // End LRS Data
     
      const emailHelper = await this.helperService.getHelper('EmailHelper');
      const accountSetupUrl = await this.getAccountSetupAndOOBUrl(
        user,
        this.configService.get<number>('defaultUserGroupId'),
      );
      if (accountSetupUrl) {
        const emailData = {
          name: user.display_name,
          redirect_link: accountSetupUrl,
        };
        return await emailHelper.sendEmail(email, 'newLearnerAccountSetupEmail', emailData);
      }
      const resetResponse = await authHelper.getUserPassResetUrl(user?.uid); 
      const emailResponse = await emailHelper.sendEmail(email, 'forgotPassword', {
        resetPasswordUrl: param?.fm === 1 ? `${resetResponse?.url}?fm=1&appType=${param?.appType}` : resetResponse?.url,
        displayName: user?.display_name,
      });
      if (emailResponse) {
        const cacheService = await this.helperService.get<CachingService>(CachingService);
        await cacheService.set(`${user?.uid}`, resetResponse?.token, 60 * 60 * 24); //store the token in memcache for expiry
      
      }
      return emailResponse ? true : false;
  } catch (error: any) {
      Logger.error('forgetPassword', {
        METHOD: this.constructor.name + '@' + this.forgetPassword.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  /**
   *
   * @param email
   * @param confirmationCode
   * @param newPassword
   * @returns
   */
  async resetPassword(resetPwd: { password: string }, params): Promise<boolean> {
    try {
      const uid = this.cryptoHelper.decrypt(params['uid']);
      const hashPassword = drupalHash.hashPassword(resetPwd?.password);
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.findOneAndUpdate({ uid: uid }, { user_options: 1, password: hashPassword });
      const userHelper = await this.helperService.getHelper('UserHelper');
      const passStatus = await userHelper.updateCloud6SentinelByUidOrMail({uid , pass: hashPassword, user_options: 1});

      if (this.configService.get('enableDrupalSync')) {
        await userHelper.syncUserDataWithMySQLDrupal({
          uid,
          password: resetPwd?.password,
          user_options: 1,
        });
      }
      if(!passStatus || !user){
        Logger.error('resetPassword', {
          METHOD: this.constructor.name + '@' + this.resetPassword.name,
          MESSAGE: "Password not changed",
          REQUEST: params,
          TIMESTAMP: new Date().getTime(),
        });
        // LRS logging starts
        const userLrsData = {
          id: user.uid,
          email: user.email,
          name: user.display_name || '',
          roles: user.roles,
        }
        const lrsData = {
          verb: 'reset-password',
          objectType: 'accounts',
          objectId: uid,
          dataVals: {
            client_id: this.configService.get('ssoClientId'),
            from_mobile: params?.fm,
            appType: "",
          },
        };
        lrsData.dataVals.appType = params?.fm === 1 ? params?.appType : '';
        const lrsInstance = await this.helperService.getHelper('lrsHelper');
        lrsInstance.sendDataToLrs(userLrsData, lrsData);
        // LRS Logging ends
        return false;
      }
      return true;
    } catch (error: any) {
      Logger.error('resetPassword', {
        METHOD: this.constructor.name + '@' + this.resetPassword.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
 
  async getUserIdpInfo(user, origin, res) {
    const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
    const linkDataCookieName = this.configService.get('linkData');
    const cookieHelper = await this.helperService.getHelper('CookieHelper');
    try {
      const showRegisterComplete = !Utility.isEmpty(origin) && origin === 'register' ? true : false;
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userInfo = await userRepository.getUserByEmail(user?.email);
      if (userInfo === null) {
        const linkData = { type: user?.type, email: user?.email, sub: user?.sub, source: 'web', code: 0 };
        await cookieHelper.setCookie(res, linkDataCookieName, JSON.stringify(linkData));
        if (showRegisterComplete) {
          return this.socialLoginRedirect('register-complete');
        }
        return this.socialLoginRedirect('social-link');
      }
      const typeInfo: {
        status: boolean;
        code: number;
        msg: string;
      } = await socialLoginHelper.userSocialStatus(user?.email, user?.type, userInfo);
      let redirectName = 'login';
      if (typeInfo?.status) {
        const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
        const userTokenDetail = await authTokenHelper.generateSessionTokens(
          userInfo,
          this.configService.get('clientKey'),
        );
        await cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenDetail.idToken);
        const AuthHelper = await this.helperService.getHelper('AuthHelper');
        const tokenRedirectUrl: string = await AuthHelper.generateRedirectLinkToManageRedirect(user, 'login', 'email');
        redirectName = '..' + tokenRedirectUrl;
      } else {
        const linkData = { type: user?.type, email: user?.email, sub: user?.sub, source: 'web', code: typeInfo?.code };
        await cookieHelper.setCookie(res, linkDataCookieName, JSON.stringify(linkData));
        redirectName = 'social-link';
      }

      return this.socialLoginRedirect(redirectName);
    } catch (error: any) {
      if (error.message === 'UserNotFoundException') {
        const linkData = { type: user?.type, email: user?.email, sub: user?.sub, source: 'web', code: 0 };
        await cookieHelper.setCookie(res, linkDataCookieName, JSON.stringify(linkData));
        return this.socialLoginRedirect('social-link');
      }
      Logger.error(this.getUserIdpInfo?.name, {
        METHOD: this.constructor?.name + '@' + this.getUserIdpInfo?.name,
        MESSAGE: error.message,
        REQUEST: { email: user?.email },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('somethingWentWrong');
    }
  }

  async socialLink(cookieData, res, linkData) {
    try {
      const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
      delete linkData['code'];
      const response: CustomResponse & { setCookie: boolean; cookieValue?: ResponseCookieType[] } =
        await socialLoginHelper.processUserLink(linkData, cookieData);

      if (response.setCookie) {
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        cookieHelper.setBulkCookie(res, response.cookieValue);
        delete response['cookieValue'];
      }

      delete response['setCookie'];
      return response;
    } catch (error: any) {
      Logger.error('socialLink', {
        METHOD: this.constructor?.name + '@' + this.socialLink?.name,
        MESSAGE: error.message,
        REQUEST: { linkData: linkData },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, data: '', msg: 'Something went wrong, please try again.' };
    }
  }
  async accountSetUp(cookieBody, @Query() queryParam, userEmail) {
    try {
      const userSsoCookie = cookieBody[this.configService.get('ssoCookie')] || '';
      const [userHelper, userRepository] = await Promise.all([
        this.helperService.getHelper('UserHelper'),
        this.helperService.get<IUserRepository>(UserRepository),
      ]);

      const dataPassToView = await userHelper.handleAccountSetup(queryParam);
      if (!dataPassToView?.status) {
        return { status: false, redirectUrl: '', msg: 'somethingWentWrong' };
      }

      if (!Utility.isEmpty(userSsoCookie)) {
        const urlToRedirect = await userHelper.getSSoCookieRedirectUrl(queryParam, userSsoCookie);
        if (!Utility.isEmpty(urlToRedirect)) {
            return { status: true, redirectUrl: urlToRedirect, msg: 'success' };
        } else {
          return { status: false, redirectUrl: '', msg: 'unauthorizedEmail' , data: dataPassToView.data };
        }
      }

      
      const userObjCheck = await userRepository.getUserByEmail(userEmail);
      // Learner already setup the account, redirecting user to onboarding page
      if (userObjCheck.account_setup === 1 && dataPassToView.status) {
        return { status: true, redirectUrl: dataPassToView?.data?.validateRedirectUrl, msg: 'success' };
      }
      userHelper.updateUserLoginTime(userObjCheck);
      return { status: false, redirectUrl: '', msg: 'success', data: dataPassToView.data };
    } catch (error: any) {
      Logger.error('resetPassword', {
        METHOD: this.constructor.name + '@' + this.resetPassword.name,
        MESSAGE: error.message,
        REQUEST: { queryParam: queryParam },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, redirectUrl: '', msg: 'error' };
    }
  }

  /**
   * @param res Express response object
   * @param formData Form data containing user registration details
   * @param linkData Data associated with the link used for registration (e.g., type, email)
   * @returns Result of the registration process
   *
   * @description Handles the completion of the user registration process.
   *
   */
  async registerCompletePost(cookieData, @Res() res, @Body() formData, linkData) {
    try {
      const userHelper = await this.helperService.getHelper('UserHelper');
      const signupDetail: Partial<User> = {
        first_name: formData?.first_name,
        last_name: formData?.last_name,
        email: formData?.email,
        password: userHelper.getUserPassword(),
        phone_no: formData?.phone_no,
        timezone: JSON.parse(formData?.country_data)?.timeZone,
        country_code: JSON.parse(formData?.country_data)?.code,
        accept_agreement: formData?.termCondition && formData?.termCondition === 'Y' ? true : false,
        name: formData?.email,
        display_name: (formData?.first_name || '') + ' ' + (formData?.last_name || ''),
        language: 'en',
        status: 1,
        user_category: this.configService.get('freeUserType'),
        account_setup: this.configService.get('userOptionsAccountSetupComplete'),
        password_created: this.configService.get('userOptionsAccountSetupComplete'),
      };

      const user = await this.userService.userRegistration(signupDetail);
      const utm_source: string = Utility.isEmpty(cookieData?.sl_su_utmz)
        ? this.configService.get<string>('defaultFreemiumSignupUtm')
        : cookieData?.sl_su_utmz;
      const referralCode: string = Utility.isEmpty(cookieData?.skillup_referral_code)
        ? ''
        : cookieData?.skillup_referral_code;
      const authHelper = await this.helperService.getHelper('AuthHelper');
      authHelper.handleUserSignup(utm_source, referralCode, user);
      return await this.socialLink(cookieData, res, linkData);
    } catch (error: any) {
      Logger.error('registerCompletePost', {
        METHOD: this.constructor?.name + '@' + this.registerCompletePost?.name,
        MESSAGE: error.message,
        REQUEST: [linkData?.type, linkData?.email],
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }
  /**
   * Perform post-account setup operations.
   *
   * @param res Response object for setting `cookies` and `redirects`
   * @param accountSetupInfo Account setup information
   * @returns Object containing status, message, and data for redirect information
   */
  async accountSetupPostOperations(
    @Res() res,
    accountSetupInfo,
  ): Promise<{
    status: boolean;
    msg: string;
    data: {
      redirect_url: string;
      redirectTo: string;
    };
  }> {
    let response: { status: boolean; msg: string; data: { redirect_url: string; redirectTo: string } } = {
      status: false,
      msg: 'Something went wrong, please try again.',
      data: { redirect_url: '', redirectTo: '' },
    };
    try {
      const clientKey = this.configService.get<string>('clientKey');
      Utility.validateClientRequest(clientKey, this.configService.get('clientSecret'));

      const emailAddress: string = accountSetupInfo?.email || '';
      let userPassword: string = accountSetupInfo?.password || '';

      const isB2bStudentFrontend: boolean =
        !Utility.isEmpty(accountSetupInfo?.isB2bStudent) && accountSetupInfo?.isB2B == 'true' ? true : false;

      const ssoRequestFrontend: boolean =
        !Utility.isEmpty(accountSetupInfo?.isSso) && accountSetupInfo?.isSso == 'true' ? true : false;
      const isB2B = accountSetupInfo?.isB2B == 'true' || false;
      const b2bLmsUrl: string = accountSetupInfo?.b2bLmsUrl || '';
      const redirect_url: string = accountSetupInfo?.redirect_url || '';
      const userHelper = await this.helperService.getHelper('UserHelper');

      if (isB2bStudentFrontend && ssoRequestFrontend) {
        userPassword = userHelper.getUserPassword();
      }

      const validateResult: { status: boolean; msg?: string } = await userHelper.validateUserAccountSetup(
        accountSetupInfo,
        isB2bStudentFrontend,
      );

      if (!validateResult?.status) {
        return response;
      }
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userObj = await userRepository.getUserByEmail(emailAddress);

      if (!userObj) {
        response.msg = 'Unauthorized email address';
        return response;
      }
    
      accountSetupInfo['account_setup'] = this.configService.get('userOptionsAccountSetupComplete');
      accountSetupInfo['password'] = drupalHash.hashPassword(accountSetupInfo?.password)
      await Promise.all(
        [
          userHelper.updateUserData(accountSetupInfo, isB2bStudentFrontend && ssoRequestFrontend),
          // Update password in cloud6 sentinel_user 
          userHelper.updateCloud6SentinelByUidOrMail({uid: userObj?.uid, pass: this.cryptoHelper.encryptDecrypt('encrypt', userPassword) })
        ]
      )
      if (this.configService.get('enableDrupalSync')) {
        await userHelper.syncUserDataWithMySQLDrupal({
          uid: userObj?.uid,
          password: this.cryptoHelper.encryptDecrypt('encrypt', userPassword),
        });
      }
    
      let userTokenDetail;

      if (!ssoRequestFrontend) {
        const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
        userTokenDetail = await authTokenHelper.generateSessionTokens(userObj, clientKey);
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        await cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenDetail.idToken, {
          expires: this.configService.get('maxAge'),
        });
      }

      if (!isB2B) {
        const couchSalt = this.configService.get('couch_salt');
        const hashInput = "a"+String(userObj?.email)+String(couchSalt)+String(userObj?.uid);
        const userKey = this.cryptoHelper.hash(hashInput, 'sha256', 'hex')
        
        const postData = {
          name: userObj.name,
          email: userObj.email || "",
          userKey: userKey
        };
        Logger.log('accountSetupPostOperations', postData);
        // TODO : Have to be uncommented after the syncReferEarnUrl is complete
        // await userHelper.syncReferEarnUrl(res ,postData ,userObj?.uid);
      }
    

      response = { ...response, status: true, msg: 'success' };
      if (isB2B && b2bLmsUrl && typeof b2bLmsUrl === 'string') {
        // Modify response fields for b2bLmsUrl
        response.data = { redirect_url: b2bLmsUrl, redirectTo: 'b2bLmsUrl' };
      } else {
        // Modify response fields for onBoardingUrl
        response.data = { redirect_url: redirect_url, redirectTo: 'onBoardingUrl' };
      }

      // Send data to lrs start
      const lrsData = {
        verb: 'accountsetup',
        objectType: 'accounts',
        objectId: userObj?.uid,
        dataVals: {
          client_id: clientKey,
          redirect_url: redirect_url,
        },
      };
      Logger.log('accountSetupPostOperations', lrsData);
      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(userTokenDetail?.userData, lrsData);
      
      return response;
    } catch (error: any) {
      Logger.error('accountSetupPostOperations', {
        METHOD: this.constructor?.name + '@' + this.accountSetupPostOperations?.name,
        MESSAGE: error.message,
        REQUEST: { accountSetupInfo },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      response.status = false;
      return response;
    }
  }
  async getMultiAccountRedirectionList(@Query() queryParam, name, route, @Res() res, userInfo: CurrentUser) {
    try {
      const userHelper = await this.helperService.getHelper('UserHelper');
      const response = {
        token: '',
        validationErrors: [],
        route: route,
        items: [],
        name: name,
        redirectUrl: this.configService.get('lmsSiteUrl'),
      };

      const gid = queryParam?.gid || '';
      const url = queryParam?.url || '';
      const calendarUrl = queryParam?.calendar_url || '';
      const authHelper = await this.helperService.getHelper('AuthHelper');

      let tokenRedirectUrl = await authHelper.generateRedirectLinkToManageRedirect(
        userInfo,
        'login',
        'email',
        response?.redirectUrl,
        calendarUrl,
      );
      let isB2BB2C = false;
      if (!Utility.isEmpty(gid)) {
        tokenRedirectUrl = `${tokenRedirectUrl}&gid=${gid}&url=${url}`;
        isB2BB2C = await userHelper.ifNotB2COrB2BLearner(gid);
      }
      if (isB2BB2C) {
        return response;
      }

      if (calendarUrl) {
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        await cookieHelper.setCookie(res, this.configService.get('calendarRedirect'), calendarUrl, {
          maxAge: 3600000,
          path: '/',
          domain: this.configService.get('ssoCookieDomain'),
        });
      }
      //get b2c assigned courses
      const { userEnterpriseList, learnerCoursesRecords=[] } = await userHelper.getUserEnterpriseAndCourses({
        uid: userInfo?.uid,
        email: userInfo?.email,
      });
      //if no enterprise list then redirect to b2c lms
      if (
        !Array.isArray(userEnterpriseList) ||
        userEnterpriseList.length === 0 ||
        (learnerCoursesRecords && learnerCoursesRecords.length === 0)
      ) {
        response['redirectUrl'] = tokenRedirectUrl;
        return response;
      }

      //get b2c assigned courses
      const paperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
      const b2cCount = await paperclipService.getAssignedB2cCoursesCount(userInfo?.uid);
      if (b2cCount && b2cCount?.count) {
        const lmsObject = {
          groupName: this.configService.get('groupName'),
          groupId: this.configService.get('defaultGroupId'),
          logoUrl: this.configService.get('simplilearnLogoUrl'),
          lmsHost: this.configService.get('lmsSiteUrl'),
        };
        learnerCoursesRecords.push(lmsObject);
      }
      if (learnerCoursesRecords && learnerCoursesRecords.length === 1) {
        response['redirectUrl'] = learnerCoursesRecords[0]?.lmsHost; // this.configService.get('httpRedirectPrefix') + result[0].lmsHost;
      }
      response['items'] = learnerCoursesRecords;
      return response;
    } catch (error: any) {
      Logger.error('getMultiAccountRedirection', {
        METHOD: this.constructor?.name + '@' + this.getMultiAccountRedirectionList?.name,
        MESSAGE: error.message,
        REQUEST: { queryParam },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }
  async redirectMultiAccount(lmsInfo: { url: string; gid: string }, user: CurrentUser): Promise<string> {
    try {
      const authHelper = await this.helperService.getHelper('AuthHelper');
      const tokenRedirectUrl = await authHelper.generateRedirectLinkToManageRedirect(user, 'login', 'email');
      let redirectUrl = lmsInfo?.url || this.configService.get('lmsSiteUrl');
      if (redirectUrl !== this.configService.get('lmsSiteUrl')) {
        redirectUrl = this.configService.get('httpRedirectPrefix') + redirectUrl;
      } else {
        redirectUrl = tokenRedirectUrl;
      }

      return redirectUrl;
    } catch (error: any) {
      Logger.error('getMultiAccountRedirect', {
        METHOD: `${this.constructor?.name}@${this.redirectMultiAccount?.name}`,
        MESSAGE: error.message,
        REQUEST: { lmsInfo },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async getLoginRedirectUrl(cookieBody, @Res() res, loginInfo, userResponse) {
    const authHelper = await this.helperService.getHelper('AuthHelper');
    const calenderRedirectCookie = cookieBody[this.configService.get('calendarRedirect')] || '';
    const cookieHelper = await this.helperService.getHelper('CookieHelper');
    if (calenderRedirectCookie) {
      return await cookieHelper.getCalendarCookie(loginInfo?.calendar_url.trim() || '', res);
    }
    const loginRedirectUrlResponse = await authHelper.getLoginRedirectUrl(cookieBody, loginInfo, userResponse);
    if (loginRedirectUrlResponse.setCookie) {
      await cookieHelper.setCookie(
        res,
        loginRedirectUrlResponse.cookieValue.name,
        loginRedirectUrlResponse.cookieValue.value,
      );
    }
    return loginRedirectUrlResponse.url;
  }

  async getAccountSetupAndOOBUrl(user: Partial<User>, groupId: number): Promise<string> {
    try {
      if (user?.account_setup === 0) {
        const onBoardingUrl = `${this.configService.get('lmsSiteUrl')}/dashboard/oob/onboarding`;
        let accountSetupUrl = `${this.configService.get(
          'accountSetupUrl',
        )}?setupAccount=true&referer=ForgotPasswordPage&redirect_url=${onBoardingUrl}&userEmail=${
          user?.email
        }&userName=${user?.display_name}&countryId=${user?.country_code}&phoneNo=${user?.phone_no}`;

        if (groupId != undefined && !this.configService.get('b2bB2cGroupId').includes(groupId)) {
          const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
          const enterpriseResponse = await enterpriseService.getGroupDomainByGid({ gid: groupId });

          if (enterpriseResponse?.data !== undefined && enterpriseResponse?.data[0]) {
            accountSetupUrl = `${accountSetupUrl}&isB2b=true&gid=${groupId}&b2bLmsUrl=${enterpriseResponse?.data[0]?.lmsSiteUrl}`;
          }
        }
        return accountSetupUrl;
      }
      return '';
    } catch (error: any) {
      Logger.error('getAccountSetupAndOOBUrl', {
        METHOD: this.constructor?.name + '@' + this.getAccountSetupAndOOBUrl?.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  socialLoginRedirect(target: string): string {
    return `<script>
        if (typeof window.opener == 'object' && window.opener != null && !window.opener.closed) {
          window.opener.location.href = '../${target}';
          window.close();
        } else {
          window.location.href = '../${target}';
        }
      </script>`;
  }
  
  /**
   * Retrieves the latest Terms and Conditions content and associated styles.
   *
   * This method fetches the Terms and Conditions from the Cloud6Service,
   * including any custom styles and the source of the data. If an error occurs
   * during retrieval, it logs the error and returns empty strings for both
   * content and styles, with the source set to 'error'.
   *
   * @returns {Promise<{ termsAndConditions: string; styles: string; source: string }>}
   * An object containing the Terms and Conditions content, styles, and the data source.
   *
   * @throws Logs any errors encountered during the fetch operation.
   */
  async getTermsAndConditions() {
    try {
      const cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const { termsAndConditions = '', styles = '', source = 'api' } = await cloud6Service.getTermsAndConditions() || {};
      return { termsAndConditions, styles, source };
    } catch (error: any) {
      Logger.error('getTermsAndConditions', {
      METHOD: `${this.constructor?.name}@${this.getTermsAndConditions?.name}`,
      MESSAGE: error.message,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
      });
      return { termsAndConditions: '', styles: '', source: 'error' };
    }
  }
}
