import { Test, TestingModule } from '@nestjs/testing';
import { Response } from 'express';
import { AuthService } from '../../auth/services/auth/auth.service';
import { CachingService } from '../../caching/caching.service';
import { ConfigService } from '@nestjs/config';
import { V1Controller } from './v1.controller';
import { HelperService } from '../../helper/helper.service';
import { UserApiService } from '../services/user.api.service';
import { BadRequestException, HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { CheckSignupValidationDto } from '../dto/signup-validation.dto';
import { UpdateUsernameDto } from '../dto/update-username.dto';
import { AuthenticationRequestDto } from '../../auth/dtos/authentication-request.dto';
import { Utility } from './../../common/util/utility';

describe('V1Controller', () => {
  let v1Controller: V1Controller;
  const authServiceMock = {
    forgetPassword: jest.fn(),
    resetPassword: jest.fn().mockResolvedValue(Promise.resolve({})),
    authenticateUser: jest.fn(),
  };

  const cachingServiceMock = {
    get: jest.fn(),
    del: jest.fn(),
  };

  const configServiceMock = {
    get: jest.fn(),
  };
  const helperServiceMock = {
    get: jest.fn(),   
    getHelper: jest.fn(),
  };

  const responseMock = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  } as unknown as Response;
  class CryptoHelperMock {
  }
  const userApiServiceMock = {
    resetPassword: jest.fn(),
    checkSignupValidation: jest.fn(),
    getUserByUid: jest.fn(),
    getUserByEmail: jest.fn(),
    updateUserName: jest.fn(),
    validateUserDetails: jest.fn(),
    authenticate: jest.fn(),
    getLinkAccounts: jest.fn(),
    removeLinkedAccounts: jest.fn(),
    getUserRoles: jest.fn(),
    assignUserRole: jest.fn(),
    revokeUserRole: jest.fn(),
    getOriginalToken: jest.fn(),
  };
  const CryptoProviders = [
    {
      provide: 'CRYPTO_HELPER',
      useClass: CryptoHelperMock, // Use the mock class here
    },
    {
      provide: 'TRIBE_CRYPTO_HELPER',
      useClass: CryptoHelperMock, // Use the mock class here
    },
  ];
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [V1Controller],
      providers: [
        {
          provide: AuthService,
          useValue: authServiceMock,
        },
        {
          provide: CachingService,
          useValue: cachingServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: HelperService, // Provide the real HelperService here if needed
          useValue: helperServiceMock, // Provide the mock object
        },
        {
          provide: UserApiService,
          useValue: userApiServiceMock,
        },
        ...CryptoProviders,
      ],
    }).compile();

    v1Controller = module.get<V1Controller>(V1Controller);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  describe('forgot password', () => {
   
    it('should send forgot password link successfully', async () => {
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };
    
      // Expected parameters passed to forgetPassword method
      const param = {
        fm: 1,
        appType: 'app',
        client_id: 'sl_looper',
      };
    
      // Mock client secret
      configServiceMock.get.mockReturnValue('mock-client-secret');
    
      // Spy on utility method
      const validateClientSpy = jest.spyOn(Utility, 'validateClientRequest').mockImplementation();
    
      // Mock forgetPassword method to return success
      const mockAuthServiceInstance = {
        forgetPassword: jest.fn().mockResolvedValue(true),
      };
    
        // Mock helperService.get to return our mock auth service
        helperServiceMock.get.mockResolvedValue(mockAuthServiceInstance);
    
      await v1Controller.forgotPassword(forgetPasswordDto, responseMock);
    
      // ✅ Assertions
      expect(validateClientSpy).toHaveBeenCalledWith('sl_looper', 'mock-client-secret');
      expect(mockAuthServiceInstance.forgetPassword).toHaveBeenCalledWith('<EMAIL>', param);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({
        type: 'success',
        msg: 'Reset password link sent on your email.',
      });
    });
    
    it('should handle UserNotFoundException', async () => {
      // Arrange
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };
      
      const errorResponse = new Error('UserNotFoundException'); // Simulate a UserNotFoundException error
      const mockAuthService = {
        forgetPassword: jest.fn().mockResolvedValue(errorResponse),
      };
    
      // Mock helperService.get to return the mocked AuthService
      helperServiceMock.get.mockResolvedValue(mockAuthService);
    
      // Mock Utility.validateClientRequest to avoid exception
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);

      // Act and Assert
      await expect(v1Controller.forgotPassword(forgetPasswordDto, responseMock)).rejects.toThrowError(
        new BadRequestException('No active account associated with this email address.'),
      );
      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });
    it('should handle UserDisabled', async () => {
      // Arrange
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly:1,
      };
      const errorResponse = new Error('UserDisabled'); // Simulate a UserDisabled error
      const mockAuthService = {
        forgetPassword: jest.fn().mockResolvedValue(errorResponse), // Simulate user disabled error
      };
    
      // Mock helperService.get to return the mocked AuthService
      helperServiceMock.get.mockResolvedValue(mockAuthService);
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);

      // Act and Assert
      await expect(v1Controller.forgotPassword(forgetPasswordDto, responseMock)).rejects.toThrowError(
        new BadRequestException('Try using a different email address to create an account.'),
      );
      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });

    it('should handle other errors', async () => {
      // Arrange
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
      linkOnly : 1,
      };
      const errorResponse = new Error('OtherError'); // Simulate an error other than UserNotFoundException or UserDisabled

      // Mock the behavior of getHelper to return a mock utility function
      // helperServiceMock.getHelper.mockResolvedValue({
      //   validateClientRequest: jest.fn(),
      //   forgetPassword: jest.fn().mockRejectedValue(errorResponse), // Mock rejection with the error
      // });
      const mockAuthService = {
        forgetPassword: jest.fn().mockRejectedValue(errorResponse),
      };
    
      // Mock helperService.get to return the mocked AuthService
      helperServiceMock.get.mockResolvedValue(mockAuthService);
      // Act and Assert
      await expect(v1Controller.forgotPassword(forgetPasswordDto, responseMock)).rejects.toThrowError(

        new InternalServerErrorException()
      );
      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });
  });

  describe('reset password', () => {
    // Success case
    const resetPassword = {
      uid: 123456,
      pass: 'newPassword123',
    };

  
    const cachingServiceMock = {
      get: jest.fn().mockResolvedValue('someToken'),
      del: jest.fn(),
    };
  
    const authServiceMock = {
      resetPasswrd: jest.fn()
     
    };
    
    it('should handle successful password reset', async () => {
      // Test when password reset is successful
      const successResponse = { success: true, msg: 'Password reset successful', data: {} };
      authServiceMock.resetPasswrd.mockResolvedValue(successResponse);

      const result = await v1Controller.resetPassword({ uid: '123456', pass: 'hello' });

      expect(result).toEqual({
        type: 'success',
        msg: 'Password reset successful',
        data: {},
      });

      expect(authServiceMock.resetPasswrd).toHaveBeenCalledWith(resetPassword);
      expect(cachingServiceMock.del).toHaveBeenCalledWith('123456');
    });
    it('should return error when uid or pass is missing', async () => {
      const missingUid = { pass: 'newPassword123' };
      const missingPass = { uid: '123456' };
    
      const result1 = await v1Controller.resetPassword(missingUid as { uid: string; pass: string });
      expect(result1).toEqual({
        type: 'error',
        msg: 'Parameter Not Found',
        data: '',
      });
    
      const result2 = await v1Controller.resetPassword(missingPass as { uid: string; pass: string });
      expect(result2).toEqual({
        type: 'error',
        msg: 'Parameter Not Found',
        data: '',
      });
    });
    it('should return error when resetPasswrd fails', async () => {
      authServiceMock.resetPasswrd.mockResolvedValue({
        success: false,
        msg: 'User not found',
      });
      await expect(
        v1Controller.resetPassword({ uid: '123', pass: 'pass123' })
      ).rejects.toThrow(InternalServerErrorException);
    
      expect(cachingServiceMock.del).not.toHaveBeenCalled();
    });
  
   
  });
  describe('checkSignupValidationAction', () => {
    it('should return success message when signup data is valid', async () => {
      // Arrange
      const signupData: CheckSignupValidationDto = {
        user_email: '<EMAIL>',
        user_name: 'Uday Singh',
        user_pwd: 'Simpli@123',
        client_id: 'sl_looper',
        device_type: 'ios',
        from_mobile: '1',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };
      userApiServiceMock.checkSignupValidation.mockResolvedValue({
        msg: 'Email available for account creation.',
        type: 'success',
      }); // Mock a successful response

      // Act
      const result = await v1Controller.checkSignupValidationAction(signupData);

      // Assert
      expect(result).toEqual({
        type: 'success',
        msg: 'Email available for account creation.',
      });
    });

    it('should return an error message when signup data is invalid', async () => {
      // Arrange
      const signupData: CheckSignupValidationDto = {
        user_email: '',
        user_name: '',
        user_pwd: '',
        client_id: 'sl_looper1',
        device_type: 'ios',
        from_mobile: '1',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };
      userApiServiceMock.checkSignupValidation.mockRejectedValue({
        msg: 'Error occurred while creating the user.',
        type: 'error',
      }); // Mock a failure response

      // Act
      const result = await v1Controller.checkSignupValidationAction(signupData);

      // Assert
      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while creating the user.',
      });
    });

    it('should handle errors and return an error message', async () => {
      // Arrange
      const signupData: CheckSignupValidationDto = {
        user_email: '<EMAIL>',
        user_name: 'Uday Singh',
        user_pwd: 'Simpli@123',
        client_id: 'sl_looper',
        device_type: 'ios',
        from_mobile: '1',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };
      const errorResponse = new Error('An error occurred'); // Simulate an error
      userApiServiceMock.checkSignupValidation.mockRejectedValue(errorResponse);

      // Act
      const result = await v1Controller.checkSignupValidationAction(signupData);

      // Assert
      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while creating the user.',
      });
    });
  });
  describe('getUserByUID', () => {
    it('should return user info when the UID is valid', async () => {
      // Arrange
      const uid = '1002312';
      const mockUserInfo = {
        // Define your mock user info data here
      };
      userApiServiceMock.getUserByUid.mockResolvedValue(mockUserInfo);

      // Act
      const result = await v1Controller.getUserByUID(uid);

      // Assert
      expect(result).toEqual(mockUserInfo);
    });

    it('should handle errors and return an empty array', async () => {
      // Arrange
      const uid = '1002312';
      const errorResponse = new Error('An error occurred'); // Simulate an error
      userApiServiceMock.getUserByUid.mockRejectedValue(errorResponse);

      // Act
      const result = await v1Controller.getUserByUID(uid);

      // Assert
      expect(result).toEqual([]);
    });
  });
  describe('getUserByEmail', () => {
    it('should return user info when the email is valid', async () => {
      // Arrange
      const email = '<EMAIL>';
      const mockUserInfo = {
        email,
      };
      userApiServiceMock.getUserByEmail.mockResolvedValue(mockUserInfo);

      // Act
      const result = await v1Controller.getUserByEmail(email);

      // Assert
      expect(result).toEqual(mockUserInfo);
    });

    it('should handle errors and return an empty array', async () => {
      // Arrange
      const email = '<EMAIL>';
      const errorResponse = new Error('An error occurred'); // Simulate an error
      userApiServiceMock.getUserByEmail.mockRejectedValue(errorResponse);

      // Act
      const result = await v1Controller.getUserByEmail(email);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('updateUsername', () => {
    it('should update the username successfully', async () => {
      const mockUpdatedUser = {
        type: 'success',
      };

      userApiServiceMock.updateUserName.mockResolvedValue(mockUpdatedUser);

      // Act
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      // Assert
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
    });

    it('should handle errors and throw an InternalServerErrorException', async () => {
      // Arrange
      const requestBody: UpdateUsernameDto = {
        uid: '',
        name: '',
      };
      const errorResponse = new Error('An error occurred'); // Simulate an error

      userApiServiceMock.updateUserName.mockRejectedValue(errorResponse);

      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      await expect(v1Controller.updateUsername(requestBody, responseMock)).rejects.toThrowError(
        new InternalServerErrorException('An error occurred'),
      );

      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });
  });
  describe('validateUserDetails', () => {
    it('should handle UserNotFoundException and return success message', async () => {
      // Arrange
      const requestBody = {
        client_id: 'your_client_id',
        method: 'your_method',
        email: '<EMAIL>',
        authToken: 'your_auth_token',
      };
      const error = new Error('UserNotFoundException');
      userApiServiceMock.validateUserDetails.mockRejectedValue(error);

      // Act
      // Assert
      expect(userApiServiceMock.validateUserDetails).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({ type: 'success', msg: 'User does not exist' });
    });

    it('should handle other errors and return an error response', async () => {
      // Arrange
      const requestBody = {
        client_id: 'your_client_id',
        method: 'your_method',
        email: '<EMAIL>',
        authToken: 'your_auth_token',
      };
      const error = new Error('SomeError');
      userApiServiceMock.validateUserDetails.mockRejectedValue(error);

      // Act
  
      // Assert
      expect(userApiServiceMock.validateUserDetails).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).not.toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).not.toHaveBeenCalled();
    });
  });

  describe('authenticate', () => {
    it('should authenticate user successfully', async () => {
      // Arrange
      const authenticationRequest: AuthenticationRequestDto = {
        user_login: '<EMAIL>',
        user_pwd: 'Simpli@12345',
        client_id: 'sl_looper',
        redirect_url: '',
        from_mobile: '0',
        device_type: '',
        app_type:'',
      };

      const expectedResult = { type: 'success' /* other properties */ };
      userApiServiceMock.authenticate.mockResolvedValue(expectedResult);

      // Act
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockResolvedValue({
          type: 'success',
        }),
      } as any; // Mocking Express Response object
      await v1Controller.authenticate(authenticationRequest, responseMock);

      // Assert
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(expectedResult);
    });

      it('should handle authentication error and throw BadRequestException', async () => {
        // Arrange
        const authenticationRequest: AuthenticationRequestDto = {
          user_login: '<EMAIL>',
          user_pwd: 'Simpli@12345',
          client_id: 'sl_looper',
          redirect_url: '',
          from_mobile: '0',
          device_type: '',
          app_type:''
        };
      const errorResponse = new Error('AuthenticationError');
      userApiServiceMock.authenticate.mockRejectedValue(errorResponse);
      configServiceMock.get = jest.fn().mockReturnValue('Some error occurred while authenticating user.');
      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any; // Mocking Express Response object
      await expect(v1Controller.authenticate(authenticationRequest, responseMock)).rejects.toThrowError(
        new BadRequestException('Some error occurred while authenticating user.'),
      );

      expect(responseMock.status).not.toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).not.toHaveBeenCalled();
    });
  });

  describe('get link accounts', () => {
    it('should fetch linked accounts successfully', async () => {
      // Arrange
      const reqBody = {
        email: '<EMAIL>',
        userId: '1004173',
        from_mobile: '1',
        client_id: 'sl_looper',
        method: 'google',
        redirect_url: '',
      };
      const expectedResult = { type: 'success' /* Provide expected result here */ };
      userApiServiceMock.getLinkAccounts.mockResolvedValue(expectedResult);

      // Act
      const response = await v1Controller.getLinkedAccounts(reqBody, {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockResolvedValue({ status: 200 }),
      });

      // Assert
      expect(response.status).toBe(HttpStatus.OK);
      expect(userApiServiceMock.getLinkAccounts).toHaveBeenCalledWith(reqBody);
      // Add more assertions based on your expected result structure
    });

    it('should handle errors and throw the error', async () => {
      // Arrange
      const reqBody = {
        email: '<EMAIL>',
        userId: '1004173',
        from_mobile: '1',
        client_id: 'sl_looper',
        method: 'google',
        redirect_url: '',
      };
      const errorResponse = new Error('SomeError');
      userApiServiceMock.getLinkAccounts.mockRejectedValue(errorResponse);

      // Act and Assert
      await expect(
        v1Controller.getLinkedAccounts(reqBody, {
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
        }),
      ).rejects.toThrowError(errorResponse);
      expect(userApiServiceMock.getLinkAccounts).toHaveBeenCalledWith(reqBody);
    });
  });

  describe('removeLinkedAccounts', () => {
    it('should remove linked accounts successfully', async () => {
      // Arrange
      const requestBody = {
        client_id: 'sl_looper',
        userId: '1004173',
        email: '<EMAIL>',
        redirect_url: 'https://example.com',
        method: 'google',
      };

      const expectedResult = { type: 'success', msg: 'Linked accounts removed successfully' };

      userApiServiceMock.removeLinkedAccounts.mockResolvedValue(expectedResult);

      // Act
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
   
      // Assert
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({ status: HttpStatus.OK, ...expectedResult });
      expect(userApiServiceMock.removeLinkedAccounts).toHaveBeenCalledWith(requestBody);
    });

    it('should handle errors and throw the error', async () => {
      // Arrange
      const requestBody = {
        client_id: 'sl_looper',
        userId: '1004173',
        email: '<EMAIL>',
        redirect_url: 'https://example.com',
        method: 'google',
      };

      const errorMessage = 'An error occurred';
      userApiServiceMock.removeLinkedAccounts.mockRejectedValue(new Error(errorMessage));

      // Act and Assert
      await expect(async () => await v1Controller.removeLinkedAccounts(requestBody, {} as any)).rejects.toThrowError(
        new Error(errorMessage),
      );

      expect(userApiServiceMock.removeLinkedAccounts).toHaveBeenCalledWith(requestBody);
    });
  });

  describe('get user roles', () => {
    it('should fetch user roles successfully', async () => {
      // Arrange
      const reqBody = {
        pageSize: 10,
        page: 1,
      };
      const expectedResult = { type: 'success' /* Provide expected result here */ };
      userApiServiceMock.getUserRoles.mockResolvedValue(expectedResult);

      // Act
      const response = await v1Controller.getRoles(reqBody, {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockResolvedValue({ status: 200 }),
      });

      // Assert
      expect(response.status).toBe(HttpStatus.OK);
      expect(userApiServiceMock.getUserRoles).toHaveBeenCalledWith(reqBody);
      // Add more assertions based on your expected result structure
    });
    it('should handle errors and log the error', async () => {
      // Arrange
      const reqQuery = {
        pageSize: 10,
        page: 1,
      };
      const errorMessage = 'some error';
      userApiServiceMock.getUserRoles.mockRejectedValue(new Error(errorMessage));

      // Act
      const response = await v1Controller.getRoles(reqQuery, {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      });

      // Assert
      expect(response).toEqual([]);
      expect(userApiServiceMock.getUserRoles).toHaveBeenCalledWith(reqQuery);

      // Verify APILog.error is called with the correct parameters
    });
  });

  describe('assign user role', () => {
    // Success case
    it('should assign user role successfully', async () => {
      // Arrange
      const requestBody = {
        uid: '123',
        rid: '23',
      };

      const expectedResult = { type: 'success', msg: 'User role assigned successfully' };

      userApiServiceMock.assignUserRole.mockResolvedValue(expectedResult);

      // Act
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      // Assert
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(expectedResult);
      expect(userApiServiceMock.assignUserRole).toHaveBeenCalledWith(requestBody);
    });

    // Error case
    it('should handle error while assigning user role', async () => {
      // Arrange
      const requestBody = {
        uid: '123456',
        rid: '3454',
      };

      const errorMessage = 'An unexpected error occurred';

      userApiServiceMock.assignUserRole.mockRejectedValue(new Error(errorMessage));

      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      await v1Controller.assignUserRole(requestBody, responseMock);

      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({});
      expect(userApiServiceMock.assignUserRole).toHaveBeenCalledWith(requestBody);
    });
  });

  describe('revoke user role', () => {
    // Success case
    it('should revoke user role successfully', async () => {
      // Arrange
      const requestBody = {
        uid: '123456',
        rid: '455654',
      };

      const expectedResult = { type: 'success', msg: 'User role revoked successfully' };

      userApiServiceMock.revokeUserRole.mockResolvedValue(expectedResult);

      // Act
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
   
      // Assert
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(expectedResult);
      expect(userApiServiceMock.revokeUserRole).toHaveBeenCalledWith(requestBody);
    });

    // Error case
    it('should handle error while revoking user role', async () => {
      // Arrange
      const requestBody = {
        uid: '123456',
        rid: '5345',
      };

      const errorMessage = 'An unexpected error occurred';

      userApiServiceMock.revokeUserRole.mockRejectedValue(new Error(errorMessage));

      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      await v1Controller.revokeUserRole(requestBody, responseMock);

      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({});
      expect(userApiServiceMock.revokeUserRole).toHaveBeenCalledWith(requestBody);
    });
  });

  describe('get original token', () => {
    const mockResponse = () => {
      const res: any = {};
      res.status = jest.fn().mockReturnValue(res);
      res.json = jest.fn().mockReturnValue(res);
      return res;
    };
    it('should return original token on success', async () => {
      const reqBody = {
        redirect_url: 'https://example.com',
        token: 'validToken',
      };
      const mockResult = {
        type: 'success',
        msg: 'Original Token generated',
        _t: 'encodedToken',
      };
  
      const res = mockResponse();
  
      userApiServiceMock.getOriginalToken.mockResolvedValue(mockResult);
  
      await v1Controller.getOriginalToken(reqBody, res);
  
      expect(userApiServiceMock.getOriginalToken).toHaveBeenCalledWith(reqBody);
      expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(res.json).toHaveBeenCalledWith(mockResult);
    });
    it('should catch exceptions and log the error', async () => {

    const mockLogger = {
      error: jest.fn(),
    };
      const reqBody = {
        redirect_url: 'https://example.com',
        token: 'token123',
      };
  
      const res = mockResponse();
  
      const error = new Error('Unexpected failure');
      userApiServiceMock.getOriginalToken.mockRejectedValue(error);
  
      const result = await v1Controller.getOriginalToken(reqBody, res);
  
      expect(userApiServiceMock.getOriginalToken).toHaveBeenCalledWith(reqBody);
      expect(mockLogger.error).toHaveBeenCalledWith('get-original-token', {
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected failure',
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: expect.any(Number),
      });
  
      expect(result).toEqual([]);
    });
  })
});
