import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  InternalServerErrorException,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UseFilters,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiProperty, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { APILog, Logger } from '../../logging/logger';
import { UserApiService } from '../services/user.api.service';
import { HttpExceptionFilter } from '../../common/filters/http.exception.filter';
import { ForgetPasswordDto } from '../dto/forget-password-dto';
import { CachingService } from '../../caching/caching.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { UseAPIGuard } from '../../auth/guards/auth.guards';
import { AuthenticationRequestDto } from '../../auth/dtos/authentication-request.dto';
import { UpdateUsernameDto } from '../dto/update-username.dto';
import { UpdateUserTimezoneDto } from '../dto/update-user-tmezone.dto';
import { RegisterDto } from '../dto/register.dto';
import { UserApiLoginDto } from '../dto/v1-login.dto';
import { CheckSignupValidationDto } from '../dto/signup-validation.dto';
import { SignupDto } from '../dto/signup.dto';
import { LinkAccountsDto } from '../dto/link.accounts.dto';
import { Utility } from './../../common/util/utility';
import { SaveProfileDto, UpdateProfileDto } from '../dto/update-profile.dto';
import { AuthenticateSaml } from '../dto/authenticate-saml.dto';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { RoleDto } from '../dto/role.dto';

@ApiTags('User API V1')
@Controller('user-api/v1')
/**
 * @summary V1Controller
 * @description This controller handles various user-related operations such as signup, login, registration, password management, account linking, and user profile updates.
 *
 * @remarks
 * The controller uses dependency injection to access services like `UserApiService`, `CachingService`, `ConfigService`, and `HelperService`.
 * It includes endpoints for user authentication, role management, and other user-related functionalities.
 *
 * @swagger
 * Each endpoint is decorated with Swagger decorators to provide API documentation, including request and response schemas, status codes, and descriptions.
 *
 * @class V1Controller
 * @decorator @Controller('/user-api/v1')
 * @decorator @UseFilters(HttpExceptionFilter)
 * @decorator @UsePipes(new ValidationPipe())
 * @decorator @UseAPIGuard()
 */
export class V1Controller {
  @Inject() private readonly userApiService: UserApiService;
  @Inject() private readonly cachingService: CachingService;
  @Inject() private readonly configService: ConfigService;
  @Inject() private readonly helperService: HelperService;

  /**
   * @summary User Signup
   * @description This endpoint allows users to sign up by providing the necessary details.
   *
   * @param {SignupDto} data - The signup data provided by the user.
   * @param {object} res - The Express response object.
   * @param {object} req - The Express request object.
   * @returns {object} The signup response.
   *
   * @throws {BadRequestException} Throws a BadRequestException if signup fails.
   *
   * Steps:
   * 1. Process the signup data.
   * 2. Log the response.
   * 3. Return the response or handle errors.
   */
  @ApiProperty()
  @ApiOperation({ summary: 'User Signup' })
  @ApiResponse({ status: 200, description: 'Signup successful' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({ type: SignupDto, description: 'Signup data' })
  @HttpCode(HttpStatus.OK)
  @Post('/signup')
  @UseAPIGuard()
  @UseFilters(new HttpExceptionFilter())
  async signup(@Body() data: SignupDto, @Res() res, @Req() req) {
    try {
      const response = await this.userApiService.signup(data, req.cookies);
      Logger.log('Return response', {
        RESPONSE: response,
      });
      return res.status(HttpStatus.OK).json(response);
    } catch (error: any) {
      APILog.error('signup?', {
        METHOD: this.constructor.name + '@' + this.signup?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.status(HttpStatus.OK).json({ type: error, msg: 'Something went wrong, please try again.' });
    }
  }

  /**
   * @summary User Registration
   * @description This endpoint allows users to register by providing the necessary details.
   *
   * @param {RegisterDto} data - The registration data provided by the user.
   * @param {object} req - The Express request object.
   * @param {object} res - The Express response object.
   * @returns {object} The registration response.
   *
   * @throws {BadRequestException} Throws a BadRequestException if registration fails.
   *
   * Steps:
   * 1. Process the registration data.
   * 2. Log the response.
   * 3. Return the response or handle errors.
   * 
   * This api is consumed by EngageX
   */
  @ApiProperty()
  @ApiOperation({ summary: 'User Registration' })
  @ApiResponse({ status: 200, description: 'Registration successful' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({ type: RegisterDto, description: 'Registration data' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UseFilters(new HttpExceptionFilter())
  @Post('/register')
  async register(@Req() req, @Res() res, @Body(new ValidationPipe()) data: RegisterDto) {
    try {
      let response = { type: 'success', msg: 'Error occurred while creating user.' }
      let responseType : 'json' | 'redirect' = Utility.isEmpty(data?.redirect_url)  ? 'json' : 'redirect';
      const registerResponse = await this.userApiService.register(data, req?.cookies, res);
      if (registerResponse?.status && registerResponse?.data?.idToken) {
        response['type'] = 'success'
        response['msg'] = registerResponse?.msg
        response['_t'] = registerResponse?.data?.idToken
      }
      return Utility.returnResponse( response, responseType, data.redirect_url, res);
    } catch (error: any) {
      APILog.error('register?', {
        METHOD: this.constructor.name + '@' + this.register?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.status(HttpStatus.OK).json({ type: error, msg: 'Something went wrong, please try again.' });
    }
  }

  /**
   * @summary User Login
   * @description This endpoint allows users to log in by providing their credentials.
   *
   * @param {UserApiLoginDto} data - The login data provided by the user.
   * @param {object} res - The Express response object.
   * @returns {object} The login response.
   *
   * @throws {BadRequestException} Throws a BadRequestException if login fails.
   *
   * Steps:
   * 1. Validate the login data.
   * 2. Process the login request.
   * 3. Return the response or handle errors.
   * 
   * This api is consumed by Paperclip.
   */
  @ApiProperty()
  @ApiOperation({ summary: 'User Login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({ type: UserApiLoginDto, description: 'Login data' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UseFilters(new HttpExceptionFilter())
  @Post('/login')
  async login(@Body(new ValidationPipe()) data: UserApiLoginDto, @Res() res) {
    try {
      let responseType : 'json' | 'redirect' = Utility.isEmpty(data?.redirect_url)  ? 'json' : 'redirect';
      let response: any = { type: 'error', msg: 'Something went wrong, please try again.' };
      response = await this.userApiService.login(data, res);
      return Utility.returnResponse( response, responseType, data?.redirect_url, res);
    } catch (error: any) {
      APILog.error('login?', {
        METHOD: this.constructor.name + '@' + this.login?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.json({ type: error, msg: 'Something went wrong, please try again.' });
    }
  }

  /**
   * @summary Forgot Password
   * @description This endpoint allows users to request a password reset link by providing their email.
   *
   * @param {ForgetPasswordDto} forgetPasswordDto - The data required to process the forgot password request.
   * @param {Response} res - The Express response object.
   * @returns {object} A success message if the reset link is sent successfully.
   *
   * @throws {BadRequestException} Throws a BadRequestException if the user is not found, disabled, or if an error occurs.
   *
   * Steps:
   * 1. Validate the client request using the client ID and secret.
   * 2. Determine additional parameters based on the request source.
   * 3. Call the AuthService to process the forgot password request.
   * 4. Handle errors such as user not found or user disabled.
   * 5. Return a success response if the reset link is sent successfully.
   * 
   * This api is consumed by Paperclip.
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Forgot password to send the reset password link' })
  @ApiResponse({ status: 200, description: 'Reset password link sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({ type: ForgetPasswordDto, description: 'Forgot password request data' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @Post('/forgot-password')
  async forgotPassword(@Body() forgetPasswordDto: ForgetPasswordDto) {
    try {
      // Validate the client request
      Utility.validateClientRequest(forgetPasswordDto?.client_id, this.configService.get('clientSecret'));

      // Determine additional parameters based on the request source
      const param = {
        ...(forgetPasswordDto?.from_mobile === '1' && { fm: 1, appType: forgetPasswordDto?.device_type }),
        client_id: forgetPasswordDto?.client_id,
        linkOnly: forgetPasswordDto?.linkOnly ? Number(forgetPasswordDto.linkOnly) : 0,
      };
      // Call the AuthService to process the forgot password request
      const userApiService = await this.helperService.get<UserApiService>(UserApiService);
      const response = await userApiService.forgetPassword(forgetPasswordDto?.user_email, param);

      // Handle specific error cases
      if (response instanceof Error) {
        if (response?.message === 'UserNotFoundException') {
          throw new BadRequestException('No active account associated with this email address.');
        } else if (response?.message === 'UserDisabled') {
          throw new BadRequestException('Try using a different email address to create an account.');
        } else {
          throw new BadRequestException('Error occurred while requesting for password reset.');
        }
      }
      if (typeof response === 'object' && response !== null && 'link' in response) {
        // Handle the linkOnly flow (if AuthService returned a link)
        return { type: 'success', link: response.link };
      } 

      // Return a success response if the reset link is sent successfully
     else if (response) {
        return { type: 'success', msg: 'Reset password link sent on your email.' };
      }
    } catch (error: any) {
      // Log the error and rethrow it
      APILog.error('forgotPassword', {
        METHOD: this.constructor.name + '@' + this.forgotPassword.name,
        MESSAGE: error.message,
        REQUEST: forgetPasswordDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  /**
   * @summary Reset Password
   * @description This endpoint allows users to reset their password using a unique token.
   *
   * @param {ResetPassword} resetPassword - The reset password data provided by the user.
   * @param {Response} res - The Express response object.
   * @returns {object} A success message if the password is reset successfully.
   *
   * @throws {BadRequestException} Throws a BadRequestException if the token is expired or invalid.
   * @throws {InternalServerErrorException} Throws an InternalServerErrorException for other errors.
   *
   * Steps:
   * 1. Retrieve the token from the caching service using the user ID.
   * 2. Validate the token and reset the password using the AuthService.
   * 3. Delete the token from the caching service after successful reset.
   * 4. Return a success response or handle errors.
   * 
   * This api is consumed by Paperclip.
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Reset Password' })
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @Post('/reset-password')
  async resetPassword(@Body() resetPassword: { uid: string; pass: string }) {
    try {
      if (!resetPassword?.uid || !resetPassword?.pass) {
        return ({
          type: 'error',
          msg: 'Parameter Not Found',
        });
      }
      // Reset the password using the AuthService
      const userApiService = await this.helperService.get<UserApiService>(UserApiService);
      const { success, msg } = await userApiService.resetPassword(resetPassword);
      if (!success) {
        return { type: 'error', msg };
      }
      else {
        // Delete the token from the caching service
        this.cachingService.del(resetPassword?.uid.toString());

        // Return a success response
        return { type: 'success', msg };
      }
    } catch (error: any) {
      // Log the error and rethrow it
      APILog.error('resetPassword', {
        METHOD: this.constructor.name + '@' + this.resetPassword.name,
        MESSAGE: error.message,
        REQUEST: resetPassword,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  /**
   * @summary Validate User Details
   * @description This endpoint validates the user details based on the provided client ID, method, email, and auth token.
   *
   * @param {object} requestBody - The request body containing user details.
   * @param {string} requestBody.client_id - The client ID.
   * @param {string} requestBody.method - The method of validation.
   * @param {string} requestBody.email - The email of the user.
   * @param {string} requestBody.authToken - The authentication token.
   * @param {Response} res - The Express response object.
   * @returns {object} The validation response.
   *
   * @throws {Error} Throws an error if validation fails.
   * This api is consumed by Paperclip.
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Validate User Details' })
  @ApiResponse({ status: 200, description: 'Validation successful' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @Post('/validate-user-details')
  @UseAPIGuard()
  @UseFilters(HttpExceptionFilter)
  async validateUserDetails(
    @Body() requestBody: { client_id: string; method: string; email: string; authToken: string },
    @Res() res,
  ) {
    try {
      const response = await this.userApiService.validateUserDetails(requestBody);
      return res.status(HttpStatus.OK).json({ ...response });
    } catch (error: any) {
      APILog.error('validateUserDetails', {
        METHOD: this.constructor.name + '@' + this.validateUserDetails.name,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error.message === 'UserNotFoundException') {
        return res.status(HttpStatus.OK).json({ type: 'success', msg: 'User does not exist' });
      }
    }
  }

  /**
   * @summary Link Accounts
   * @description This endpoint allows users to link their accounts by providing the necessary details.
   *
   * @param {LinkAccountsDto} requestBody - The data required to link accounts.
   * @param {Response} res - The Express response object.
   * @returns {object} The response indicating the success or failure of the account linking process.
   *
   * @throws {Error} Throws an error if the account linking process fails.
   *
   * Steps:
   * 1. Process the account linking request.
   * 2. Return a success or error response based on the result.
   * 
   * This api is consumed by Paperclip
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Link Accounts' })
  @ApiResponse({ status: 200, description: 'Accounts linked successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @UseAPIGuard()
  @UseFilters(HttpExceptionFilter)
  @Post('/link-accounts')
  async linkAccounts(@Body(new ValidationPipe()) requestBody: LinkAccountsDto, @Res() res) {
    try {
      const response = await this.userApiService.linkAccounts(requestBody);
      if (response?.type === 'success') {
        return res.status(HttpStatus.OK).json({ ...response });
      }
      return res.status(HttpStatus.BAD_REQUEST).json({ ...response });
    } catch (error: any) {
      APILog.error('link-accounts', {
        METHOD: this.constructor.name + '@' + this.linkAccounts.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  /**
   * @summary Get Linked Accounts
   * @description This endpoint retrieves the linked accounts for a user based on the provided details.
   *
   * @param {object} requestBody - The request body containing the necessary details.
   * @param {string} requestBody.client_id - The client ID.
   * @param {string} requestBody.userId - The user ID.
   * @param {string} requestBody.email - The email of the user.
   * @param {string} requestBody.redirect_url - The redirect URL.
   * @param {string} requestBody.from_mobile - Indicates if the request is from a mobile device.
   * @param {Response} res - The Express response object.
   * @returns {object} The response containing the linked accounts.
   *
   * @throws {Error} Throws an error if the retrieval process fails.
   * 
   * This api is consumed by Paperclip
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Get Linked Accounts' })
  @ApiResponse({ status: 200, description: 'Fetch linked accounts successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({
    description: 'Details required to fetch linked accounts',
    schema: {
      type: 'object',
      properties: {
        client_id: { type: 'string', description: 'The client ID' },
        userId: { type: 'string', description: 'The user ID' },
        email: { type: 'string', description: 'The email of the user' },
        redirect_url: { type: 'string', description: 'The redirect URL' },
        from_mobile: { type: 'string', description: 'Indicates if the request is from a mobile device' },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @Post('/get-linked-accounts')
  async getLinkedAccounts(
    @Body()
    requestBody: {
      client_id: string;
      userId: string;
      email: string;
      redirect_url: string;
      from_mobile: string;
    },
    @Res() res,
  ) {
    try {
      const response = await this.userApiService.getLinkAccounts(requestBody);
      return res.status(HttpStatus.OK).json({ status: HttpStatus.OK, ...response });
    } catch (error: any) {
      APILog.error('get-linked-accounts', {
        METHOD: this.constructor.name + '@' + this.getLinkedAccounts.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * @summary Remove Linked Accounts
   * @description This endpoint allows users to remove linked accounts by providing the necessary details.
   *
   * @param {object} requestBody - The request body containing the details to remove linked accounts.
   * @param {string} requestBody.client_id - The client ID.
   * @param {string} requestBody.userId - The user ID.
   * @param {string} requestBody.email - The email of the user.
   * @param {string} requestBody.redirect_url - The redirect URL.
   * @param {string} requestBody.method - The method of removal.
   * @param {Response} res - The Express response object.
   * @returns {object} The response indicating the success or failure of the account removal process.
   *
   * @throws {Error} Throws an error if the account removal process fails.
   *
   * Steps:
   * 1. Process the account removal request.
   * 2. Return a success or error response based on the result.
   * 
   * This api is consumed by paperclip
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Remove Linked Accounts' })
  @ApiResponse({ status: 200, description: 'Linked accounts removed successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({
    description: 'Details required to remove linked accounts',
    schema: {
      type: 'object',
      properties: {
        client_id: { type: 'string', description: 'The client ID' },
        userId: { type: 'string', description: 'The user ID' },
        email: { type: 'string', description: 'The email of the user' },
        redirect_url: { type: 'string', description: 'The redirect URL' },
        method: { type: 'string', description: 'The method of removal' },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @Post('/remove-linked-accounts')
  async removeLinkedAccounts(
    @Body() requestBody: { client_id: string; userId: string; email: string; redirect_url: string; method: string },
    @Res() res,
  ) {
    try {
      const response = await this.userApiService.removeLinkedAccounts(requestBody);
      return res.status(HttpStatus.OK).json({ status: HttpStatus.OK, ...response });
    } catch (error: any) {
      APILog.error('remove-linked-accounts', {
        METHOD: this.constructor.name + '@' + this.removeLinkedAccounts.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * @summary Update LinkedIn Accounts
   * @description This endpoint updates the LinkedIn account status for a user based on the provided details.
   *
   * @param {object} requestBody - The request body containing the details for updating LinkedIn accounts.
   * @param {string} requestBody.redirect_url - The redirect URL.
   * @param {string} requestBody.user_id - The user ID.
   * @param {string} requestBody.source - The source of the update request.
   * @param {Response} res - The Express response object.
   * @returns {object} The response indicating the success or failure of the LinkedIn account update process.
   *
   * @throws {Error} Throws an error if the LinkedIn account update process fails.
   *
   * Steps:
   * 1. Validate the source of the update request.
   * 2. Call the service to update the LinkedIn account status.
   * 3. Return a success or error response based on the result.
   */
  @ApiProperty({
    description: 'Details required to update LinkedIn accounts',
    type: 'object',
    properties: {
      redirect_url: { type: 'string', description: 'The redirect URL' },
      user_id: { type: 'string', description: 'The user ID' },
      source: { type: 'string', description: 'The source of the update request' },
    },
  })
  @ApiOperation({ summary: 'Update LinkedIn Accounts' })
  @ApiResponse({ status: 200, description: 'LinkedIn accounts updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @Post('/update-linkedin')
  async updateLinkedin(@Body() requestBody: { redirect_url: string; user_id: number; source: string }, @Res() res) {
    try {
      // Validate the source of the update request
      if (requestBody?.source === this.configService.get<string>('updateSource')) {
        // Call the service to update the LinkedIn account status
        const response = await this.userApiService.updateLinkedinStatus(requestBody);
        return res.status(HttpStatus.OK).json({ status: HttpStatus.OK, ...response });
      } else {
        // Return an error response if the source is not supported
        return res.status(HttpStatus.OK).json({ type: 'error', msg: 'Only LinkedIn status update supported.' });
      }
    } catch (error: any) {
      // Log the error and rethrow it
      APILog.error('update-linkedin', {
        METHOD: this.constructor.name + '@' + this.updateLinkedin.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  @ApiOperation({ summary: 'To fetch the user info by email' })
  @ApiResponse({ status: 200, description: 'Fetch info successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @UsePipes(new ValidationPipe())
  @UseAPIGuard()
  @Post('/authenticate-saml')
  /**
   * Handles SAML authentication requests.
   *
   * @param requestBody - The request body containing the SAML authentication details.
   * @param res - The HTTP response object used to send the response back to the client.
   * @returns A JSON response with the authentication result.
   * 
   * @throws Will log and rethrow any errors encountered during the authentication process.
   * 
   * @remarks
   * This method uses the `userApiService` to process the SAML authentication request.
   * In case of an error, it logs the error details including the method name, error message,
   * request body, stack trace, and a timestamp.
   * 
   * This api is consumed by Paperclip
   */
  async authenticateSaml(@Body() requestBody: AuthenticateSaml, @Res() res) {
    try {
      let responseType : 'json' | 'redirect' = Utility.isEmpty(requestBody?.redirect_url)  ? 'json' : 'redirect';
      const cookieHelper = await this.helperService.getHelper('CookieHelper');    
      const response = await this.userApiService.authenticateSaml(requestBody);
      if (responseType === 'redirect' && response?.['_t']) {
        cookieHelper.setCookie(res, this.configService.get('ssoCookie'), response['_t']);
        responseType = 'redirect';
      }
      return Utility.returnResponse( response, responseType, requestBody?.redirect_url, res);
    } catch (error: any) {
      APILog.error('authenticateSaml', {
        METHOD: this.constructor.name + '@' + this.authenticateSaml.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * @param requestBody
   * @param res
   * @returns
   * @summary Update the user timezone.
   *
   * @description This endpoint used to update the timezone in the mongo  -e AWS_ACCESS_KEY_ID='********************' \
  -e AWS_SECRET_ACCESS_KEY='QJSbvqkU4hCCtbv6BeURrQ+fWCi/GbNLsxW4n5Gp' \
  -e AWS_SESSION_TOKEN='IQoJb3JpZ2luX2VjECUaCXVzLWVhc3QtMSJHMEUCIHI6VI6DFM5qrfBd2uGzRc3Pbr5+Mc3tGNxHhMM6jo57AiEAjQcOvDBiIY5l3XELDrfbMSad6UsdrqVQ3DYPcoKAGEMqqQIIzv//////////ARAEGgwyNDk0Mzc2MzQ3NDQiDEpx6cw37q9vwp6Tzir9AZIS5E+ybbyieVwUQlQ0kaHvUu7wViUJqfFD5n85g37mjwCn3HJkm3HFQpk/LeFy3GwnjnMuGxSk1WXdbIDfVV+HDt38Hte67M4QHnBIxj0BW+mpX64vlBB544W6JXDTpM//4Nk6oFwjW8Cnc66fLzs6u9uk+gxql5XPAZXGdouOn+ihepFAJdlN44wYAETk1Uef0lUHYhh8I5L9kfsKSryLnr2z2e/qetnQvg7+Nho+Odu2zCeOW+8QZXaewv9aA1fXBEGJx5aiTM9zd35+A1vEH96GxjFpshMgQ1+Cni+xaS/i6G0qZR2fOrM9ypara7kc1ek5vwOw7fRRSMwwkoKGwQY6nQHAbThFWbCMaA7ge6CnkTsSnjhw9O2n5f4U+DgPFHx+8Oo6oTcC9cHcDHqUdGGGgruLAWm9T9MlkOrqUsEZddEIeFQOGh9KDMQAUDP1nmNtKH67U8xaeJ2MxmUxb0IkH7uk9ZQc2pU58CI0Dg0Nm2Y3m8t1i+x+2fPvj2fcFRIW1/dlZuHjziNEilf2obYER31dYKITwSRTCdjVXJrU'\
  --add-host=devlms.simplilearn.com:*********** \
   * @throws {BadRequestException} Throws a BadRequestException if query fails.
   *
   * 1. Extract the timezone via country
   * 2. update the timezone in mongo
   */
  @ApiProperty({})
  @ApiOperation({ summary: 'Update user timezone' })
  @ApiResponse({ status: 200, description: 'User timezone updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @UseAPIGuard()
  @Post('/update-user-timezone')
  async updateUserTimezone(@Body() requestBody: UpdateUserTimezoneDto, @Res() res) {
    try {
      const user = await this.userApiService.updateUserTimezone(requestBody);
      return res.status(HttpStatus.OK).json({ ...requestBody, timezone: user?.timezone });
    } catch (error: any) {
      APILog.error('getTimezoneFromCountryCode', {
        METHOD: this.constructor.name + '@' + this.updateUserTimezone.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error.message === 'FailedUpdateUserTimezone'
        ? new BadRequestException('Some error occurred.')
        : error;
    }
  }

  /**
   * @param requestBody
   * @param res
   * @returns
   * @summary Update the username or display_name.
   *
   * @description This endpoint used to update the display_name in the mongo
   * @throws {BadRequestException} Throws a BadRequestException if query fails.
   *
   * 1. Fetch the user info from mongo by uid
   * 2. update the name in mongo
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Update username' })
  @ApiResponse({ status: 200, description: 'Username updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @Post('/update-username')
  @UseAPIGuard()
  async updateUsername(@Body() requestBody: UpdateUsernameDto, @Res() res) {
    try {
      const updateUser = await this.userApiService.updateUserName(requestBody);
      return res.status(HttpStatus.OK).json({
        type: 'success',
        msg: this.configService.get('updateUsernameSuccess'),
        data: updateUser,
      });
    } catch (error: any) {
      // Log and handle the error
      APILog.error('updateUsername', {
        METHOD: this.constructor.name + '@' + this.updateUsername.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error instanceof BadRequestException) {
        throw new BadRequestException({ message: error.message, isData: { data: '' } });
      } else {
        throw new InternalServerErrorException(error.message);
      }
    }
  }

  /**
   * @summary Update User Profile
   * @description This endpoint allows users to update their profile information.
   *
   * @param {UpdateProfileDto} body - The profile data to be updated.
   * @param {Response} res - The Express response object.
   * @returns {object} The response indicating the success or failure of the profile update.
   *
   * @throws {Error} Throws an error if the profile update fails.
   *
   * Steps:
   * 1. Validate the input data using ValidationPipe.
   * 2. Call the service to update the user profile.
   * 3. Return a success response or handle errors.
   * 
   * This api is consumed by Paperclip
   */
  @ApiProperty({
    description: 'Details required to update the user profile',
    type: UpdateProfileDto,
  })
  @ApiOperation({ summary: 'Update User Profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @Patch('/update-profile')
  async updateProfile(@Body(new ValidationPipe()) body: UpdateProfileDto) {
    try {
      return await this.userApiService.updateProfile(body);;
    } catch (error: any) {
      APILog.error('updateProfile', {
        METHOD: this.constructor.name + '@' + this.updateProfile.name,
        MESSAGE: error.message,
        REQUEST: body,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { type: 'error', msg: 'Error occurred while updating profile. Please try again.' };
    }
  }

  /**
   * @summary Fetch user information by email.
   * @description This endpoint retrieves user details based on the provided email address.
   *
   * @param {string} email - The email address of the user.
   * @returns {object} The user information.
   *
   * @throws {Error} Throws an error if the user retrieval fails.
   * 
   * This api is consumed by Paperclip, Xenia, Ice9, EngageX, Consumers and Caldon
   */
  @ApiOperation({ summary: 'Fetch user information by email' })
  @ApiResponse({ status: 200, description: 'User information fetched successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(new HttpExceptionFilter())
  @UsePipes(new ValidationPipe())
  @UseAPIGuard()
  @Get('/get-users-by-email')
  async getUserByEmail(@Query('mail') email: string) {
    try {
      return await this.userApiService.getUserByEmail(email);
    } catch (err: any) {
      APILog.error('getUserByEmail', {
        METHOD: this.constructor.name + '@' + this.getUserByEmail.name,
        MESSAGE: err?.message,
        REQUEST: email,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return [];
    }
  }

  /**
   * @summary Fetch user information by UID.
   * @description This endpoint retrieves user details based on the provided UID.
   *
   * @param {string} uid - The unique identifier of the user.
   * @returns {object} The user information.
   *
   * @throws {Error} Throws an error if the user retrieval fails.
   * 
   * This api is consumed by paperclip, Certificate, Ice9, Xenia and EngageX
   */
  @ApiOperation({ summary: 'Fetch user information by UID' })
  @ApiResponse({ status: 200, description: 'User information fetched successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @UseAPIGuard()
  @Get('/get-users-by-uid')
  async getUserByUID(@Query('uid') uid: string) {
    try {
      return await this.userApiService.getUserByUid(uid);
    } catch (err: any) {
      APILog.error('getUserByUID', {
        METHOD: this.constructor.name + '@' + this.getUserByUID.name,
        MESSAGE: err?.message,
        REQUEST: uid,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return [];
    }
  }

  /**
   * @summary Authenticate a user.
   * @description This endpoint allows users to authenticate using their email and password.
   *
   * @param {AuthenticationRequestDto} data - Data for user authentication.
   * @param {object} res - Express response object.
   * @returns {object} The authentication result.
   *
   * @throws {BadRequestException} Throws a BadRequestException if authentication fails.
   *
   * Steps:
   * 1. Create utility and service instances.
   * 2. Authenticate the user.
   * 3. Handle authentication failure.
   * 4. Extract the token and LMS URL (if provided).
   * 5. Send a successful response or handle errors.
   * 
   * This api is consumed by EngageX
   */
  @ApiProperty()
  @ApiOperation({ summary: 'Authenticate' })
  @ApiResponse({ status: 200, description: 'success' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiBody({ type: AuthenticationRequestDto, description: 'authenticate' })
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe())
  @UseFilters(new HttpExceptionFilter())
  @UseAPIGuard()
  @Post('/authenticate')
  async authenticate(@Body(new ValidationPipe()) data: AuthenticationRequestDto, @Res({passthrough : true}) res) {
    try {
      let responseType : 'json' | 'redirect' = Utility.isEmpty(data?.redirect_url)  ? 'json' : 'redirect';
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      const response = await this.userApiService.authenticate(data);
      Logger.log('Return response', {
        RESPONSE: response,
      });
      if (responseType === 'redirect' && response?.['_t']) {
        cookieHelper.setCookie(res, this.configService.get('ssoCookie'), response['_t']);
        responseType = 'redirect';
      }
      return Utility.returnResponse( response, responseType, data.redirect_url, res);
    } catch (error: any) {
      APILog.error('authenticate', {
        METHOD: this.constructor?.name + '@' + this.authenticate?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Some error occurred while authenticating user.');
    }
  }

  /**
   * @summary Check Signup Validation
   * @description This endpoint validates the signup data provided by the user.
   *
   * @param {CheckSignupValidationDto} signupData - The signup data to validate.
   * @returns {object} The validation response.
   *
   * @throws {Error} Throws an error if validation fails.
   * This api is consumed by Paperclip.
   */
  @ApiOperation({ summary: 'Check Signup Validation' })
  @ApiResponse({ status: 200, description: 'Validation successful' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @Post('check-signup-validation')
  @UseFilters(new HttpExceptionFilter())
  async checkSignupValidationAction(@Body(new ValidationPipe()) signupData: CheckSignupValidationDto) {
    try {
      const response = await this.userApiService.checkSignupValidation(signupData);
      return response;
    } catch (error: any) {
      APILog.error('checkSignupValidationAction', {
        METHOD: this.constructor?.name + '@' + this.checkSignupValidationAction?.name,
        MESSAGE: error.message,
        REQUEST: signupData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {
        type: 'error',
        msg: 'Error occurred while creating the user.',
      };
    }
  }

  /**
   * @summary Fetch Roles
   * @description This endpoint retrieves the roles available in the system.
   *
   * @param {object} query - The query parameters for pagination.
   * @param {number} query.pageSize - The number of roles per page.
   * @param {number} query.page - The page number.
   * @param {Response} res - The Express response object.
   * @returns {object} The roles data.
   *
   * @throws {Error} Throws an error if fetching roles fails.
   */
  @ApiOperation({ summary: 'Fetch Roles' })
  @ApiResponse({ status: 200, description: 'Roles fetched successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe())
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  @Get('/role')
  async getRoles(@Query() query: { pageSize: number; page: number, prefix?: string }, @Res() res) {
    try {
      const response = await this.userApiService.getUserRoles(query);
      return res.status(HttpStatus.OK).json(response);
    } catch (err: any) {
      APILog.error('getRoles', {
        METHOD: this.constructor.name + '@' + this.getRoles.name,
        MESSAGE: err?.message,
        REQUEST: query,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return [];
    }
  }

  /**
   * @summary Assign User Role
   * @description This endpoint assigns a role to a user.
   *
   * @param {object} reqBody - The request body containing user and role details.
   * @param {string} reqBody.uid - The user ID.
   * @param {string} reqBody.rid - The role ID.
   * @param {Response} res - The Express response object.
   * @returns {object} The response indicating the success or failure of the role assignment.
   *
   * @throws {Error} Throws an error if the role assignment fails.
   */
  @ApiOperation({ summary: 'Assign User Role' })
  @ApiResponse({ status: 200, description: 'Role assigned successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(HttpExceptionFilter)
  @Post('/assign-user-role')
  async assignUserRole(@Body() reqBody: { uid: number; rid: string }, @Res() res) {
    try {
      const response = await this.userApiService.assignUserRole(reqBody);
      return res.status(HttpStatus.OK).json(response);
    } catch (err: any) {
      APILog.error('assignUserRole', {
        METHOD: this.constructor.name + '@' + this.assignUserRole.name,
        MESSAGE: err?.message,
        REQUEST: reqBody,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.status(HttpStatus.OK).json({});
    }
  }

  /**
   * @summary Revoke User Role
   * @description This endpoint revokes a role from a user.
   *
   * @param {object} reqBody - The request body containing user and role details.
   * @param {string} reqBody.uid - The user ID.
   * @param {string} reqBody.rid - The role ID.
   * @param {Response} res - The Express response object.
   * @returns {object} The response indicating the success or failure of the role revocation.
   *
   * @throws {Error} Throws an error if the role revocation fails.
   */
  @ApiOperation({ summary: 'Revoke User Role' })
  @ApiResponse({ status: 200, description: 'Role revoked successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  @UsePipes(new ValidationPipe())
  @UseFilters(HttpExceptionFilter)
  @Post('/revoke-user-role')
  async revokeUserRole(@Body() reqBody: { uid: number; rid: string }, @Res() res) {
    try {
      const response = await this.userApiService.revokeUserRole(reqBody);
      return res.status(HttpStatus.OK).json(response);
    } catch (err: any) {
      APILog.error('revokeUserRole', {
        METHOD: this.constructor.name + '@' + this.revokeUserRole.name,
        MESSAGE: err?.message,
        REQUEST: reqBody,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.status(HttpStatus.OK).json({});
    }
  }

  /**
   * @summary Fetch the original token.
   * @description This endpoint allows fetching the original token based on the provided redirect URL and token.
   *
   * @param {object} requestBody - The request body containing redirect_url and token.
   * @param {string} requestBody.redirect_url - The redirect URL.
   * @param {string} requestBody.token - The token to fetch the original token.
   * @param {object} res - The Express response object.
   * @returns {object} The response containing the original token.
   *
   * return [];
   * This api is consumed by EngageX
   */
  @ApiOperation({ summary: 'Fetch the original token' })
  @ApiResponse({ status: 200, description: 'Original token fetched successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(new HttpExceptionFilter())
  @UsePipes(new ValidationPipe())
  @UseAPIGuard()
  @Post('/get-original-token')
  async getOriginalToken(
    @Body() requestBody: { redirect_url: string; token: string },
    @Res() res,
  ) {
    try {
      const response = await this.userApiService.getOriginalToken(requestBody);
      return res.status(HttpStatus.OK).json({ ...response });
    } catch (err: any) {
      APILog.error('get-original-token', {
        METHOD: this.constructor.name + '@' + this.getOriginalToken.name,
        MESSAGE: err?.message,
        REQUEST: requestBody,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return [];
    }
  }

  /**
   * @summary Fetch Taxonomy Data
   * @description This endpoint retrieves taxonomy data based on the provided query parameters.
   *
   * @param {object} data - The query parameters for fetching taxonomy data.
   * @param {Response} res - The Express response object.
   * @returns {object} The taxonomy data.
   *
   * @throws {Error} Throws an error if fetching taxonomy data fails.
   */
  @ApiOperation({ summary: 'Fetch Taxonomy Data' })
  @ApiResponse({ status: 200, description: 'Taxonomy data fetched successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(new HttpExceptionFilter())
  @Get('/get-taxonomy')
  async getTaxonomy(@Query() data, @Res() res) {
    try {
      const response = await this.userApiService.getTaxonomy(data);
      return res.status(HttpStatus.OK).json(response);
    } catch (err: any) {
      APILog.error('getTaxonomy', {
        METHOD: this.constructor.name + '@' + this.getTaxonomy.name,
        MESSAGE: err?.message,
        REQUEST: data,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.status(HttpStatus.BAD_REQUEST).json({ type: 'error', msg: 'Failed to fetch taxonomy data.' });
    }
  }

  /**
   * @param reqData - The request data containing Google One Tap response details.
   * @param res - The Express response object.
   * @returns The processed response or a redirect to the token URL.
   *
   * @description This endpoint processes the Google One Tap response.
   * It validates the response, processes the credential, and either redirects
   * the user to a token URL or returns the response.
   *
   * @throws {Error} Throws an error if processing fails.
   * 
   * This api is consumed by Sheldon
   */
  @Post('/process-google-one-tap-response')
  @UseFilters(HttpExceptionFilter)
  async processGoogleOneTapResponse(
    @Body() reqData: { is_frs_page: string; credential: string; calling_api_from: string },
    @Res() res,
  ) {
    try {
      Logger.log('Processing Google One Tap response', { reqData });
      const response = await this.userApiService.processGoogleOneTapResponse(reqData, res);
      Logger.log('Google One Tap response processed successfully', { response });

      if (response?.tokenRedirectUrl) {
        Logger.log('Redirecting to token URL', { tokenRedirectUrl: response.tokenRedirectUrl });
        return res.redirect(response?.tokenRedirectUrl);
      }

      return res.status(HttpStatus.OK).json(response);
    } catch (err: any) {
      Logger.error('Error processing Google One Tap response', {
        METHOD: this.constructor.name + '@' + this.processGoogleOneTapResponse.name,
        MESSAGE: err.message,
        REQUEST: reqData,
        RESPONSE: err.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw err;
    }
  }

  @Get('/test-api')
  async testApi(@Res() res) {
    try {
      const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
      const groupDomainData = await enterpriseService.getGroupByGid(2514);
      console.log(groupDomainData?.data[0])
      res.json(groupDomainData);
    } catch (err: any) {
      throw err;
    }
  }
  
  //This api is consumed by Paperclip
  @Post('get-taxonomy-name')
  async getTaxonomyName(@Body() body: { uid: string }, @Res() res) {
    try {
      const response = await this.userApiService.getTaxonamyName(body.uid); 
      return res.status(HttpStatus.OK).json(response);}
    catch (err: any) {
      Logger.error('Error fetching taxonomy name', {
        METHOD: this.constructor.name + '@' + this.getTaxonomyName.name,
        MESSAGE: err?.message,
        REQUEST: body,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });

    }
  }

  /**
 * @summary Fetch Term List by Taxonomy
 * @description This endpoint retrieves term lists for one or more taxonomy machine names.
 *              It also includes title, gender, and industry_disable values.
 *
 * @param {object} query - Query parameters containing taxonomy[] or taxonomy (machine names).
 * @param {Response} res - The Express response object.
 * @returns {object} JSON response with term list mapped to taxonomy categories, including title and gender data.
 *
 * @throws {BadRequestException} Throws when taxonomy parameter is missing or invalid.
 * @throws {Error} Throws when internal processing fails.
 * This api is consumed by Ice9 and Paperclip.
 */
  @ApiOperation({ summary: 'Fetch Term List by Taxonomy' })
  @ApiResponse({ status: 200, description: 'Taxonomy term list fetched successfully' })
  @ApiResponse({ status: 400, description: 'Missing or invalid taxonomy parameter' })
  @Get('/get-term-list-by-taxonomy')
  async getTermListByTaxonomy(@Query() query, @Res() res) {
    try {
      let taxonomy: string | string[] = query['taxonomy[]'] || query['taxonomy'];
      let appType: string | undefined = query['appType']||'';

      if (!taxonomy) {
        throw new BadRequestException('Please provide taxonomy machine name');
      }

      const taxonomyList: string[] = Array.isArray(taxonomy) ? taxonomy : [taxonomy];

      const filteredTaxonomyList = taxonomyList
        .filter((item): item is string => typeof item === 'string' && item.trim() !== '')
        .map((item) => item.trim());

      if (filteredTaxonomyList.length === 0) {
        throw new BadRequestException('Please provide taxonomy machine name');
      }

      const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');

      const termList = {};
      for (const category of filteredTaxonomyList) {
        const terms = await this.userApiService.getTermsListByTaxonomyCategory(category,appType);
        if (terms && Object.keys(terms).length > 0) {
          termList[category] = terms;
        }
      }

      const titleGenderList = await userMgmtUtilityHelperInstance.getTitleGenderList();

      return res.status(HttpStatus.OK).json({
        type: 'success',
        termlist: {
          ...termList,
          title: titleGenderList.title,
          gender: titleGenderList.gender,
          industry_disable: titleGenderList.industry_disable,
        },
      });
    } catch (err: any) {
      Logger.error('Error fetching taxonomy terms', {
        METHOD: this.constructor.name + '@' + this.getTermListByTaxonomy.name,
        MESSAGE: err?.message,
        CATEGORY: query['taxonomy[]'] || query['taxonomy'],
        STACK: err?.stack,
        TIMESTAMP: new Date().toISOString(),
      });
      return res.json({});
    }
  }

  /**
 * @summary Validate SSO Token
 * @description This endpoint validates a JWT token sent by the client for authentication purposes.
 * If the token is valid and associated with a user, session tokens are generated.
 *
 * @param {object} body - The request payload containing client_id, token, and optional redirect_url.
 * @returns {object} JSON response with either an error or the generated session token (_t).
 *
 * @throws {Error} Returns a structured error message on failure.
 * This api is consumed by Ice9.
 */
  @ApiOperation({ summary: 'Validate SSO Token' })
  @ApiResponse({ status: 200, description: 'Token validated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request or token' })
  @ApiResponse({ status: 500, description: 'Internal Server Error during token validation' })
  @Post('/validate-token')
  async validateToken(@Body() body: {
    client_id: string;
    redirect_url?: string;
    token: string;
  }) {
    try{
   
    const response =  this.userApiService.validateToken(body);
    return response
    
  }catch(err: any) {
    Logger.error('validateToken', {
      METHOD: this.constructor.name + '@' + this.validateToken.name,
      MESSAGE: err?.message,
      REQUEST: body,
      RESPONSE: err?.stack,
      TIMESTAMP: new Date().toISOString(),
    });
  }
  }

/**
  * @summary Save User Profile and Form Data
  * @description This endpoint saves user profile and form data based on the provided parameters.
  * It handles different types of profile updates including basic, contact, professional, academics, and outcome.
  *
  * @param {SaveProfileDto} body - The profile data to be saved
  * @returns {object} Response indicating success or failure of the operation
  * 
  * This api is consumed by Paperclip
  */
  @ApiOperation({ summary: 'Save User Profile and Form Data', description: 'This endpoint saves user profile and form data based on the provided parameters.' })
  @HttpCode(HttpStatus.CREATED)
  @UseAPIGuard()
  @UseFilters(new HttpExceptionFilter())
  @ApiQuery({ name: 'email', required: true })
  @ApiQuery({ name: 'edit_type', required: true, enum: ['basic', 'contact', 'professional', 'academics', 'outcome'] })
  @ApiQuery({ name: 'isB2bStudent', required: false })
  @ApiQuery({ name: 'userId', required: true })
  @ApiBody({ type: SaveProfileDto })
  @ApiResponse({ status: 200, description: 'Profile data saved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid Request' })
  @Post('/save-profile-and-form-data')
  async saveProfileAndFormData(@Body(new ValidationPipe()) body: SaveProfileDto, @Req() req) {
    try {
      const editType = body.edit_type?.trim();
      const userId = body.userId;
      const userEmail = body.email?.trim();
      const isB2bStudent = body.isB2bStudent?.trim() === 'true';
  
      const validEditTypes = ['basic', 'contact', 'professional', 'academics', 'outcome'];
      const inputParams = { ...req.query, ...req.body, ...req.params };
  
      if (!validEditTypes.includes(editType)) {
        return {
          type: 'error',
          msg: 'Invalid Request.',
        };
      }
  
      const saveProfileResult = await this.userApiService.saveProfileAndFormData({
        btnSignup: editType,
        userId,
        userEmail,
        isB2bStudent,
      }, inputParams);
  
      return saveProfileResult || {
        type: 'error',
        msg: 'Invalid Request.',
      };
    } catch (err: any) {
      Logger.error('saveProfileAndFormData', {
        METHOD: `${this.constructor.name}@${this.saveProfileAndFormData.name}`,
        MESSAGE: err?.message,
        REQUEST: body,
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().toISOString(),
      });
  
      throw err;
    }
  }
  
@HttpCode(HttpStatus.OK)
@Post('/update-fetch-roles')
async updateAndFetchUserRoles(@Body() reqBody: { uid: number; roles: string[] }) {
  try {
    const result = await this.userApiService.updateAndFetchUserRoles(reqBody);
    return {
      type: 'success', data: result
    };
  } catch (err: any) {
    Logger.error('updateAndFetchUserRoles', {
      METHOD: this.constructor.name + '@' + this.updateAndFetchUserRoles.name,
      MESSAGE: err?.message,  
      REQUEST: reqBody,
      RESPONSE: err?.stack,
      TIMESTAMP: new Date().toISOString(),
    });
  }
}


/**
 * @summary Get Profile Completion Stats
 * @description This endpoint retrieves the profile completion statistics for a user.
 * It calculates how complete a user's profile is based on filled fields.
 *
 * @param {Object} body - Request body containing user ID
 * @returns {Object} Response with profile completion percentage
 */
@Post('profile-completion-stats')
@ApiOperation({ 
  summary: 'Get Profile Completion Stats', 
  description: 'Retrieves the profile completion statistics for a user' 
})
@HttpCode(HttpStatus.OK)
@UseAPIGuard()
@UseFilters(new HttpExceptionFilter())
@ApiBody({ 
  schema: {
    type: 'object',
    required: ['userId'],
    properties: {
      userId: { type: 'string', description: 'User ID' }
    }
  }
})
@ApiResponse({ status: 200, description: 'Profile completion stats retrieved successfully' })
@ApiResponse({ status: 400, description: 'Invalid request data' })
@ApiResponse({ status: 500, description: 'Internal server error' })
async getProfileCompletionStats(@Body() body: { userId: number }) {
  try {
    // Validate request
    if (!body.userId) {
      return {
        type: 'error',
        msg: 'Invalid Request. User ID is required.'
      };
    }

    const userId = body.userId;
    
    // Get user repository and profile helper
    const [profileHelper, userRepository] = await Promise.all([
      this.helperService.getHelper('ProfileHelper'),
      this.helperService.get<IUserRepository>(UserRepository)
    ]);
    
    // Load user data
    const user = await userRepository.findByUID(userId);
    
    // Check if user exists
    if (!user) {
      return {
        type: 'error',
        msg: 'User not found.'
      };
    }
    
    // Calculate profile completion stats
    const profileCompletion = await profileHelper.getProfileCompletionStats(user,true,'Yes',false);
    if (profileCompletion && profileCompletion?.overallCompletion) {
    // Return response
      return {
        type: 'success',
        profileCompletion: Number(profileCompletion?.overallCompletion)
      };
    }
  } catch (err: any) {
    Logger.error('getProfileCompletionStats', {
      METHOD: this.constructor.name + '@' + this.getProfileCompletionStats.name,
      MESSAGE: err.message,
      REQUEST: body,
      RESPONSE: err.stack,
      TIMESTAMP: new Date().toISOString(),
    });
    
    return {
      type: 'error',
      msg: 'An error occurred while retrieving profile completion stats.'
    };
    } 
}



@ApiOperation({ summary: 'remove user roles', description: 'This endpoint is created for removinguser roles. This endpoint is used by Enterprise Service.' })
@ApiResponse({ status: 200, description: 'User roles removed successfully' })
@ApiResponse({ status: 400, description: 'Invalid Request' })
@Post('/remove-user-roles')
async removeUserRoles(@Body() reqBody: { uid: string; roles: string[] }) {
  try {
    const result = await this.userApiService.removeUserRoles(reqBody);

    if (result !== true) {
      return { type: 'error', msg: result };
    }
    return { type: 'success', msg: 'User roles removed successfully' };
  } catch (error: any) {
    Logger.error('removeUserRoles', {
      METHOD: this.constructor.name + '@' + this.removeUserRoles.name,
      MESSAGE: error.message,
      REQUEST: reqBody,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().toISOString(),
    });
    return {type: 'error', msg: error.message};
  }
}

/**
 * @summary Create New Role
 * @description This endpoint creates a new role in both MySQL and MongoDB using the provided role details.
 * It first checks if the role already exists in MySQL and MongoDB, then saves it in both systems.
 *
 * @param {RoleDto} reqBody - The role data to be created (e.g., `rid`, `roleName`)
 * @returns {object} Response indicating success or failure of the operation
 */
@ApiOperation({ summary: 'Create new role', description: 'Creates a new role in both MySQL and MongoDB using the provided role details.' })
@ApiBody({ type: RoleDto })
@ApiResponse({ status: 200, description: 'Role created successfully' })
@ApiResponse({ status: 400, description: 'Role creation failed' })
@Post('/create-role')
@HttpCode(HttpStatus.OK)
async createRole(@Body() reqBody: RoleDto) {
  try {
    const result = await this.userApiService.createRole(reqBody);

    if (!result) {
      return { type: 'error', msg: 'Role creation failed' };
    }

    return { type: 'success', msg: 'Role created successfully', data: result };
  } catch (error: any) {
    Logger.error('createRole', {
      METHOD: this.constructor.name + '@' + this.createRole.name,
      MESSAGE: error.message,
      REQUEST: reqBody,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().toISOString(),
    });

    return { type: 'error', msg: error.message };
  }
}

/**
 * @summary Update Existing Role
 * @description This endpoint updates an existing role in both MySQL and MongoDB using the provided role details.
 * It checks if the role exists in both databases before performing the update.
 *
 * @param {RoleDto} reqBody - The role data to be updated (e.g., `rid`, `roleName`)
 * @returns {object} Response indicating success or failure of the operation
 */
@ApiOperation({
  summary: 'Update Existing Role',
  description: 'Updates an existing role in both MySQL and MongoDB using the provided role details.',
})
@ApiBody({ type: RoleDto })
@ApiResponse({ status: 200, description: 'Role updated successfully' })
@ApiResponse({ status: 400, description: 'Role updation failed' })
@Post('/update-role')
@HttpCode(HttpStatus.OK)
async updateRole(@Body() reqBody: RoleDto) {
  try {
    const result = await this.userApiService.updateRole(reqBody);

    if (!result) {
      return { type: 'error', msg: 'Role updation failed' };
    }

    return { type: 'success', msg: 'Role updated successfully', data: result };
  } catch (error: any) {
    Logger.error('createRole', {
      METHOD: this.constructor.name + '@' + this.createRole.name,
      MESSAGE: error.message,
      REQUEST: reqBody,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().toISOString(),
    });

    return { type: 'error', msg: error.message };
  }
}
}
