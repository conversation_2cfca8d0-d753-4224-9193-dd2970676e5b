import { IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsPasswordValid } from '../../common/validators/password.validator';

export class CheckSignupValidationDto {
  @Transform(({ value }) => value.trim())
  time_zone: string;

  @Transform(({ value }) => value.trim())
  affiliateId = 2;

  @Transform(({ value }) => value.trim())
  client_id: string;

  @Transform(({ value }) => value.trim())
  redirect_url: string;

  @Transform(({ value }) => value.trim())
  from_mobile: string;

  @Transform(({ value }) => value.trim())
  device_type: string;

  @Transform(({ value }) => value.trim())
  user_name: string;

  @IsEmail({}, { message: 'Please provide valid email id.' })
  @Transform(({ value }) => value.trim())
  user_email: string;

  @Transform(({ value }) => value.trim())
  @IsPasswordValid()
  user_pwd: string;

  @Transform(({ value }) => value.trim())
  register_mode: string;

  @Transform(({ value }) => value.trim())
  country_id: number;

  @Transform(({ value }) => value.trim())
  country_code = 'US';

  @Transform(({ value }) => value.trim())
  city_id = 0;

  @Transform(({ value }) => value.trim())
  auto_login = 'N';

  user_roles: number[];

  @Transform(({ value }) => value.trim())
  user_type: string;

  @Transform(({ value }) => value.trim())
  email_agreement = 'Y';
}
