import { Test, TestingModule } from '@nestjs/testing';
import { UserApiService } from './user.api.service';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { UserApiLoginDto } from '../dto/v1-login.dto';
import { Utility } from '../../common/util/utility';
import { CheckSignupValidationDto } from '../dto/signup-validation.dto';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { TaxonomyRepository } from '../../user/repositories/taxonomy/taxonomy.repository';
import { UpdateUserTimezoneDto } from '../dto/update-user-tmezone.dto';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { UserService } from '../../user/services/user.service';
import { RoleRepository } from '../../user/repositories/role/role.repository';
import { Logger } from '@nestjs/common';

describe('UserApiService', () => {
  let service: UserApiService;
  let helperServiceMock: HelperService;
  let configServiceMock: ConfigService;


  const drupalHash = {
    checkPassword: jest.fn(),
    hashPassword: jest.fn(),
  };

  const userRepositoryMock = {
    getUserByEmail: jest.fn(),
    find: jest.fn(),
    findByUID: jest.fn(),
    findOneAndUpdate: jest.fn(),
  };

  const userHelperMock = {
    getUserInfo: jest.fn(),
    getUserEmail: jest.fn(),
    lmsSettingsCacheData: jest.fn(),
  };

  const mockLogger = {
    error: jest.fn(),
    log: jest.fn(),
  };

  // const utilityMock = {
  //   validateClientRequest: jest.fn(),
  // };

  const cloud6ServiceMock = {
    getLmsEnterpriseSettings: jest.fn(),
  };

  const createCryptoHelperMock = {
    createHmac: jest.fn(),
  };

  const taxonomyRepositoryMock = {
    find: jest.fn(),
    aggregate: jest.fn(),
  };

  const userMgmtUtilityHelperMock = {
    updateUserTimezone: jest.fn().mockResolvedValue({
      /* mock user data after update */
    }),
  };

  const authTokenHelperMock = {
    encodeOriginalToken: jest.fn().mockResolvedValue('mockEncodedToken'),
  };

  beforeEach(async () => {
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserApiService,
        HelperService,
        Cloud6Service,
        {
          provide: TaxonomyRepository,
          useValue: taxonomyRepositoryMock,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'clientSecret') return 'mockSecret';
              if (key === 'drupal_hash_salt') return 'mockSalt';
              return undefined;
            }),
          },
        },
        { provide: 'CRYPTO_HELPER', useValue: createCryptoHelperMock },
      ],
    }).compile();

    service = module.get<UserApiService>(UserApiService);
    helperServiceMock = module.get<HelperService>(HelperService);
    configServiceMock = module.get<ConfigService>(ConfigService);
  });

  describe('updateUserName', () => {
    it('should update the user name successfully', async () => {
      const reqBody = { uid: '123', name: 'newName' };
      const userRepositoryMock = {
        findByUID: jest.fn().mockResolvedValue({ status: 1 }),
        findOneAndUpdate: jest.fn().mockResolvedValue({ display_name: 'newName' }),
      };

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(userRepositoryMock);
      jest.spyOn(configServiceMock, 'get').mockReturnValue('updateUsernameNotExist');

      const result = await service.updateUserName(reqBody);

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('123');
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith({ uid: '123' }, { display_name: 'newName' });
      expect(result).toEqual({ display_name: 'newName' });
    });

    it('should throw BadRequestException if user is not found', async () => {
      const reqBody = { uid: '123', name: 'newName' };
      const userRepositoryMock = {
        findByUID: jest.fn().mockResolvedValue(null),
      };

      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(userRepositoryMock);
      jest.spyOn(configServiceMock, 'get').mockReturnValue('updateUsernameNotExist');

      await expect(service.updateUserName(reqBody)).rejects.toThrowError(BadRequestException);
    });
  });

  describe('login', () => {
    let userRepositoryMock: any;
    let socialLoginHelperMock: any;
    let cookieHelperMock: any;

    beforeEach(() => {
      userRepositoryMock = {
        getUserByEmail: jest.fn(),
      };

      socialLoginHelperMock = {
        processSocialAuthResponse: jest.fn(),
      };

      cookieHelperMock = {
        setCookie: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'SocialLoginHelper') {
          return Promise.resolve(socialLoginHelperMock);
        }
        if (helperName === 'CookieHelper') {
          return Promise.resolve(cookieHelperMock);
        }
        return null;
      });

      jest.spyOn(configServiceMock, 'get').mockImplementation((key: string) => {
        if (key === 'clientSecret') return 'mockSecret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google', 'facebook'] };
        return undefined;
      });
    });

    it('should return success response for valid social login', async () => {
      const data: UserApiLoginDto = {
        email: '<EMAIL>',
        method: 'google',
        authToken: 'mockToken',
        client_id: 'mockClientId',
        from_mobile: '1',
        device_type: 'android',
      };

      const userMock = { email: '<EMAIL>', status: 1 };
      const socialAuthResponseMock = {
        returnResponse: { type: 'success', socialAccountStatus: true, userAccountStatus: true },
      };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue(socialAuthResponseMock);

      const result = await service.login(data, {});

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(socialLoginHelperMock.processSocialAuthResponse).toHaveBeenCalledWith(
        expect.objectContaining({ email: '<EMAIL>', type: 'google' }),
        userMock,
      );
      expect(result).toEqual(expect.objectContaining({ type: 'success' }));
    });

    it('should return error if user does not exist', async () => {
      const data: UserApiLoginDto = {
        email: '<EMAIL>',
        method: 'google',
        authToken: 'mockToken',
        client_id: 'mockClientId',
      };

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      const result = await service.login(data, {});

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        type: 'error',
        msg: 'No active account associated with this email address',
      });
    });

    it('should return error for invalid credentials', async () => {
      const data: UserApiLoginDto = {
        email: '<EMAIL>',
        password: 'wrongPassword',
        method: 'email',
        client_id: 'mockClientId',
      };

      const userMock = { email: '<EMAIL>', status: 1, password: 'hashedPassword' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      jest.spyOn(drupalHash, 'checkPassword').mockResolvedValue(false);

      const result = await service.login(data, {});

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(drupalHash.checkPassword).toHaveBeenCalledWith('wrongPassword', 'hashedPassword');
      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid user credentials.',
      });
    });

    it('should handle social login with invalid platform', async () => {
      const data: UserApiLoginDto = {
        email: '<EMAIL>',
        method: 'invalidPlatform',
        authToken: 'mockToken',
        client_id: 'mockClientId',
      };

      const userMock = { email: '<EMAIL>', status: 1 };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);

      const result = await service.login(data, {});

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should handle errors and log them', async () => {
      const data: UserApiLoginDto = {
        email: '<EMAIL>',
        method: 'google',
        authToken: 'mockToken',
        client_id: 'mockClientId',
      };

      const error = new Error('Unexpected error');
      userRepositoryMock.getUserByEmail.mockRejectedValue(error);

      const result = await service.login(data, {});

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });
  });

  describe('getUserByEmail', () => {
    it('should retrieve user information by email', async () => {
      // Mocking UserRepository and UserHelper
      const userRepositoryMock = {
        find: jest.fn().mockResolvedValue([{ id: 1, name: 'John' }]),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        }
        return null;
      });

      // Test data
      const emails = '<EMAIL>';

      jest.spyOn(userHelperMock, 'getUserInfo').mockReturnValue({ id: 1, name: 'John' });

      // Executing the method
      const result = await service.getUserByEmail(emails);

      // Assertions
      // expect(helperServiceMock.get).toHaveBeenCalledWith('UserRepository');
      expect(userRepositoryMock.find).toHaveBeenCalledWith({ email: { $in: ['<EMAIL>'] } });
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(userHelperMock.getUserInfo).toHaveBeenCalledWith([{ id: 1, name: 'John' }]);
      expect(result).toEqual({ id: 1, name: 'John' });
    });
    it('should handle errors and log them', async () => {
      // Mocking HelperService to throw an error
      jest.spyOn(helperServiceMock, 'get').mockRejectedValue(new Error('HelperService error'));

      // Test data
      const emails = '<EMAIL>';

      // Executing the method and expecting it to throw an error
      await expect(service.getUserByEmail(emails)).rejects.toThrowError('HelperService error');
    });
  });

  describe('getUserEmail', () => {
    it('should retrieve user email by user ID', async () => {
      // Mocking UserRepository and UserHelper
      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue({ id: 1, email: '<EMAIL>' }),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        }
        return null;
      });

      // Test data
      const email = '<EMAIL>';

      jest.spyOn(userHelperMock, 'getUserEmail').mockReturnValue(email);
      // Executing the method
      const result = await service.getUserEmail(email);

      // Assertions
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(userHelperMock.getUserEmail).toHaveBeenCalledWith({ id: 1, email: '<EMAIL>' });
      expect(result).toEqual('<EMAIL>');
    });

    it('should handle errors and log them', async () => {
      // Mocking HelperService to throw an error
      jest.spyOn(helperServiceMock, 'get').mockRejectedValue(new Error('HelperService error'));

      // Test data
      const email = '<EMAIL>';

      // Executing the method and expecting it to throw an error
      await expect(service.getUserEmail(email)).rejects.toThrowError('HelperService error');
    });
  });

  describe('getUserByUid', () => {
    it('should retrieve user information by UID', async () => {
      // Mocking UserRepository and UserHelper
      const userRepositoryMock = {
        find: jest.fn().mockResolvedValue([{ uid: '123', name: 'John' }]),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        }
        return null;
      });

      // Test data
      const uIds = '123';

      jest.spyOn(userHelperMock, 'getUserInfo').mockReturnValue([{ uid: '123', name: 'John' }]);

      // Executing the method
      const result = await service.getUserByUid(uIds);

      // Assertions
      expect(userRepositoryMock.find).toHaveBeenCalledWith({ uid: { $in: ['123'] } });
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(userHelperMock.getUserInfo).toHaveBeenCalledWith([{ uid: '123', name: 'John' }]);
      expect(result).toEqual([{ uid: '123', name: 'John' }]);
    });

    it('should handle errors and log them', async () => {
      // Mocking HelperService to throw an error
      jest.spyOn(helperServiceMock, 'get').mockRejectedValue(new Error('HelperService error'));

      // Test data
      const uIds = '123';

      // Executing the method and expecting it to throw an error
      await expect(service.getUserByUid(uIds)).rejects.toThrowError('HelperService error');
    });
  });

  describe('getLmsSettingsFromGid', () => {
    it('should return lms settings when data is available', async () => {
      // Mocking the necessary dependencies
      const groupId = '2';
      const enterpriseSettings = {
        data: {
          data: {},
        },
      };
      const lmsSettings = 'mocluserhelper_lmsSettingsCacheData';

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === Cloud6Service) {
          return Promise.resolve(cloud6ServiceMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        }
        return null;
      });

      jest.spyOn(cloud6ServiceMock, 'getLmsEnterpriseSettings').mockResolvedValueOnce(enterpriseSettings);
      jest.spyOn(userHelperMock, 'lmsSettingsCacheData').mockResolvedValueOnce('mocluserhelper_lmsSettingsCacheData');
      Utility.isEmpty = jest.fn().mockReturnValueOnce(false);

      // Executing the method
      const result = await service.getLmsSettingsFromGid(groupId);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(Cloud6Service);
      expect(cloud6ServiceMock.getLmsEnterpriseSettings).toHaveBeenCalledWith({ group_id: groupId });
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(result).toEqual({ status: true, msg: 'success', data: lmsSettings });
    });

    it('should return no data found message when lms settings are empty', async () => {
      // Mocking the necessary dependencies
      const groupId = '2';
      const enterpriseSettings = {
        data: {},
      };
      // const lmsSettings = 'mocluserhelper_lmsSettingsCacheData';

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === Cloud6Service) {
          return Promise.resolve(cloud6ServiceMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserHelper') {
          return Promise.resolve(userHelperMock);
        }
        return null;
      });

      jest.spyOn(cloud6ServiceMock, 'getLmsEnterpriseSettings').mockResolvedValueOnce(enterpriseSettings);
      jest.spyOn(userHelperMock, 'lmsSettingsCacheData').mockResolvedValueOnce('mocluserhelper_lmsSettingsCacheData');
      Utility.isEmpty = jest.fn().mockReturnValueOnce(true);

      // Executing the method
      const result = await service.getLmsSettingsFromGid(groupId);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(Cloud6Service);
      expect(cloud6ServiceMock.getLmsEnterpriseSettings).toHaveBeenCalledWith({ group_id: groupId });
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(result).toEqual({ status: false, msg: undefined, data: '' });
    });
  });

  describe('getTaxonomy', () => {
    it('should return taxonomy data when available', async () => {
      // Mock data for testing
      const testData = {
        category: 'your_category', // Add your test data
        name: 'your_name', // Add your test data
      };

      // Mock the return value of taxonomyRepo.aggregate
      const expectedResult = { data: [{ your_field: 'your_value' }], status: 'success' };
      taxonomyRepositoryMock.aggregate = jest.fn().mockResolvedValue(expectedResult);

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === TaxonomyRepository) {
          return Promise.resolve(taxonomyRepositoryMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.getTaxonomy(testData);

      // Assertions
      expect(taxonomyRepositoryMock.aggregate).toHaveBeenCalledWith([
        { $match: { category: { $in: ['your_category'] }, name: 'your_name' } },
        { $group: { _id: '$category', data: { $push: '$$ROOT' } } },
        { $group: { _id: null, root: { $push: { k: '$_id', v: '$data' } } } },
        { $replaceRoot: { newRoot: { $arrayToObject: '$root' } } },
      ]);

      expect(mockLogger.log).not.toHaveBeenCalled(); // Ensure that Logger.log is not called in case of success
      expect(result).toEqual({
        data: {},
        status: 'success',
      });
    });
    it('should return an error message when an error occurs', async () => {
      // Mock data for testing
      const testData = {
        category: 'your_category', // Add your test data
        name: 'your_name', // Add your test data
      };

      // Mock the rejection of taxonomyRepo.aggregate with an error
      const expectedError = new Error('Test error message'); // Replace this with your specific error message
      taxonomyRepositoryMock.aggregate = jest.fn().mockRejectedValue(expectedError);

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === TaxonomyRepository) {
          return Promise.resolve(taxonomyRepositoryMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.getTaxonomy(testData);

      // Assertions
      expect(taxonomyRepositoryMock.aggregate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            $match: {
              category: {
                $in: ['your_category'],
              },
              name: 'your_name',
            },
          }),
          // Add other expectations for each aggregation stage as needed
        ]),
      );

      expect(result).toEqual({ type: 'error', msg: 'Error occurred while updating profile. Please try again.' });
    });
  });

  describe('updateUserTimezone', () => {
    it('should update user timezone successfully', async () => {
      // Mock data for testing
      const requestBody: UpdateUserTimezoneDto = {
        uid: '112233',
        country: 'US',
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return Promise.resolve(userMgmtUtilityHelperMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.updateUserTimezone(requestBody);

      // Assertions
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserMgmtUtilityHelper');
      expect(userMgmtUtilityHelperMock.updateUserTimezone).toHaveBeenCalledWith(requestBody);
      expect(result).toEqual({
        /* expected result after successful update */
      });
    });

    it('should handle errors and log them', async () => {
      // Mock data for testing
      const requestBody: UpdateUserTimezoneDto = {
        uid: '',
        country: '',
      };

      // Mock error scenario
      const errorMessage = 'An error occurred.';
      jest.spyOn(helperServiceMock, 'getHelper').mockRejectedValueOnce(new Error(errorMessage));

      // Mock Logger.error to capture the log
      jest.spyOn(mockLogger, 'error').mockImplementation((message, context) => {
        // Perform assertions on the logged message and context
        expect(message).toEqual('updateUserTimezone');
        expect(context).toEqual({
          METHOD: expect.any(String),
          MESSAGE: errorMessage,
          REQUEST: requestBody,
          RESPONSE: expect.any(String),
        });
      });

      // Execute the method and expect it to throw an error
      await expect(service.updateUserTimezone(requestBody)).rejects.toThrowError(errorMessage);

      // Assertions
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserMgmtUtilityHelper');
    });
  });

  describe('getOriginalToken', () => {
    it('should generate original token successfully', async () => {
      // Mock data for testing
      const testData = {
        redirect_url: 'https://example.com/callback',
        token: 'mockAuthToken',
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.getOriginalToken(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(AuthTokenHelper);
      expect(authTokenHelperMock.encodeOriginalToken).toHaveBeenCalledWith('mockAuthToken');
      expect(result).toEqual({
        type: 'success',
        msg: 'Original Token generated',
        _t: 'mockEncodedToken',
      });
    });

    it('should return error for empty token and skip helper call', async () => {
      const helperServiceMock = {
        get: jest.fn(), // THIS is essential
      };
      const testData = {
        redirect_url: 'https://example.com/callback',
        token: '', // Empty token
      };
  
      const result = await service.getOriginalToken(testData);
  
      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid Token sent.',
      });
  
      expect(helperServiceMock.get).not.toHaveBeenCalled();
    });
    it('should catch exception during encoding and return fallback error response', async () => {
      const testData = {
        redirect_url: 'https://example.com/callback',
        token: 'mockAuthToken',
      };
    
      const errorMessage = 'Encoding failed.';
    
      // Mock the global Logger.error
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(() => {});
    
      // Mock failure in encodeOriginalToken
      authTokenHelperMock.encodeOriginalToken = jest.fn().mockRejectedValue(new Error(errorMessage));
      jest.spyOn(helperServiceMock, 'get').mockResolvedValue(authTokenHelperMock);
    
      const result = await service.getOriginalToken(testData);
    
      expect(helperServiceMock.get).toHaveBeenCalledWith(AuthTokenHelper);
      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    
      expect(loggerSpy).toHaveBeenCalledWith('getOriginalToken', {
        METHOD: expect.any(String),
        MESSAGE: errorMessage,
        REQUEST: testData,
        RESPONSE: expect.any(String),
      });
    
      loggerSpy.mockRestore(); // optional cleanup
    });
    

  });

  describe('updateLinkedinStatus', () => {
    it('should update LinkedIn status successfully', async () => {
      // Mock data for testing
      const testData = {
        source: 'linkedin',
        user_id: '123456',
      };

      // Mock UserRepository and SocialLoginHelper
      const userMock = { uid: '123456', name: 'John Doe' };
      const userRepositoryMock = {
        findByUID: jest.fn().mockResolvedValue(userMock),
      };

      const socialLoginHelperMock = {
        updateUserSocialAccountDetails: jest.fn().mockResolvedValue({ type: 'success' }),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'SocialLoginHelper') {
          return Promise.resolve(socialLoginHelperMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.updateLinkedinStatus(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('SocialLoginHelper');
      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('123456');
      expect(socialLoginHelperMock.updateUserSocialAccountDetails).toHaveBeenCalledWith(userMock, 'linkedin');
      expect(result).toEqual({
        type: 'success',
        msg: 'Details stored successfully',
      });
    });

    it('should handle errors and return an error message when updating LinkedIn status fails', async () => {
      // Mock data for testing
      const testData = {
        source: 'linkedin',
        user_id: '123456',
      };

      // Mock UserRepository and SocialLoginHelper
      const userMock = { uid: '123456', name: 'John Doe' };
      const userRepositoryMock = {
        findByUID: jest.fn().mockResolvedValue(userMock),
      };

      const socialLoginHelperMock = {
        updateUserSocialAccountDetails: jest.fn().mockResolvedValue({ type: 'error' }),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'SocialLoginHelper') {
          return Promise.resolve(socialLoginHelperMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.updateLinkedinStatus(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('SocialLoginHelper');
      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('123456');
      expect(socialLoginHelperMock.updateUserSocialAccountDetails).toHaveBeenCalledWith(userMock, 'linkedin');
      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred. Please try again.',
      });
    });

    it('should handle errors when user is not found', async () => {
      // Mock data for testing
      const testData = {
        source: 'linkedin',
        user_id: '123456',
      };

      // Mock UserRepository to simulate user not found
      const userRepositoryMock = {
        findByUID: jest.fn().mockResolvedValue(null),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.updateLinkedinStatus(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('123456');
      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred. Please try again.',
      });
    });
  });

  describe('revokeUserRole', () => {
    it('should revoke user role successfully', async () => {
      // Mock data for testing
      const testData = {
        uid: '123456',
        rid: 'role123',
      };

      // Mock UserRepository
      const userMock = {
        uid: '123456',
        roles: [
          { rid: 'role123', name: 'RoleName' },
          { rid: '2', name: 'AnotherRole' },
        ],
      };

      (configServiceMock.get as jest.Mock).mockReturnValueOnce({ '2': 'authenticated user' });

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(userRepositoryMock, 'findByUID').mockResolvedValueOnce(userMock);
      jest.spyOn(userRepositoryMock, 'findOneAndUpdate').mockResolvedValueOnce(userMock);

      // Execute the method
      const result = await service.revokeUserRole(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('123456');
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { uid: '123456' },
        { roles: [{ rid: '2', name: 'AnotherRole' }] },
      );
      expect(result).toEqual({
        roles: { '2': undefined },
      });
    });

    it('should handle errors and log them when revoking user role fails', async () => {
      // Mock data for testing
      const testData = {
        uid: '123456',
        rid: 'role123',
      };

      // Mock UserRepository to simulate user not found
      const userRepositoryMock = {
        findByUID: jest.fn().mockResolvedValue(null),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      // Mock Logger.error to capture the log
      jest.spyOn(mockLogger, 'error').mockImplementation((message, context) => {
        // Perform assertions on the logged message and context
        expect(message).toEqual('revokeUserRole');
        expect(context).toEqual({
          METHOD: expect.any(String),
          MESSAGE: expect.any(String),
          REQUEST: testData,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        });
      });

      // Execute the method and expect it to throw an error
      await expect(service.revokeUserRole(testData)).rejects.toThrowError(expect.any(Error));

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('123456');
      // expect(mockLogger.error).toHaveBeenCalledWith('revokeUserRole', {
      //   METHOD: expect.any(String),
      //   MESSAGE: expect.any(String),
      //   REQUEST: testData,
      //   RESPONSE: expect.any(String),
      //   TIMESTAMP: expect.any(Number),
      // });
    });
  });

  describe('getUserRoles', () => {
    it('should get user roles successfully', async () => {
      // Mock data for testing
      const testData = {
        pageSize: 10,
        page: 1,
      };

      // Mock RoleRepository
      const rolesMock = [
        { rid: 'role1', name: 'Role 1' },
        { rid: 'role2', name: 'Role 2' },
      ];
      const roleRepositoryMock = {
        getRoles: jest.fn().mockResolvedValue(rolesMock),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === RoleRepository) {
          return Promise.resolve(roleRepositoryMock);
        }
        return null;
      });

      // Execute the method
      const result = await service.getUserRoles(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(RoleRepository);
      expect(roleRepositoryMock.getRoles).toHaveBeenCalledWith({
        pageSize: 10,
        skipCount: 0,
      });
      expect(result).toEqual(rolesMock);
    });

    // it('should handle errors and log them when getting user roles fails', async () => {
    //   // Mock data for testing
    //   const testData = {
    //     pageSize: 10,
    //     page: 1,
    //   };

    //   // Mock RoleRepository to simulate an error
    //   const roleRepositoryMock = {
    //     getRoles: jest.fn().mockRejectedValue(new Error('An error occurred')),
    //   };

    //   jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
    //     if (helperName === RoleRepository) {
    //       return Promise.resolve(roleRepositoryMock);
    //     }
    //     return null;
    //   });

    //   // Mock Logger.log to capture the log
    //   jest.spyOn(mockLogger, 'log').mockImplementation((message, context) => {
    //     // Perform assertions on the logged message and context
    //     expect(message).toEqual('get User Roles');
    //     expect(context).toEqual({
    //       METHOD: expect.any(String),
    //       MESSAGE: expect.any(String),
    //       REQUEST: testData,
    //       RESPONSE: expect.any(String),
    //       TIMESTAMP: expect.any(Number),
    //     });
    //   });

    //   // Execute the method and expect it to throw an error
    //   await expect(service.getUserRoles(testData)).rejects.toThrowError(expect.any(Error));

    //   // Assertions
    //   expect(helperServiceMock.get).toHaveBeenCalledWith(RoleRepository);
    //   expect(roleRepositoryMock.getRoles).toHaveBeenCalledWith({
    //     pageSize: 10,
    //     skipCount: 0,
    //   });
    //   expect(mockLogger.log).toHaveBeenCalledWith('get User Roles', {
    //     METHOD: expect.any(String),
    //     MESSAGE: expect.any(String),
    //     REQUEST: testData,
    //     RESPONSE: expect.any(String),
    //     TIMESTAMP: expect.any(Number),
    //   });
    // });
  });

  describe('checkSignupValidation', () => {
    it('should validate signup successfully when user is not found and client request is valid', async () => {
      // Mock data for testing
      const testData: CheckSignupValidationDto = {
        client_id: 'your_client_id',
        user_email: '<EMAIL>',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        from_mobile: '',
        device_type: '',
        user_name: '',
        user_pwd: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      // Mock UserRepository
      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue(null),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(Utility, 'validateClientRequest').mockImplementationOnce(() => {
        return true;
      });

      // Execute the method
      const result = await service.checkSignupValidation(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      // expect(Utility.validateClientRequest).toHaveBeenCalledWith('your_client_id', 'your_client_secret');
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        type: 'success',
        msg: 'Email available for account creation.',
      });
    });

    it('should validate signup successfully when user is found but disabled', async () => {
      // Mock data for testing
      const testData: CheckSignupValidationDto = {
        client_id: 'your_client_id',
        user_email: '<EMAIL>',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        from_mobile: '',
        device_type: '',
        user_name: '',
        user_pwd: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      // Mock UserRepository
      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(Utility, 'validateClientRequest').mockImplementationOnce(() => {
        return true;
      });

      // Execute the method
      const result = await service.checkSignupValidation(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      // expect(Utility.validateClientRequest).toHaveBeenCalledWith('your_client_id', 'your_client_secret');
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        type: 'success',
        msg: 'Email available for account creation.',
      });

      // Ensure the branch related to this.configService.get('userDisabled') is covered
      // expect(configServiceMock.get).toHaveBeenCalledWith('userDisabled');
    });

    it('should handle validation errors and log them when client request is invalid', async () => {
      // Mock data for testing
      const testData: CheckSignupValidationDto = {
        client_id: 'invalid_client_id',
        user_email: '<EMAIL>',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        from_mobile: '',
        device_type: '',
        user_name: '',
        user_pwd: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      // Mock Logger.error to capture the log
      jest.spyOn(mockLogger, 'error').mockImplementation((message, context) => {
        // Perform assertions on the logged message and context
        expect(message).toEqual('checkSignupValidation');
        expect(context).toEqual({
          METHOD: expect.any(String),
          MESSAGE: expect.any(String),
          REQUEST: testData,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        });
      });

      // Execute the method and expect it to throw an error
      await expect(service.checkSignupValidation(testData)).rejects.toThrowError(expect.any(Error));

      // Assertions
      // expect(Utility.validateClientRequest).toHaveBeenCalledWith('invalid_client_id', 'your_client_secret');
      // expect(mockLogger.error).toHaveBeenCalledWith('checkSignupValidation', {
      //   METHOD: expect.any(String),
      //   MESSAGE: expect.any(String),
      //   REQUEST: testData,
      //   RESPONSE: expect.any(String),
      //   TIMESTAMP: expect.any(Number),
      // });
    });

    it('should handle validation errors and log them when user is found and active', async () => {
      // Mock data for testing
      const testData: CheckSignupValidationDto = {
        client_id: 'your_client_id',
        user_email: '<EMAIL>',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        from_mobile: '',
        device_type: '',
        user_name: '',
        user_pwd: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      // Mock UserRepository
      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue({ status: 1 }), // User is active
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(Utility, 'validateClientRequest').mockImplementationOnce(() => {
        return true;
      });

      // Execute the method
      const result = await service.checkSignupValidation(testData);

      // Assertions
      expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
      // expect(Utility.validateClientRequest).toHaveBeenCalledWith('your_client_id', 'your_client_secret');
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        type: 'failed',
        msg: undefined,
      });
    });

    // it('should handle validation errors and log them when user is found but disabled', async () => {
    //   // Mock data for testing
    //   const testData: CheckSignupValidationDto = {
    //     client_id: 'your_client_id',
    //     user_email: '<EMAIL>',
    //     time_zone: '',
    //     affiliateId: 0,
    //     redirect_url: '',
    //     from_mobile: '',
    //     device_type: '',
    //     user_name: '',
    //     user_pwd: '',
    //     register_mode: '',
    //     country_id: 0,
    //     country_code: '',
    //     city_id: 0,
    //     auto_login: '',
    //     user_roles: [],
    //     user_type: '',
    //     email_agreement: ''
    //   };

    //   // Mock UserRepository
    //   const userRepositoryMock = {
    //     getUserByEmail: jest.fn().mockResolvedValue({ status: 0 }), // User is disabled
    //   };

    //   jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
    //     if (helperName === UserRepository) {
    //       return Promise.resolve(userRepositoryMock);
    //     }
    //     return null;
    //   });

    //   jest.spyOn(Utility, 'validateClientRequest').mockImplementationOnce(() => {
    //     return true
    //   });

    //   // Execute the method
    //   const result = await service.checkSignupValidation(testData);

    //   // Assertions
    //   expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
    //   // expect(Utility.validateClientRequest).toHaveBeenCalledWith('your_client_id', 'your_client_secret');
    //   expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
    //   expect(result).toEqual({
    //     type: 'failed',
    //     msg: 'User account is disabled.',
    //   });
    // });

    // it('should handle errors and log them when an unexpected error occurs', async () => {
    //   // Mock data for testing
    //   const testData: CheckSignupValidationDto = {
    //     client_id: 'your_client_id',
    //     user_email: '<EMAIL>',
    //     time_zone: '',
    //     affiliateId: 0,
    //     redirect_url: '',
    //     from_mobile: '',
    //     device_type: '',
    //     user_name: '',
    //     user_pwd: '',
    //     register_mode: '',
    //     country_id: 0,
    //     country_code: '',
    //     city_id: 0,
    //     auto_login: '',
    //     user_roles: [],
    //     user_type: '',
    //     email_agreement: ''
    //   };

    //   // Mock UserRepository to simulate an unexpected error
    //   const userRepositoryMock = {
    //     getUserByEmail: jest.fn().mockRejectedValue(new Error('Unexpected error')),
    //   };

    //   jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
    //     if (helperName === UserRepository) {
    //       return Promise.resolve(userRepositoryMock);
    //     }
    //     return null;
    //   });

    //   jest.spyOn(Utility, 'validateClientRequest').mockImplementationOnce(() => {
    //     return true
    //   });

    //   // Mock Logger.error to capture the log
    //   jest.spyOn(mockLogger, 'error').mockImplementation((message, context) => {
    //     // Perform assertions on the logged message and context
    //     expect(message).toEqual('checkSignupValidation');
    //     expect(context).toEqual({
    //       METHOD: expect.any(String),
    //       MESSAGE: 'Unexpected error',
    //       REQUEST: testData,
    //       RESPONSE: expect.any(String),
    //       TIMESTAMP: expect.any(Number),
    //     });
    //   });

    //   // Execute the method and expect it to throw an error
    //   await expect(service.checkSignupValidation(testData)).rejects.toThrowError(expect.any(Error));

    //   // Assertions
    //   expect(helperServiceMock.get).toHaveBeenCalledWith(UserRepository);
    //   // expect(Utility.validateClientRequest).toHaveBeenCalledWith('your_client_id', 'your_client_secret');
    //   expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
    //   expect(mockLogger.error).toHaveBeenCalledWith('checkSignupValidation', {
    //     METHOD: expect.any(String),
    //     MESSAGE: 'Unexpected error',
    //     REQUEST: testData,
    //     RESPONSE: expect.any(String),
    //     TIMESTAMP: expect.any(Number),
    //   });
    // });
  });

  

  describe('getTermsListByTaxonomyCategory', () => {
  const taxonomyMock = {
    find: jest.fn(),
  };
    afterEach(() => {
      jest.clearAllMocks();
    });
    
    it('should return formatted taxonomy terms for a valid category', async () => {
      const mockTaxonomies = [
        { _id: '1', name: 'Technology' },
        { _id: '2', name: 'Finance' },
      ];
      taxonomyMock.find.mockResolvedValue(mockTaxonomies);
      const result = await service.getTermsListByTaxonomyCategory('industry');
      expect(result).toEqual({
        '1': 'Technology',
        '2': 'Finance',
      });
      expect(taxonomyMock.find).toHaveBeenCalledWith({ category: 'industry' });
    });
    it('should throw BadRequestException if taxonomy is not provided', async () => {
      await expect(service.getTermsListByTaxonomyCategory('')).rejects.toThrow(BadRequestException);
    });

    it('should return an empty object if no taxonomies are found', async () => {
      taxonomyRepositoryMock.find.mockResolvedValueOnce([]);
      
      const result = await service.getTermsListByTaxonomyCategory('industry');
  
      expect(result).toEqual({});
    });

  })

  // describe('validateUserDetails', () => {
  //   it('should return success for valid social login', async () => {
  //     const data = {
  //       client_id: 'sl_looper',
  //       authToken: 'your_auth_token',
  //       method: 'facebook', // Assuming 'facebook' is a valid social login platform
  //       email: '<EMAIL>',
  //     };

  //     jest.spyOn(configServiceMock, 'get').mockReturnValueOnce({
  //       clientSecret: {
  //         'sl_looper': 'your_client_secret',
  //       },
  //       socialAuth: {
  //         allowedSocialLoginPlatforms: ['facebook', 'google'], // Add other platforms as needed
  //       },
  //     });

  //     const socialHelperMock = {
  //       processSocialAuthResponse: jest.fn().mockResolvedValue({
  //         status: true,
  //         msg: 'Social login successful',
  //       }),
  //     };

  //     jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
  //       if (helperName === 'SocialLoginHelper') {
  //         return Promise.resolve(SocialLoginHelperMock)
  //       }
  //       return null;
  //     });
  //     jest.spyOn(SocialLoginHelperMock, 'processSocialAuthResponse').mockResolvedValueOnce(socialHelperMock);

  //     const result = await service.validateUserDetails(data);

  //     expect(configServiceMock.get).toHaveBeenCalledWith('clientSecret');
  //     expect(configServiceMock.get).toHaveBeenCalledWith('socialAuth');
  //     expect(helperServiceMock.getHelper).toHaveBeenCalledWith('SocialLoginHelper');
  //     expect(socialHelperMock.processSocialAuthResponse).toHaveBeenCalledWith({
  //       ...data,
  //       token: data.authToken,
  //     });
  //     expect(result).toEqual({ type: 'success', msg: 'Social login successful' });
  //   });

  //   it('should return failed for invalid social login method', async () => {
  //     const data = {
  //       client_id: 'sl_looper',
  //       authToken: 'your_auth_token',
  //       method: 'invalid_social_platform', // Assuming 'invalid_social_platform' is not allowed
  //       email: '<EMAIL>',
  //     };

  //     jest.spyOn(configServiceMock, 'get').mockReturnValueOnce({
  //       clientSecret: {
  //         'sl_looper': 'your_client_secret',
  //       },
  //       socialAuth: {
  //         allowedSocialLoginPlatforms: ['facebook', 'google'], // Add other platforms as needed
  //       },
  //     });

  //     const result = await service.validateUserDetails(data);

  //     expect(configServiceMock.get).toHaveBeenCalledWith('clientSecret');
  //     expect(configServiceMock.get).toHaveBeenCalledWith('socialAuth');
  //     expect(helperServiceMock.getHelper).not.toHaveBeenCalled();
  //     expect(result).toBeUndefined(); // Since the method is not allowed, it should return undefined
  //   });

  //   it('should return error for existing user', async () => {
  //     const data = {
  //       client_id: 'sl_looper',
  //       authToken: 'your_auth_token',
  //       method: 'local', // Assuming 'local' is not a social login method
  //       email: '<EMAIL>',
  //     };

  //     jest.spyOn(configServiceMock, 'get').mockReturnValueOnce({
  //       clientSecret: {
  //         'sl_looper': 'your_client_secret',
  //       },
  //       socialAuth: {
  //         allowedSocialLoginPlatforms: ['facebook', 'google'], // Add other platforms as needed
  //       },
  //     });

  //     jest.spyOn(userRepositoryMock, 'getUserByEmail').mockResolvedValue({ status: 1 }); // Assuming user exists

  //     const result = await service.validateUserDetails(data);

  //     expect(configServiceMock.get).toHaveBeenCalledWith('clientSecret');
  //     expect(configServiceMock.get).toHaveBeenCalledWith('socialAuth');
  //     expect(helperServiceMock.getHelper).not.toHaveBeenCalled();
  //     expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
  //     expect(result).toEqual({ type: 'error', msg: 'User already exist.' });
  //   });
  // });

  describe('authenticate', () => {
    let authHelperMock: any;

    beforeEach(() => {
      authHelperMock = {
        authenticateUser: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') {
          return Promise.resolve(authHelperMock);
        }
        return null;
      });

      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);
    });

    it('should authenticate user successfully and return a success response', async () => {
      const data = {
        user_login: '<EMAIL>',
        user_pwd: 'password123',
        client_id: 'sl_looper',
        redirect_url: 'https://example.com',
        from_mobile: '1',
        device_type: 'android',
      };

      const userMock = { id: 1, email: '<EMAIL>' };
      const userTokenDetailMock : any = { _t: 'mockToken', lmsUrl: 'https://lms.example.com' };

      authHelperMock.authenticateUser.mockResolvedValue(userMock);
      jest.spyOn(service, 'getUserToken').mockResolvedValue(userTokenDetailMock);

      const result = await service.authenticate(data);

      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('AuthHelper');
      expect(Utility.validateClientRequest).toHaveBeenCalledWith(
        data.client_id,
        undefined,
        data.redirect_url,
      );
      expect(authHelperMock.authenticateUser).toHaveBeenCalledWith({
        email: data.user_login,
        password: data.user_pwd,
      });
      expect(service.getUserToken).toHaveBeenCalledWith(userMock, {
        response_type: 'redirect',
        from_mobile_flag: 1,
        app_type: 'android',
        redirect_url: data.redirect_url,
        lrs_object_type: expect.anything(),
        client_id: data.client_id,
      });
      expect(result).toEqual({
        type: 'success',
        _t: 'mockToken',
        lmsUrl: 'https://lms.example.com',
      });
    });

    it('should return error response if user authentication fails', async () => {
      const data = {
        user_login: '<EMAIL>',
        user_pwd: 'wrongpassword',
        client_id: 'sl_looper',
      };

      authHelperMock.authenticateUser.mockResolvedValue(null);

      const result = await service.authenticate(data);

      expect(authHelperMock.authenticateUser).toHaveBeenCalledWith({
        email: data.user_login,
        password: data.user_pwd,
      });
      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should handle UserNotFoundException and return appropriate error message', async () => {
      const data = {
        user_login: '<EMAIL>',
        user_pwd: 'password123',
        client_id: 'sl_looper',
      };

      const error = new BadRequestException('UserNotFoundException');
      authHelperMock.authenticateUser.mockRejectedValue(error);

      const result = await service.authenticate(data);

      expect(result).toEqual({
        type: 'error',
        msg: 'No active account associated with this email address',
      });
    });

    it('should handle InvalidCredentials exception and return appropriate error message', async () => {
      const data = {
        user_login: '<EMAIL>',
        user_pwd: 'wrongpassword',
        client_id: 'sl_looper',
      };

      const error = new BadRequestException('InvalidCredentials');
      authHelperMock.authenticateUser.mockRejectedValue(error);

      const result = await service.authenticate(data);

      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid user credentials.',
      });
    });

    it('should handle UnauthorizedException and return appropriate error message', async () => {
      const data = {
        user_login: '<EMAIL>',
        user_pwd: 'password123',
        client_id: 'sl_looper',
      };

      const error = new UnauthorizedException('Unauthorized access');
      authHelperMock.authenticateUser.mockRejectedValue(error);

      const result = await service.authenticate(data);

      expect(result).toEqual({
        type: 'error',
        msg: 'Unauthorized access',
      });
    });

    it('should handle unexpected errors and return a generic error message', async () => {
      const data = {
        user_login: '<EMAIL>',
        user_pwd: 'password123',
        client_id: 'sl_looper',
      };

      const error = new Error('Unexpected error');
      authHelperMock.authenticateUser.mockRejectedValue(error);

      const result = await service.authenticate(data);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });
  });

  describe('authenticateSaml', () => {
    let authTokenHelperMock: any;
    beforeEach(() => {
      authTokenHelperMock = {
        learnerAuthenticationRehash: jest.fn(),
        teamLearnerAuthenticationRehash: jest.fn(),
      };

      configServiceMock = {
        get: jest.fn((key: string) => {
          if (key === 'clientSecret') return 'mockSecret';
          if (key === 'drupal_hash_salt') return 'mockSalt';
          return undefined;
        }),
      } as unknown as ConfigService;

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === AuthTokenHelper) {
          return Promise.resolve(authTokenHelperMock);
        }
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
        return null;
      });

      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);
    });

    it('should authenticate SAML user successfully and return a success response', async () => {
      const reqData = {
        client_id: 'sl_looper',
        user_login: '<EMAIL>',
        redirect_url: 'https://example.com',
        additional_info: {
          request_type: '1',
          affiliate_id: '123',
          timestamp: '1234567890',
          hash_token: 'validHashToken',
        },
      };


      const userMock = { id: 1, email: '<EMAIL>', status: 1 };
      const tokenResponseMock = { type: 'success' ,_t: 'mockToken', msg: 'user logged in successfully', lmsUrl: 'https://lms.example.com' };

      jest.spyOn(userRepositoryMock, 'getUserByEmail').mockResolvedValue(userMock);
      jest.spyOn(authTokenHelperMock, 'learnerAuthenticationRehash').mockReturnValue('validHashToken');
      jest.spyOn(service, 'getUserToken').mockResolvedValue(tokenResponseMock);

      const result = await service.authenticateSaml(reqData);
      expect(Utility.validateClientRequest).toHaveBeenCalledWith(
        reqData.client_id,
        expect.anything(),
        reqData.redirect_url,
      );
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(reqData.user_login);
      expect(authTokenHelperMock.learnerAuthenticationRehash).toHaveBeenCalledWith(
        '123',
        '1234567890',
        reqData.user_login,
        expect.anything(),
      );
      expect(service.getUserToken).toHaveBeenCalledWith(userMock, {
        response_type: 'redirect',
        from_mobile_flag: 0,
        app_type: undefined,
        redirect_url: reqData.redirect_url,
        lrs_object_type: expect.anything(),
        client_id: reqData.client_id,
      });
      expect(result).toEqual({
        type: 'success',
        _t: 'mockToken',
        lmsUrl: 'https://lms.example.com',
      });
    });

    it('should return error if hash token validation fails', async () => {
      const reqData = {
        client_id: 'sl_looper',
        user_login: '<EMAIL>',
        redirect_url: 'https://example.com',
        additional_info: JSON.stringify({
          request_type: 'learner',
          affiliate_id: '123',
          timestamp: '1234567890',
          hash_token: 'invalidHashToken',
        }),
      };

      const userMock = { id: 1, email: '<EMAIL>', status: 1 };

      jest.spyOn(userRepositoryMock, 'getUserByEmail').mockResolvedValue(userMock);
      jest.spyOn(authTokenHelperMock, 'learnerAuthenticationRehash').mockReturnValue('validHashToken');

      const result = await service.authenticateSaml(reqData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should return error if user is not found', async () => {
      const reqData = {
        client_id: 'sl_looper',
        user_login: '<EMAIL>',
        redirect_url: 'https://example.com',
        additional_info: JSON.stringify({
          request_type: 'learner',
          affiliate_id: '123',
          timestamp: '1234567890',
          hash_token: 'validHashToken',
        }),
      };

      jest.spyOn(userRepositoryMock, 'getUserByEmail').mockResolvedValue(null);

      const result = await service.authenticateSaml(reqData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should handle UnauthorizedException and return appropriate error message', async () => {
      const reqData = {
        client_id: 'sl_looper',
        user_login: '<EMAIL>',
        redirect_url: 'https://example.com',
      };

      const error = new UnauthorizedException('Unauthorized access');
      jest.spyOn(userRepositoryMock, 'getUserByEmail').mockRejectedValue(error);

      const result = await service.authenticateSaml(reqData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Unauthorized access',
      });
    });

    it('should handle unexpected errors and return a generic error message', async () => {
      const reqData = {
        client_id: 'sl_looper',
        user_login: '<EMAIL>',
        redirect_url: 'https://example.com',
      };

      const error = new Error('Unexpected error');
      jest.spyOn(userRepositoryMock, 'getUserByEmail').mockRejectedValue(error);

      const result = await service.authenticateSaml(reqData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });
  });

  describe('register', () => {
    let userRepositoryMock: any;
    let tokenServiceMock: any;
    let userServiceMock: any;
    let authHelperMock: any;
    let lrsHelperMock: any;
    let userMgmtUtilityHelperMock: any;
    let cookieHelperMock: any;
    let userHelperMock: any;

    beforeEach(() => {
      userRepositoryMock = {
        getUserByEmail: jest.fn(),
      };
      tokenServiceMock = {
        generateSessionTokens: jest.fn(),
      };
      userServiceMock = {
        userRegistration: jest.fn(),
      };
      authHelperMock = {
        handleUserSignup: jest.fn(),
        getSignupRedirect: jest.fn(),
      };
      lrsHelperMock = {
        sendDataToLrs: jest.fn(),
      };
      userMgmtUtilityHelperMock = {
        getTimezoneFromCountryCode: jest.fn(),
      };
      cookieHelperMock = {
        setBulkCookie: jest.fn(),
      };
      userHelperMock = {
        updateUserLoginTime: jest.fn(),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) return Promise.resolve(userRepositoryMock);
        if (helperName === AuthTokenHelper) return Promise.resolve(tokenServiceMock);
        if (helperName === UserService) return Promise.resolve(userServiceMock);
        if (helperName === 'lrsHelper') return Promise.resolve(lrsHelperMock);
        if (helperName === 'UserMgmtUtilityHelper') return Promise.resolve(userMgmtUtilityHelperMock);
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((helperName: any) => {
        if (helperName === 'AuthHelper') return Promise.resolve(authHelperMock);
        if (helperName === 'CookieHelper') return Promise.resolve(cookieHelperMock);
        if (helperName === 'UserHelper') return Promise.resolve(userHelperMock);
        return null;
      });

      jest.spyOn(configServiceMock, 'get').mockImplementation((key: string) => {
        if (key === 'userOptionsAccountSetupComplete') return true;
        if (key === 'defaultFreemiumSignupUtm') return 'defaultUtm';
        if (key === 'clientKey') return 'mockClientKey';
        return undefined;
      });
    });

    it('should register a new user successfully', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'password123',
        user_name: 'Test User',
        country_code: 'US',
        from_mobile: '1',
        user_roles: 'role1,role2',
      };
      const cookieData = {};
      const res : any = { cookie: jest.fn() };

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userMgmtUtilityHelperMock.getTimezoneFromCountryCode.mockResolvedValue('America/New_York');
      userServiceMock.userRegistration.mockResolvedValue({ uid: '12345' });
      tokenServiceMock.generateSessionTokens.mockResolvedValue({ idToken: 'mockIdToken', userData: {} });
      authHelperMock.getSignupRedirect.mockResolvedValue({ cookieValue: [] });

      const result = await service.register(signupDetail, cookieData, res);

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(userMgmtUtilityHelperMock.getTimezoneFromCountryCode).toHaveBeenCalledWith('US');
      expect(userServiceMock.userRegistration).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
          password: 'password123',
          display_name: 'Test User',
        }),
        ['role1', 'role2'],
      );
      expect(tokenServiceMock.generateSessionTokens).toHaveBeenCalledWith(
        { uid: '12345' },
        'mockClientKey',
      );
      expect(cookieHelperMock.setBulkCookie).toHaveBeenCalled();
      expect(result).toEqual({
        status: true,
        msg: 'Successfully created new user.',
        data: { idToken: 'mockIdToken', userData: {} },
      });
    });

    it('should return an error if the email already exists', async () => {
      const signupDetail = { user_email: '<EMAIL>' };
      const cookieData = {};
      const res: any = { cookie: jest.fn() };

      userRepositoryMock.getUserByEmail.mockResolvedValue({ status: 1 });

      const result = await service.register(signupDetail, cookieData, res);

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(result).toEqual({
        status: false,
        msg: 'Email address already exists, please choose a different one.',
      });
    });

    it('should return an error if phone number is required but not provided', async () => {
      const signupDetail = { user_email: '<EMAIL>', from_mobile: '0', register_mode: 'scorm' };
      const cookieData = {};
      const res : any = { cookie: jest.fn() };

      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({ status: true, msg:'',data: { phone_number: { value: 'yes' } } });

      const result = await service.register(signupDetail, cookieData, res);

      expect(result).toEqual({
        status: false,
        msg: 'Please provide valid phone no.',
      });
    });

    it('should handle errors and log them', async () => {
      const signupDetail = { user_email: '<EMAIL>' };
      const cookieData = {};
      const res : any = { cookie: jest.fn() };

      const error = new Error('Unexpected error');
      userRepositoryMock.getUserByEmail.mockRejectedValue(error);

      await expect(service.register(signupDetail, cookieData, res)).rejects.toThrowError('Unexpected error');
    });
  });

  describe('updateProfile', () => {
    let mockUserRepository: any;
    let mockUserHelper: any;
    let mockHelperService: any;
  
    beforeEach(() => {
      mockUserRepository = {
        findByUID: jest.fn(),
        findOneAndUpdate: jest.fn()
      };
  
      mockUserHelper = {
        updateCloud6SentinelByUidOrMail: jest.fn()
      };
  
      mockHelperService = {
        getHelper: jest.fn()
          .mockImplementation((type: any) => {
            if (type === 'UserHelper') return mockUserHelper;
            return mockUserRepository;
          })
      };

    });
  
    it('should update profile successfully and return success message', async () => {
      const mockUser = {
        uid: '123',
        email: '<EMAIL>',
        status: true
      };
  
      const mockParams = {
        first_name: 'John',
        last_name: 'Doe',
        display_name: 'John Doe',
      };
  
      const updatedUser = { display_name: 'John Doe' };
  
      mockUserRepository.findByUID.mockResolvedValue(mockUser);
      mockUserRepository.findOneAndUpdate.mockResolvedValue(updatedUser);
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
  
      const result = await service.updateProfile({
        uid: '123',
        first_name: 'John',
        last_name: 'Doe'
      });
  
      expect(result).toEqual({
        type: 'success',
        msg: 'Your profile has been successfully updated.'
      });
  
      expect(mockUserRepository.findByUID).toHaveBeenCalledWith('123');
      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        expect.objectContaining(mockParams)
      );
      expect(mockUserHelper.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith(
        expect.objectContaining({
          uid: '123',
          ...mockParams
        })
      );
    });
  });

  describe('saveProfileAndFormData', () => {
    let service: UserApiService;
  
    const mockHelperService = {
      get: jest.fn(),
      getHelper: jest.fn(),
    };
  
    const mockUserRepo = {
      findByUID: jest.fn(),
    };
  
    const mockUserApiV1Helper = {
      getLmsSetting: jest.fn(),
      validateEditProfileBasic: jest.fn(),
      validateEditProfileContact: jest.fn(),
      validateEditProfileOutcome: jest.fn(),
      validateEditProfileProfessional: jest.fn(),
      validateEditProfileAcademics: jest.fn(),
    };
  
    const mockProfileHelper = {
      saveProfileData: jest.fn(),
      getOneTaxonomy: jest.fn().mockReturnValue({ _id: '12345' }),
    };
  
    const mockLrsInstance = {
      sendDataToKafka: jest.fn(),
    };
  
    beforeEach(async () => {
      service = service
  
      mockHelperService.get.mockResolvedValue(mockUserRepo);
      mockHelperService.getHelper.mockImplementation(helper => {
        switch (helper) {
          case 'UserApiV1Helper': return mockUserApiV1Helper;
          case 'ProfileHelper': return mockProfileHelper;
          case 'lrsHelper': return mockLrsInstance;
        }
      });
  
      jest.spyOn(Logger, 'error').mockImplementation(() => {});
    });
  
    it('should return error if user not found', async () => {
      mockUserRepo.findByUID.mockResolvedValue(null);
  
      const result = await service.saveProfileAndFormData({ btnSignup: 'basic', userId: '999' }, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'UserNotFoundException',
      });
    });
  
    it('should return success for valid basic profile update', async () => {
      mockUserRepo.findByUID.mockResolvedValue({ uid: '123', user_groups: [1] });
      mockUserApiV1Helper.getLmsSetting.mockResolvedValue('yes');
      mockUserApiV1Helper.validateEditProfileBasic.mockResolvedValue(true);
      mockProfileHelper.saveProfileData.mockResolvedValue(true);
  
      const inputParams = {
        first_name: 'John',
        last_name: 'Doe',
        title: 'Mr',
        middle_name: '',
        training_funded_by: '',
      };
  
      const result = await service.saveProfileAndFormData({
        btnSignup: 'basic',
        userId: '123',
        userEmail: '<EMAIL>',
        isB2bStudent: false
      }, inputParams);
  
      expect(result).toEqual({
        type: 'success',
        msg: 'Your profile has been successfully updated.',
      });
      expect(mockLrsInstance.sendDataToKafka).toHaveBeenCalled();
    });
  
    it('should handle exception and return error', async () => {
      mockUserRepo.findByUID.mockImplementation(() => { throw new Error('DB down'); });
  
      const result = await service.saveProfileAndFormData({ btnSignup: 'basic', userId: '123' }, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid Request.',
      });
    });
  
    it('should return custom error message from BadRequestException', async () => {
      mockUserRepo.findByUID.mockResolvedValue({ uid: '123', user_groups: [1] });
      mockUserApiV1Helper.getLmsSetting.mockResolvedValue('yes');
      mockUserApiV1Helper.validateEditProfileBasic.mockImplementation(() => {
        throw new BadRequestException('Invalid first name');
      });
  
      const result = await service.saveProfileAndFormData({
        btnSignup: 'basic',
        userId: '123',
        userEmail: '<EMAIL>',
      }, {});
  
      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid first name',
      });
    });
  });
  
});
