import { Injectable, Inject, BadRequestException, Res, UnauthorizedException, InternalServerErrorException } from '@nestjs/common';
import { HelperService } from '../../helper/helper.service';
import { Logger } from '../../logging/logger';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { IRoleRepository, RoleRepository } from '../../user/repositories/role/role.repository';
import { ConfigService } from '@nestjs/config';
import { User } from '../../db/mongo/schema/user/user.schema';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { CheckSignupValidationDto } from '../dto/signup-validation.dto';
import { SignupDto } from '../dto/signup.dto';
import { LinkAccountsDto } from '../dto/link.accounts.dto';
import { CustomResponse, ResponseCookieType, ValidationResponse } from '../../common/typeDef/auth.type';
import { UserSocial } from '../../db/mongo/schema/user/social.schema';
import { UserApiLoginDto } from '../dto/v1-login.dto';
import * as drupalHash from 'drupal-hash';
import { AuthenticationRequestDto } from '../../auth/dtos/authentication-request.dto';
import { Utility } from './../../common/util/utility';
import { ITaxonomyRepository, TaxonomyRepository } from '../../user/repositories/taxonomy/taxonomy.repository';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { UpdateUserTimezoneDto } from '../dto/update-user-tmezone.dto';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { SocialLoginService } from '../../auth/services/social-login/social-login.service';
import { UserService } from '../../user/services/user.service';
import { AuthenticateSaml } from '../dto/authenticate-saml.dto';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { Category } from '../../db/mongo/schema/taxonomies/taxonomies.schema';
import { CachingService } from '../../caching/caching.service';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { RoleDto } from '../dto/role.dto';


@Injectable()
export class UserApiService {
  @Inject(HelperService) private readonly helperService: HelperService;
  @Inject(ConfigService) private readonly configService: ConfigService;
  

  #lrsObjectType = 'accounts_userapi_v1';

  async updateUserName(reqBody: { uid: number; name: string }) {
    try {
      const [userRepository, UserHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
      ]);
      const userResponse = await userRepository.findByUID(reqBody?.uid);
      if (!userResponse || userResponse.status !== 1) {
        throw new BadRequestException('User account does not exists.');
      }
      const [mongoUpdate] = await Promise.all([
        // updating user details in mongo
        userRepository.findOneAndUpdate({ uid: reqBody?.uid }, { display_name: reqBody?.name }),
        // updating user details in mysql
        UserHelper.updateCloud6SentinelByUidOrMail({ uid: reqBody?.uid,  display_name: reqBody?.name }),
      ]);
      if (this.configService.get('enableDrupalSync')) {
        await UserHelper.syncUserDataWithMySQLDrupal({
          uid: reqBody?.uid,
          display_name: reqBody?.name,
        });
      }

      return mongoUpdate;
    } catch (error: any) {
      Logger.error('updateUserName', {
        METHOD: this.constructor.name + '@' + this.updateUserName.name,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async login(data: UserApiLoginDto, res) {
    let response = {
      type: 'error',
      msg: 'Some error occurred while authenticating user.',
    };
    try {
      const redirectUrl = data?.redirect_url || '';
      Utility.validateClientRequest(data?.client_id, this.configService.get('clientSecret'), redirectUrl);
      const [userRepository, socialLoginHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('SocialLoginHelper'),
      ]);
      const socialAuth = this.configService.get('socialAuth');
      const user: Partial<User> = await userRepository.getUserByEmail(data?.email);
      if (!user || user === null || user.status !== 1) {
        throw new BadRequestException('UserNotFoundException');
      }
      const additionalInfo = {
        response_type: !Utility.isEmpty(data?.redirect_url) ? 'redirect' : 'json',
        from_mobile_flag: data?.from_mobile === '1' ? 1 : 0,
        app_type: data?.device_type,
        redirect_url: data?.redirect_url,
        lrs_object_type: this.#lrsObjectType,
        client_id: data?.client_id,
      };
      if (socialAuth?.allowedSocialLoginPlatforms.includes(data?.method)) {
        const socialParams = {
          email : data?.email,
          type: data?.method,
          token: data?.authToken,
          authRespType: 'token',
          referer: 'login',
          requestSource: 'mobile', 
        };
        const socialAuthResponse = await socialLoginHelper.processSocialAuthResponse(socialParams, user);
        if (!Utility.isEmpty(socialAuthResponse.setCookie) && socialAuthResponse.setCookie) {
          const cookieHelper = await this.helperService.getHelper('CookieHelper');
          await cookieHelper.setCookie(res, socialAuthResponse?.cookieName, socialAuthResponse?.cookieValue);
        }
        if (
          !Utility.isEmpty(socialAuthResponse?.returnResponse) &&
          socialAuthResponse?.returnResponse?.type === 'success'
        ) {
          const returnResponse = socialAuthResponse?.returnResponse;
          const socialAccountStatus = returnResponse?.socialAccountStatus || false;
          const userAccountStatus = returnResponse?.userAccountStatus || false; 
          const accountSetupStatus = returnResponse?.accountSetupStatus || false;
          
          response['userAccountStatus'] = userAccountStatus;
          response['socialAccountStatus'] = socialAccountStatus;
          response['accountSetupStatus'] = accountSetupStatus;
          
          if(!socialAccountStatus && !userAccountStatus) {
            response['msg'] = 'User does not exist.';
            response['refferer'] = socialParams.referer;
            response['errorCode'] = 101;
          } else if (!socialAccountStatus) {
            response['msg'] = 'Email is already exist with different signin method.';
            response['refferer'] = socialParams.referer;
            response['errorCode'] = 102;
          } else {
            const loginResult = await this.getUserToken(user, additionalInfo);
            if (loginResult.type && loginResult.type.toLowerCase() === 'success') {
              return loginResult;
            }
          }       
          return response;
        } else {
          throw new BadRequestException(socialAuthResponse?.returnResponse?.msg || response.msg);
        }
      } else {
        const isPassValid = await drupalHash.checkPassword(data?.password, user?.password);
        if (isPassValid) {
          const loginResult = await this.getUserToken(user, additionalInfo);
          if (loginResult.type && loginResult.type.toLowerCase() === 'success') {
            return loginResult;
          }
        } else {
          if (data?.from_mobile == '1') {
           const exeedLimit = await this.loginAttemptLimit(user?.uid);
           if(exeedLimit.type == 'notice') return exeedLimit;
          }
          throw new BadRequestException('InvalidCredentials');
        }
      }
   
      return { type: 'error', msg: 'Something went wrong.' };
    } catch (error: any) {
      Logger.error('login', {
        METHOD: this?.constructor?.name + '@' + this?.login?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error instanceof BadRequestException && error?.message === 'UserNotFoundException') {
        response['msg'] = 'No active account associated with this email address';
        response['errorCode'] = 101;
      } else if (error instanceof BadRequestException && error?.message === 'InvalidCredentials') {
        response['msg'] = 'Invalid user credentials.';
      } else if (error instanceof UnauthorizedException) {
        response['msg'] = error.message;
      } else {
        response['msg'] = 'Some error occurred while authenticating user.';
      }
      return response;
    }
  }

  /**
   * Handles the login attempt limit for a user by checking and updating the attempt count.
   * This method interacts with a helper service to enforce limits on login attempts
   * and applies configurations for reset count, wait time, and email cache time.
   *
   * @param userUid - The unique identifier of the user attempting to log in.
   * @returns A promise that resolves to an object containing a message (`msg`) and a type (`type`).
   * 
   * @throws Will throw an error if the helper service or configuration values fail to process.
   * 
   */
  async loginAttemptLimit(userUid: number): Promise<{ msg: string; type: string }> {
    try {
      const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
      return await userMgmtUtilityHelperInstance.checkAndUpdateAttempt(
        `${userUid}_login_reset`,
        `${userUid}_login_reset_timeout`,
        this.configService.get<number>('fpResetCount'),
        this.configService.get<number>('fpResetWaitTime'),
        this.configService.get<number>('fpEmailInCacheTime')
      );
    } catch (error: any) {
      Logger.error('loginAttemptLimit', {
        METHOD: this.constructor.name + '@' + this.loginAttemptLimit.name,
        MESSAGE: error.message,
        REQUEST: { userUid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async forgetPasswordAttemptLimit(userEmail: string): Promise<{ msg: string; type: string }> {
    try {
      const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
      return await userMgmtUtilityHelperInstance.checkAndUpdateAttempt(
        `${userEmail}_forget_password_reset`,
        `${userEmail}_forget_password_reset_timeout`,
        this.configService.get<number>('fpResetCount'),
        this.configService.get<number>('fpResetWaitTime'),
        this.configService.get<number>('fpEmailInCacheTime')
      );
    } catch (error: any) {
      Logger.error('forgetPasswordAttemptLimit', {
        METHOD: this.constructor.name + '@' + this.forgetPasswordAttemptLimit.name,
        MESSAGE: error.message,
        REQUEST: { userEmail },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async getUserByEmail(emails: string) {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userEmails = emails.split(',').filter(email => email.trim() !== '');
      const users: Partial<User[]> = await userRepository.find({ email: { $in: userEmails } });
      const userHelper = await this.helperService.getHelper('UserHelper');
      return userHelper.getUserInfo(users);
    } catch (error: any) {
      Logger.error('getUserByEmail', {
        METHOD: this.constructor.name + '@' + this.getUserByEmail.name,
        MESSAGE: error.message,
        REQUEST: { emails },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async getUserEmail(email: string) {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user: Partial<User> = await userRepository.getUserByEmail(email);
      const userHelper = await this.helperService.getHelper('UserHelper');
      return userHelper.getUserEmail(user);
    } catch (error: any) {
      Logger.error('getUserByEmail', {
        METHOD: this.constructor.name + '@' + this.getUserByEmail.name,
        MESSAGE: error.message,
        REQUEST: { email },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async getUserByUid(uIds: string) {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userIds = uIds.split(',').filter(id => id.trim() !== '');
      const users: Partial<User[]> = await userRepository.find({ uid: { $in: userIds } });
      const userHelper = await this.helperService.getHelper('UserHelper');
      return userHelper.getUserInfo(users);
    } catch (error: any) {
      Logger.error('getUserByUid', {
        METHOD: this.constructor.name + '@' + this.getUserByUid.name,
        MESSAGE: error.message,
        REQUEST: { uIds },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async register(signupDetail, cookieData, @Res() res: Response) {
    try {
      const [userRepository, tokenService, userService, authHelper, lrsInstanse, userMgmtUtilityHelperInstance] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.get<UserService>(UserService),
        this.helperService.getHelper('AuthHelper'),
        this.helperService.getHelper('lrsHelper'),
        this.helperService.getHelper('UserMgmtUtilityHelper'),
      ]);

      const redirectUrl = signupDetail?.redirect_url || '';
      Utility.validateClientRequest(signupDetail?.client_id, this.configService.get('clientSecret'), redirectUrl);
      
      const response: CustomResponse = { status: false, msg: 'Something went wrong, please try again.' };
      if (Utility.isEmpty(signupDetail?.time_zone)) {
        signupDetail.time_zone = await userMgmtUtilityHelperInstance.getTimezoneFromCountryCode(
          signupDetail?.country_code,
        );
      }
      const fromMobileFlag =
        !Utility.isEmpty(signupDetail?.from_mobile) && signupDetail?.from_mobile?.toString() === '1' ? true : false;
      signupDetail['fromMobileFlag'] = fromMobileFlag;
      const dataValsForLrs = {
        client_id: this.configService.get('clientKey'),
        redirect_url: signupDetail?.redirect_url,
        from_mobile: signupDetail?.from_mobile,
        appType: ""
      }
      if (!fromMobileFlag) {
        let phoneStatus = 'no';
        const lmsPref = await this.getLmsSettingsFromGid(signupDetail?.affiliateId);

        if (lmsPref.status) {
          phoneStatus = lmsPref?.data['phone_number']['value'];
        }
        
       if (
              phoneStatus === 'yes' &&
              Utility.isEmpty(signupDetail?.phone_no) &&
              (Utility.isEmpty(signupDetail?.register_mode) ||
              (!Utility.isEmpty(signupDetail?.register_mode) && signupDetail?.register_mode !== 'scorm'))
            ) {
              return { ...response, msg: 'Please provide valid phone no.' };
           }     
         
      } else {
        dataValsForLrs.appType = signupDetail?.device_type;
      }

      //implement it here
      const passwdResult = Utility.validatePasswd(
        signupDetail?.user_pwd,
        this.configService.get('passwordLength'),
        this.configService.get('maxPasswordLength'),
      );

      // Check if there's an error
      if (passwdResult && 
          passwdResult.type && 
          passwdResult.type.toLowerCase() === 'error') {
          return response
      }

      // Check whether user already exists
      const user: Partial<User> = await userRepository.getUserByEmail(signupDetail?.user_email);
      if (!Utility.isEmpty(user)) {
        if (user?.status == 0) {
          return { ...response, msg: 'Try using a different email address to create an account.' };
        } else {
          return {...response, msg: 'Email is already exist with different signin method.' }; 
        }
      }

      let user_display_name = signupDetail?.user_name || '';
      if(signupDetail?.user_firstname && signupDetail?.user_lastname) {
        user_display_name = signupDetail?.user_firstname + ' ' +  signupDetail?.user_lastname
      }

      const emailCommFlag = (signupDetail?.email_agreement ?? 'Y').toString().trim();
      signupDetail = {
        ...signupDetail,
        email: signupDetail?.user_email,
        password: signupDetail?.user_pwd,
        status: 1,
        account_setup: this.configService.get('userOptionsAccountSetupComplete'),
        password_created: this.configService.get('userOptionsAccountSetupComplete'),
        display_name: user_display_name,
        accept_agreement: emailCommFlag === 'Y' ? 1 : 0,
      };
      delete signupDetail.user_email;
      delete signupDetail.user_pwd;
      const user_roles: string[] = signupDetail?.user_roles?.split(',') || [];
      const userInfo = await userService.userRegistration(signupDetail, user_roles);
      const userTokenDetail = await tokenService.generateSessionTokens(userInfo, this.configService.get('clientKey'));

      //Send LRS Data Start
      const lrsData = {
        verb: 'register',
        objectType: this.#lrsObjectType,
        objectId: userInfo?.uid,
        dataVals: dataValsForLrs,
      };
      lrsInstanse.sendDataToLrs(userTokenDetail['userData'], lrsData);
      //Send LRS Data End
      
      if (userTokenDetail?.idToken) {
        const [userHelper, cookieHelper] = await Promise.all([
          this.helperService.getHelper('UserHelper'),
          this.helperService.getHelper('CookieHelper'),
        ]);

        const signupRedirectData = await authHelper.getSignupRedirect(cookieData, userInfo, signupDetail?.redirect_url);
        const cookieValue: ResponseCookieType[] = [];
        cookieValue.push(...signupRedirectData.cookieValue);
        if (signupDetail?.auto_login?.toLowerCase() === 'y') {
          cookieValue.push({ name: 'ssoCookie', value: userTokenDetail?.idToken });
          await userHelper.updateUserLoginTime(userInfo);
        }
        await cookieHelper.setBulkCookie(res, cookieValue);
        return { status: true, msg: 'Successfully created new user.', data: userTokenDetail };
      }
      return { ...response, msg: 'Error occurred while creating user.' };
    } catch (error: any) {
      Logger.error('register', {
        METHOD: `${this.constructor?.name}@${this.register?.name}`,
        MESSAGE: error.message,
        REQUEST: signupDetail,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  async checkSignupValidation(data: CheckSignupValidationDto): Promise<{ type: string; msg: string }> {
    try {
      Utility.validateClientRequest(data?.client_id, this.configService.get('clientSecret'));
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(data?.user_email);
      if (user && user?.status) {
        const message =
          user?.status === 1 ? 'Email address already exists, please choose a different one' : 'Try using a different email address to create an account.';
        return { type: 'failed', msg: message };
      }
      return { type: 'success', msg: 'Email available for account creation.' };
    } catch (error: any) {
      Logger.error('checkSignupValidation', {
        METHOD: this.constructor.name + '@' + this.checkSignupValidation.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async validateUserDetails(data: {
    client_id: string;
    authToken: string;
    method: string;
    email: string;
  }): Promise<{ type: string; msg: string }> {
    try{
      if (!data?.client_id) {
        return { type: 'error', msg: 'Unauthorized access request.' };
      }
    Utility.validateClientRequest(data?.client_id, this.configService.get('clientSecret'));
    const socialAuth = this.configService.get('socialAuth');
    const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
    const userData = data?.email ? await userRepository.getUserByEmail(data?.email) : null;

    if (socialAuth?.allowedSocialLoginPlatforms.includes(data?.method)) {
      const socialHelper = await this.helperService.getHelper('SocialLoginHelper');
      const dataa = await socialHelper.processSocialAuthResponse({
        ...data,
        type: data?.method,
        token: data?.authToken,
      }, userData);
      const response = this.checkValidation(dataa?.returnResponse);
      return response
    } else {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userResponse = await userRepository.getUserByEmail(data?.email);
      if (userResponse) {
        return { type: 'error', msg: 'User already exist.' };
      }
      else{
        return { type: 'success', msg: 'User does not exist.' }
      }
    }
  } catch(error: any){
    Logger.error('validateUserDetails', {
      METHOD: this.constructor.name + '@' + this.validateUserDetails.name,
      REQUEST: data,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
  }}

  async signup(data: Partial<SignupDto>, cookieData) {
    try {
      const response = { type: 'error', msg: 'Something went wrong, please try again.' };
      const affiliateId = data?.affiliateId || 2;
      data.source = data?.from_mobile === '1' ? 'mobile' : 'web';
      const redirectUrl = data?.redirect_url || '';
      Utility.validateClientRequest(data?.client_id, this.configService.get('clientSecret'), redirectUrl);
      let timeZone: string;

      if (Utility.isEmpty(data?.time_zone)) {
        const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
        timeZone = await userMgmtUtilityHelperInstance.getTimezoneFromCountryCode(data?.country_code);
      }

      const fromMobileFlag =
        !Utility.isEmpty(data?.from_mobile) && data?.from_mobile?.toString() === '1' ? true : false;
      data['fromMobileFlag'] = fromMobileFlag;
      if (!fromMobileFlag) {
        let phoneStatus = 'no';
        const lmsPref = await this.getLmsSettingsFromGid(affiliateId);
        if (!lmsPref.status) {
          return { ...response, msg: lmsPref.msg };
        }

        if (lmsPref.status) {
          phoneStatus = lmsPref?.data['phone_number']['value'];
        }
        if (
          phoneStatus !== 'yes' &&
          Utility.isEmpty(data?.phone_no) &&
          (Utility.isEmpty(data?.register_mode) ||
            (!Utility.isEmpty(data?.register_mode) && data?.register_mode === 'scorm'))
        ) {
          return { ...response, msg: 'Please provide valid phone no.' };
        }
      }
      const socialAuth = this.configService.get('socialAuth');
      let socialLoginAllowed = false;
      if (!Utility.isEmpty(data?.auth_token)) {
        socialLoginAllowed = socialAuth?.allowedSocialLoginPlatforms.includes(data?.method);
      }

      const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user: Partial<User> = await userRepository.getUserByEmail(data?.user_email);
      if (!Utility.isEmpty(user)) {
        if (user?.status != 1) {
          return { ...response, msg: 'Try using a different email address to create an account.' };
        }
        return {
          ...response,
          msg: 'Email is already exist with different signin method.',
        };
      }
      const utmSource: string = Utility.isEmpty(cookieData?.sl_su_utmz)
        ? this.configService.get<string>('defaultFreemiumSignupUtm')
        : cookieData?.sl_su_utmz;
      const params = {
        email: data?.user_email,
        token: data?.auth_token,
        type: data?.method,
        authRespType: 'token',
        referer: 'register',
        requestSource: 'mobile',
        timeZone: timeZone,
        firstName: data?.first_name,
        lastName: data?.last_name,
        phoneNo: data?.phone_no,
        countryCode: data?.country_code,
        countryId: data?.country_id,
        platform: 'mobileapp',
        referralCode: data?.referral_code,
        userType: data?.user_type,
        utmSource: utmSource,
      };
      let userValidity;
      if (socialLoginAllowed) {
        const socialAuthResponse = await socialLoginHelper.processSocialAuthResponse(params, user);
        userValidity = this.checkValidation(socialAuthResponse?.returnResponse);
        if (userValidity?.type === 'error' && response['errorCode'] && response['errorCode'] != 101) {
          return userValidity;
        }
      }
      const createUserAccountResponse = await socialLoginHelper.createUserAccount(data, utmSource);
      if (createUserAccountResponse?.type === 'error') {
        return createUserAccountResponse;
      }
      if (socialLoginAllowed) {
        const socialLoginData = await socialLoginHelper.createUserSocialData({
          token: params?.token,
          type: params?.type,
          email: params?.email,
          source: params?.requestSource,
        });
        if (socialLoginData?.status) {
          const linkData = {
            ...socialLoginData?.data,
            status: socialLoginData?.status,
          };
          await socialLoginHelper.linkUserSocialData(linkData, createUserAccountResponse?.data);
        }
      }
      const newUser: Partial<User> = createUserAccountResponse?.data;
 
      const additionalInfo = {
        response_type: !Utility.isEmpty(data?.redirect_url) ? 'redirect' : 'json',
        from_mobile_flag: data?.from_mobile === '1' ? 1 : 0,
        app_type: data?.device_type,
        redirect_url: data?.redirect_url,
        lrs_object_type: this.#lrsObjectType,
        client_id: data?.client_id,
      };
      const loginResult = await this.getUserToken(newUser, additionalInfo);
      let userTokenDetail
      if(loginResult?.type == 'success'){
        userTokenDetail = { _id : loginResult._t , userData : newUser};
      }
    
      // done to track mobile login
      let lrsData = {
        verb: 'register',
        objectType: this.#lrsObjectType,
        objectId: newUser?.uid,
        dataVals: {
          client_id: this.configService.get('clientKey'),
          redirect_url: data?.redirect_url,
          from_mobile: data?.from_mobile == '1' ? 1 : 0,
          appType: data?.from_mobile == '1' ? params?.platform : '',
        },
      };

      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(userTokenDetail['userData'], lrsData);
      const lmsUrl = await this.getDomainBasedOnUserGroupId(userTokenDetail?.userData);
      return { type: 'success', _t: userTokenDetail?._id, lmsUrl: lmsUrl };
    } catch (error: any) {
      Logger.error('signup', {
        METHOD: this.constructor.name + '@' + this.signup.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async linkAccounts(userLinkData: LinkAccountsDto) {
    try {
      let response: ValidationResponse = { type: 'error', msg: 'Error occurred while creating user.' };
      Utility.validateClientRequest(userLinkData?.client_id, this.configService.get('clientSecret'));
      const params = {
        requestSource: 'mobile',
        referer: 'register',
        type: userLinkData?.method,
        email: userLinkData?.user_email,
        token: userLinkData?.auth_token,
        authRespType: userLinkData?.auth_token ? 'token' : '',
      };
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user: Partial<User> = await userRepository.getUserByEmail(userLinkData?.user_email);

      const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
      const data = await socialLoginHelper.processSocialAuthResponse(params, user);
      response = this.checkValidation(data?.returnResponse);
      if (response['type'] == 'success' || (response['errorCode'] && response['errorCode'] == 102)) {
        const userSocialAccountDetails: UserSocial = {
          type: userLinkData?.method,
          source: 'mobile',
          email: userLinkData?.user_email,
          status: 1,
          created_on: new Date().getTime().toString(),
        };
        user?.user_social_data?.push(userSocialAccountDetails);

        const socialRespData = await socialLoginHelper.createUserSocialAccountDetails(user);
        if (socialRespData['type'] && socialRespData['type'] == 'success') {
          const additionalParams = {
            response_type: 'json', //TODO Fix need to be worked
            from_mobile_flag: userLinkData?.from_mobile,
            app_type: userLinkData?.device_type,
            redirect_url: userLinkData?.redirect_url,
            lrs_object_type: this.#lrsObjectType,
            client_id: userLinkData?.client_id,
          };
          const loginResponse = await this.getUserToken(user, additionalParams);
          return loginResponse;
        } else {
          response['msg'] = socialRespData['message'];
        }
      }
      return response;
    } catch (error: any) {}
  }

  async getLinkAccounts(reqData: {
    client_id: string;
    userId: string;
    email: string;
    redirect_url: string;
    from_mobile: string;
  }) {
    try {
      const responseType =
        !Utility.isEmpty(reqData?.redirect_url) && Utility.checkZendUri(reqData?.redirect_url) ? 'redirect' : 'json';
        Logger.info('getLinkAccounts', {responseType});
      // ToDo validation client request verification with redirect url
      Utility.validateClientRequest(reqData?.client_id, this.configService.get('clientSecret'));
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userResponse = await userRepository.getUserByEmail(reqData?.email);
      const socialData = userResponse?.user_social_data.map((data) => {
        return { type: data['type'], status: data['status'].toString(), created_on: data['created_on'] };
      });
      return {
        type: 'success',
        isPasswordCreated: userResponse?.password_created ? true : false,
        accounts: socialData,
      };
    } catch (error: any) {
      Logger.error('getLinkAccounts', {
        METHOD: this.constructor.name + '@' + this.getLinkAccounts.name,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async removeLinkedAccounts(reqData: {
    client_id: string;
    userId: string;
    email: string;
    redirect_url: string;
    method: string;
  }) {
    try {
      const responseType =
        !Utility.isEmpty(reqData?.redirect_url) && Utility.checkZendUri(reqData?.redirect_url) ? 'redirect' : 'json';
       Logger.info('removeLinkedAccounts', {responseType})
        // ToDo validation client request verification with redirect url
      Utility.validateClientRequest(reqData?.client_id, this.configService.get('clientSecret'));
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(reqData?.email);
      if (user?.user_social_data?.length > 1 || user?.account_setup === 1) {
        const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
        const updatedResponse = await socialLoginHelper.updateUserSocialAccountDetails(user, reqData?.method);
        // TODO: synch data with cloud6
        const response = { type: 'error', msg: 'Error occurred while fetching accounts detail.' };
        if (updatedResponse['type'] === 'success') {
          response['type'] = 'success';
          response['msg'] = 'Details stored successfully';
          return response;
        }
        return updatedResponse ? updatedResponse : response;
      } else {
        return {
          type: 'error',
          msg: 'Unable to delink account.',
        };
      }
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Retrieves user roles from the role repository, with optional pagination.
   *
   * @param pageData - Optional pagination data.
   * @param pageData.pageSize - The number of roles to retrieve per page.
   * @param pageData.page - The page number to retrieve.
   * @returns A promise that resolves to the list of user roles, either paginated or all roles if no pagination is provided.
   * @throws Will throw an error if the role retrieval fails.
   *
   * Logs errors with details including method name, error message, request data, and stack trace.
   */
  async getUserRoles(pageData?: { pageSize?: number; page?: number, prefix?: string  }) {
    try {
      const roleRepository = await this.helperService.get<IRoleRepository>(RoleRepository);
      const filter: any = {};

      if (pageData?.prefix) {
        filter.roleName = { $regex: `^${pageData.prefix}`, $options: 'i' }; 
      }
      if (pageData?.pageSize && pageData?.page) {
        const skipCount = (pageData.page - 1) * pageData.pageSize;
        return await roleRepository.getRoles({
          pageSize: pageData.pageSize,
          skipCount,
          filter
        });
      }

      return await roleRepository.findAll(filter);
    } catch (error: any) {
      Logger.error('getUserRoles', {
        METHOD: `${this.constructor.name}@${this.getUserRoles.name}`,
        MESSAGE: error.message,
        REQUEST: pageData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async assignUserRole(reqBody: { uid: number; rid: string }) {
    try {
      const [roleRepository, userRepository, userHelper] = await Promise.all([
        this.helperService.get<IRoleRepository>(RoleRepository),
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
      ]);
      const [roleDoc, userDoc] = await Promise.all([
        roleRepository.findOne({ rid: reqBody.rid }),
        userRepository.findByUID(reqBody.uid),
      ]);
      const userResponse = { roles: {} };
      userResponse['roles'] = { ...this.mapUserRoles(userDoc.roles) };
      if (!roleDoc) {
        return userResponse;
      } 

      const isAlreadyAssigned = userDoc?.roles?.some((role) => role.rid.toString() === reqBody.rid);
      if (isAlreadyAssigned) {
        return userResponse;   
      }
      userDoc.roles.push(roleDoc);
      userResponse['roles'][roleDoc?.rid] = roleDoc?.roleName;
      
   
      const mysqlPayload = userDoc.roles.map(role => ({
        uid: reqBody.uid,
        rid: role.rid,
      }));

      await Promise.all([
        userHelper.syncCloud6SentinelUserRole(mysqlPayload),
        userRepository.findOneAndUpdate({ uid: reqBody.uid }, { roles: userDoc.roles }),
      ]);
      if (this.configService.get('enableDrupalSync')) {
        const roleMap: Record<string, string> = {};
        for (const role of userDoc.roles) {
          if (role?.rid && role?.roleName) {
            roleMap[role.rid.toString()] = role.roleName;
          }
        }
  
        await userHelper.syncUserDataWithMySQLDrupal({
          uid: reqBody.uid,
          roles: roleMap,
        });
      }
      return userResponse;
    } catch (error: any) {
      Logger.error('assignUserRole', {
        METHOD: this.constructor.name + '@' + this.assignUserRole.name,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async revokeUserRole(reqBody: { uid: number; rid: string }) {
    try {
      const [userRepository, userHelper] = await Promise.all([
          this.helperService.get<IUserRepository>(UserRepository),
          this.helperService.getHelper('UserHelper'),
      ]);
      const userDoc = await userRepository.findByUID(reqBody.uid);
      const userResponse = { roles: {} };
      const filteredRoles = userDoc?.roles?.filter((role) => role.rid.toString() !== reqBody.rid);
      // TODO: synch data with cloud6
      await userRepository.findOneAndUpdate({ uid: reqBody.uid }, { roles: filteredRoles });
     
      if (this.configService.get('enableDrupalSync')) {
        const roleMap: Record<string, string> = {};
        for (const role of filteredRoles) {
          if (role?.rid && role?.roleName) {
            roleMap[role.rid.toString()] = role.roleName;
          }
        }
        await userHelper.syncUserDataWithMySQLDrupal({
          uid: reqBody.uid,
          roles: roleMap,
        });
      }
      userResponse['roles'] = { ...userResponse['roles'], ...this.mapUserRoles(filteredRoles) };
      return userResponse;
    } catch (error: any) {
      Logger.error('revokeUserRole', {
        METHOD: this.constructor.name + '@' + this.revokeUserRole.name,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  private mapUserRoles(roles: any[]) {
    const mappedRoles = {};
    roles.forEach((role) => {
      mappedRoles[role.rid] = role.roleName;
    });
    return mappedRoles;
  }
  async authenticate(data: AuthenticationRequestDto) {
    let response = {
      type: 'error',
    };
    try {
      // aditional params
      const redirectUrl = data?.redirect_url || '';
      const authHelper = await this.helperService.getHelper('AuthHelper');
      Utility.validateClientRequest( data?.client_id, this.configService.get('clientSecret'), redirectUrl);

      // user authentication
      const user = await authHelper.authenticateUser({ email: data?.user_login, password: data?.user_pwd });

      if(user){
        // additional options
        const additionalInfo = {
          response_type: !Utility.isEmpty(redirectUrl) ? 'redirect' : 'json',
          from_mobile_flag: data?.from_mobile === '1' ? 1 : 0,
          app_type: data?.device_type || '',
          redirect_url: data?.redirect_url,
          lrs_object_type: this.#lrsObjectType,
          client_id: data?.client_id,
        };

        // Have replace generateSessionTokens with getUserToken
        const userTokenDetail = await this.getUserToken(user, additionalInfo);
        if (userTokenDetail?._t) {
          response.type = 'success';
          response['_t'] = userTokenDetail._t;
          response['lmsUrl'] = userTokenDetail.lmsUrl;
          return response;
        }
      } else {
        throw Error('Some error occurred while authenticating user.');
      }
      return response;
    } catch (error: any) {
      Logger.error('authenticate', {
        METHOD: this.constructor?.name + '@' + this.authenticate?.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error instanceof BadRequestException && error?.message === 'UserNotFoundException') {
        response['msg'] = 'No active account associated with this email address';
      } else if (error instanceof BadRequestException && error?.message === 'InvalidCredentials') {
        response['msg'] = 'Invalid user credentials.';
      } else if (error instanceof UnauthorizedException) {
        response['msg'] = error.message;
      } else {
        response['msg'] = 'Some error occurred while authenticating user.';
      }
      return response;
    }
  }

  async updateProfile(data: Partial<UpdateProfileDto>) {
    try {
      const [userRepository, userHelper , lrsInstanse] = await Promise.all([
        this.helperService.getHelper<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper('lrsHelper')
      ]);
      const user: Partial<User> = await userRepository.findByUID(data?.uid);
      if (!user || !user.status) {
        throw new BadRequestException('UserNotFoundException');
      }
      const params = {
        first_name: data?.first_name || '',
        last_name: data?.last_name || '',
        display_name: `${data?.first_name} ${data?.last_name}`,
      };
      
      const [updatedUserResult, cloud6UpdateResult] = await Promise.all([
        userRepository.findOneAndUpdate({ email: user?.email }, params),
        userHelper.updateCloud6SentinelByUidOrMail({ uid: user?.uid, ...params })
      ]);

      params['user_email'] = user?.email;
      params['user_id'] = user?.uid;

      lrsInstanse.sendDataToKafka(this.configService.get('profileTopic'), params);

      if( updatedUserResult && cloud6UpdateResult ) {
         // drupal synchronization
         userHelper.syncUserDataWithMySQLDrupal(params);
         return { type: 'success', msg: 'Your profile has been successfully updated.' };
       } else {
         throw new InternalServerErrorException('Update failed to DB.');
      }
    } catch (error: any) {
      Logger.log('updateProfile', {
        METHOD: this.constructor.name + '@' + this.updateProfile.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { type: 'error', msg: 'Error occurred while updating profile. Please try again.' };
    }
  }

  async updateLinkedinStatus(data: { source: string; user_id: number }) {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.findByUID(data?.user_id);
      if (user) {
        const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
        const updatedResponse = await socialLoginHelper.updateUserSocialAccountDetails(user, data?.source);
        // TODO: synch data with cloud6
        const response = { type: 'error', msg: 'Some error occurred. Please try again.' };
        if (updatedResponse['type'] === 'success') {
          response['type'] = 'success';
          response['msg'] = 'Linkedin status updated successfully.';
          return response;
        }
        return response;
      } else {
        return { type: 'error', msg: 'Some error occurred. Please try again.' };
      }
    } catch (error: any) {}
  }

  async getOriginalToken(data: { redirect_url: string; token: string }) {
    const response = {
      type: 'error',
      msg: 'Some error occurred while authenticating user.',
    };
    try {
      if (data?.token) {  
        const authTokenHelper: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
        const token = await authTokenHelper.encodeOriginalToken(data.token);
        return { type: 'success', msg: 'Original Token generated', _t: token };
      } else {
        return { type: 'error', msg: 'Invalid Token sent.' };
      }
    } catch (error: any) {
      Logger.error('getOriginalToken', {
        METHOD: this.constructor.name + '@' + this.getOriginalToken.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
      });
    }
    return response;
  }

  async updateUserTimezone(requestBody: UpdateUserTimezoneDto) {
    try {
      const userMgmtUtility = await this.helperService.getHelper('UserMgmtUtilityHelper');
      const user = await userMgmtUtility.updateUserTimezone(requestBody);
      return user;
    } catch (error: any) {
      Logger.error('updateUserTimezone', {
        METHOD: this.constructor.name + '@' + this.updateUserTimezone.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
      });
      throw error;
    }
  }
  async getTaxonomy(data) {
    try {
      const aggregateQuery = [
        {
          $match: {
            category: {
              $in: data?.category.split(','),
            },
            ...(data?.name && { name: data.name }),
          },
        },
        {
          $group: {
            _id: '$category',
            data: {
              $push: '$$ROOT',
            },
          },
        },
        {
          $group: {
            _id: null,
            root: {
              $push: {
                k: '$_id',
                v: '$data',
              },
            },
          },
        },
        {
          $replaceRoot: {
            newRoot: {
              $arrayToObject: '$root',
            },
          },
        },
      ];

      const taxonomyRepo = await this.helperService.get<ITaxonomyRepository>(TaxonomyRepository);
      const resultAsObject = await taxonomyRepo.aggregate(aggregateQuery);
      return { status: 'success', data: resultAsObject[0] || {} };
    } catch (error: any) {
      Logger.log('getTaxonomy', {
        METHOD: this.constructor.name + '@' + this.getTaxonomy.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { type: 'error', msg: 'Error occurred while updating profile. Please try again.' };
    }
  }

  // Todo this function is used only in user-api service
  /**
   * Handles user login.
   * @param user - Partial<User> representing user details.
   * @param additionalParams - Additional parameters for the login request.
   * @returns A Promise resolving to an object with 'type' and 'msg'.
   *   - type: string indicating the result type ('error' or 'success').
   *   - msg: string containing a message related to the login result.
   */
   async getUserToken(
    user: Partial<User>,
    additionalParams,
  ): Promise<{
    type: string;
    msg: string;
    _t?: string;
    lmsUrl?: string;
  }> {
    const defaultResponse = {
      type: 'error',
      msg: 'Some error occurred while user login.',
    };

    try {
      const [userMgmtUtilityHelper, authHelper] = await Promise.all([
        this.helperService.getHelper('UserMgmtUtilityHelper'),
        this.helperService.getHelper('AuthHelper'),
      ]);

      await userMgmtUtilityHelper.updateUserTimezone({
        uid: user?.uid,
        country: user?.country_code,
      });

      const userTokenData: { idToken: string; userData: object } = await authHelper.getTokenByEmail(
        user?.email,
        additionalParams?.client_id,
      );

      if (!userTokenData?.idToken) {
        return defaultResponse;
      }

      let lmsUrl = '';
      if (Array.isArray(user?.user_groups) && user.user_groups.length > 0) {
        const gidObj = {
          gid: user.user_groups.join(','),
        };
        lmsUrl = await this.getDomainBasedOnUserGroupId(gidObj);
      }

      const response = {
        type: 'success',
        msg: 'user logged in successfully',
        _t: userTokenData.idToken,
        lmsUrl,
      };

      const [userHelper, lrsInstance] = await Promise.all([
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper('lrsHelper'),
      ]);

      userHelper.updateUserLoginTime(user);

      const lrsData = {
        verb: 'login',
        objectType: additionalParams.lrs_object_type,
        objectId: user?.uid,
        dataVals: {
          client_id: additionalParams.client_key,
          redirect_url: additionalParams.redirect_url,
          from_mobile: additionalParams.from_mobile_flag,
          appType: additionalParams.from_mobile_flag === 1 ? additionalParams.app_type : '',
        },
      };

      lrsInstance.sendDataToLrs(user, lrsData);

      return response;
    } catch (error: any) {
      Logger.error('login', {
        METHOD: `${this.constructor?.name}@${this.login?.name}`,
        MESSAGE: error.message,
        REQUEST: { user, additionalParams },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return defaultResponse;
    }
  }

  /**
   * Retrieves the domain based on the user's group ID.
   * @param userData - Object containing user data.
   * @returns A Promise resolving to a string representing the domain.
   */
  async getDomainBasedOnUserGroupId(userData: any): Promise<string> {
    const domain: string = this.configService.get('lmsSiteUrl');
    if (!userData.gid) {
      return domain;
    }

    const gidArr: number[] = userData.gid.split(',');

    if (gidArr.length === 1 && gidArr[0] === this.configService.get('defaultLrsApplicationId')) {
      return domain;
    }

    const defaultLrsIndex: number = gidArr.indexOf(this.configService.get('defaultLrsApplicationId'));
    if (defaultLrsIndex !== -1) {
      gidArr.splice(defaultLrsIndex, 1);
    }

    const maxGid: string = Math.max(...gidArr).toString();
    const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
    const enterpriseResponse = await enterpriseService.getGroupDomainByGid({ gid :  Number(maxGid)});
    if (enterpriseResponse && enterpriseResponse.data[0] && enterpriseResponse?.data[0]?.lmsSiteUrl) {
      return enterpriseResponse?.data[0]?.lmsSiteUrl;
    } else {
      return domain;
    }
  }

  checkValidation(data: any) {
    const response: any = {
      type: 'error',
      msg: data.msg,
    };
    if (data.type && data.type === 'success') {
      const socialAccountStatus = data.socialAccountStatus;
      const userAccountStatus = data.userAccountStatus;

      response.userAccountStatus = userAccountStatus;
      response.socialAccountStatus = socialAccountStatus;
      response.accountSetupStatus = data.accountSetupStatus;

      if (!socialAccountStatus && !userAccountStatus) {
        response.errorCode = 101;
        response.msg = 'User does not exist.';
      } else if (!socialAccountStatus) {
        response.errorCode = 102;
        response.msg = 'Email is already exist with different signin method.';
      } else if (socialAccountStatus) {
        response.errorCode = 103;
        response.msg = 'Same method exist.';
      }
    }
    return response;
  }

  async getLmsSettingsFromGid(groupId) {
    const cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
    const enterpriseSetting = await cloud6Service.getLmsEnterpriseSettings({ group_id: groupId });
    const userHelper = await this.helperService.getHelper('UserHelper');
    const lmsSettings = await userHelper.lmsSettingsCacheData(enterpriseSetting?.data?.data);
    if (Utility.isEmpty(lmsSettings)) {
      return { status: false, msg: 'No data found', data: '' };
    }
    return { status: true, msg: 'success', data: lmsSettings };
  }
  async processGoogleOneTapResponse(reqData, @Res() res) {
    const { 
      credential: userCredential, 
      is_frs_page: isFrsPage, 
      calling_api_from: apiSource 
    } = reqData;
    const redirectUrl = apiSource
      ? `${this.configService.get('sheldonSiteUrl')}${decodeURIComponent(apiSource)}`
      : `${this.configService.get('sheldonSiteUrl')}/skillup-free-online-courses`;

    try {
     
      const params = {
        type: 'google',
        source: 'web',
        referer: 'login',
        authRespType: 'token',
        token: userCredential,
      };
      const frsSource = isFrsPage === 'true';

      const socialLoginService = await this.helperService.get<SocialLoginService>(SocialLoginService);
      const authResponsePayload: CustomResponse = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
        token: params.token,
        type: params.type,
      });

      if (authResponsePayload.status === true) {
        const userData = {
          status: 'anf',
          emailAddress: authResponsePayload?.data?.data.email,
          type: params.type,
          referer: params.referer,
          firstName: authResponsePayload?.data?.data.given_name,
          lastName: authResponsePayload?.data?.data.family_name,
        };

        const cookieHelper = await this.helperService.getHelper('CookieHelper');

        const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
        let userResponse = await userRepository.getUserByEmail(userData.emailAddress);

        const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);

        if (!userResponse) {
          const socialHelper = await this.helperService.getHelper('SocialLoginHelper');
          userResponse = await this.handleNewUser(userData, res, frsSource, {
            authTokenHelper,
            cookieHelper,
            socialHelper,
          });
        } else {
          const userTokenData = await authTokenHelper.generateSessionTokens(
            userResponse,
            this.configService.get('clientKey'),
          );

          const userHelper = await this.helperService.getHelper('UserHelper');
          userHelper.updateUserLoginTime(userResponse);
          cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenData?.idToken);
        }
        const authHelper = await this.helperService.getHelper('AuthHelper');
        // LRS logging starts
        const lrsData = {
          verb: 'register',
          objectType: this.#lrsObjectType,
          objectIds: userResponse?.uid,
          dataVals: {
            client_id: this.configService.get('ssoClientKey'),
            redirectUrl: redirectUrl,
          }
        };
        const lrsInstance = await this.helperService.getHelper('lrsHelper');
        lrsInstance.sendDataToLrs(userResponse, lrsData);
        // LRS logging ends
        return await this.handleTokenRedirect(userResponse, res, apiSource, frsSource, {
          cookieHelper,
          authHelper,
        });
      } else {
        return { tokenRedirectUrl: redirectUrl };
      }
    } catch (error) {
      return { tokenRedirectUrl: redirectUrl };
    }
  }

  private async handleNewUser(userData, res, frsSource, helper) {
    const token = await helper.authTokenHelper.createSignedToken(userData);
    helper.cookieHelper.setCookie(res, this.configService.get('socialLinkToken'), token, { expires: 3600 * 1000 });

    const userAccount = await helper.socialHelper.registerFrsUser(userData, res?.cookie);
    if (frsSource) {
      const utmData = res?.cookie?.sl_su_utmz || this.configService.get('defaultFreemiumSignupUtm');
      const utmDetails = utmData.split('|');
      const frsCookieData = Utility.getFrsUtm(utmDetails);
      const userCommunityHelper = await this.helperService.getHelper('UsermgmtCommunityHelper');
      userCommunityHelper.updateUserSignupUtm({ email: userAccount?.email, utm_source: frsCookieData });
    }
    if (userAccount?.setCookie) {
      helper.cookieHelper.setCookie(res, userAccount?.cookieName, userAccount?.cookieValue);
    }
    return userAccount?.user;
  }

  private async handleTokenRedirect(userResponse, res, callingApiFrom, frsSource, helper) {
    if (frsSource) {
      const frsUrl = callingApiFrom
        ? `${this.configService.get('sheldonSiteUrl')}${decodeURIComponent(callingApiFrom)}`
        :`${this.configService.get('sheldonSiteUrl')}/skillup-free-online-courses`;
      helper.cookieHelper.setCookie(res, this.configService.get('frsOneTapRedirectCookie'), frsUrl, {
        expires: 3600 * 1000,
      });
      const utmData = res?.cookie?.sl_su_utmz || this.configService.get('defaultFreemiumSignupUtm');
      const utmDetails = utmData.split('|');
      const frsCookieData = Utility.getFrsUtm(utmDetails);
      helper.cookieHelper.setCookie(res, this.configService.get('slUuUtmz'), frsCookieData, { expires: 3600 * 1000 });
      return { tokenRedirectUrl: frsUrl };
    }
    const redirectUrl = callingApiFrom
      ? `${decodeURIComponent(callingApiFrom)}`
      : `${this.configService.get('sheldonSiteUrl')}/skillup-free-online-courses`;

    helper.cookieHelper.setCookie(res, this.configService.get('skillUpOneTapRedirectCookie'), redirectUrl, {
      expires: 3600 * 1000,
    });

    const tokenRedirectUrl = await helper.authHelper.generateRedirectLinkToManageRedirect(
      userResponse,
      'login',
      'email',
    );

    return { tokenRedirectUrl };
  }

  async validateHashToken(
    requestType: string = '',
    affiliateId: string = '',
    timestamp: string = '',
    userLogin: string = '',
    reqHashToken: string = '',
    teamId: string = '',
  ) {
    let hashToken: string;
    const tokenService = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
    const learnerRequest = this.configService.get<number>('learnerActivationRequest') || '';
    const teamRequest = this.configService.get<number>('teamLearnerActivationRequest') || '';
    const secretSalt = this.configService.get<string>('drupal_hash_salt');
    if (Utility.isEmpty(requestType) || requestType == learnerRequest) {
      hashToken = tokenService.learnerAuthenticationRehash(
        affiliateId,
        timestamp,
        userLogin,
        secretSalt,
      );
    } else if (!Utility.isEmpty(requestType) && requestType == teamRequest) {
      hashToken = tokenService.teamLearnerAuthenticationRehash(
        affiliateId,
        teamId,
        requestType,
        timestamp,
        userLogin,
        secretSalt,
      );
    }

    // Validate hash token
    if (hashToken !== reqHashToken) {
      throw new Error('Invalid hash token');
    }
  }

  async authenticateSaml(reqData: AuthenticateSaml) {
    const response = {
      type: 'error'
  };
    try {
      const redirectUrl = reqData?.redirect_url || '';
      Utility.validateClientRequest( reqData?.client_id, this.configService.get('clientSecret'), redirectUrl);
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(reqData?.user_login);
      if (!user || user?.status !== 1) {
        throw Error('user not found');
      }
      if(reqData?.additional_info) {
          const additional_info = typeof reqData.additional_info === 'string' 
          ? JSON.parse(reqData.additional_info) 
          : reqData.additional_info;
          //validating hash token
        await this.validateHashToken(
          additional_info?.['request_type'],
          additional_info?.['affiliate_id'],
          additional_info?.['timestamp'],
          reqData?.user_login,
          additional_info?.['hash_token'],
          additional_info?.['team_id'],
        );
      } else {
        throw Error('Additional info not found for hash token validation');
      }
      const additionalInfo = {
        response_type: !Utility.isEmpty(redirectUrl) ? 'redirect' : 'json',
        from_mobile_flag: reqData?.from_mobile === '1' ? 1 : 0,
        app_type: reqData?.device_type,
        redirect_url: redirectUrl,
        lrs_object_type: this.#lrsObjectType,
        client_id: reqData?.client_id,
      };
      const tokenResponse = await this.getUserToken(user, additionalInfo);
      if (tokenResponse?._t) {
        response.type = 'success';
        response['_t'] = tokenResponse._t;
        response['lmsUrl'] = tokenResponse.lmsUrl;
        return response;
      } else {
        throw Error('Some error occurred while generating token.');
      }
    } catch (error: any) {
      Logger.error('authenticateSaml', {
        METHOD: this.constructor.name + '@' + this.authenticateSaml.name,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error instanceof BadRequestException && error?.message === 'UserNotFoundException') {
        response['msg'] = 'No active account associated with this email address';
      } else if (error instanceof UnauthorizedException) {
        response['msg'] = error.message;
      } else {
        response['msg'] = 'Some error occurred while authenticating user.';
      }
      return response;
    }
  }

  async getTaxonamyName(uid: string) {
    try{
      if (!uid ) {
        throw new BadRequestException('Please provide user id')
      }
    // Find user by UID
    const userRepository = await this.helperService.getHelper<UserRepository>(UserRepository);
    const user: Partial<User> = await userRepository.findOne({ uid })
    if(user){
    const response = {
      company: user.work_experience?.map(({ company }) => company && ({ tid: company, name: company })).filter(Boolean) || [],
      designation: user.work_experience?.map(({ designation }) => designation && ({ tid: designation, name: designation })).filter(Boolean) || [],
      institute: user.academics?.map(({ institute }) => institute && ({ tid: institute, name: institute })).filter(Boolean) || [],
    };
    return response;
  }
  }catch(err: any){
    Logger.error('Error fetching taxonomy details', {
      METHOD: this.constructor.name + '@' + this.getTaxonamyName.name,
      MESSAGE: err?.message,
      UID: uid,
      STACK: err?.stack,
      TIMESTAMP: new Date().toISOString(),
    });
  }
  }

async getTermsListByTaxonomyCategory(taxonomy: string, appType: string): Promise<any> {
  try {
    if (!taxonomy) {
      throw new BadRequestException('Please provide a taxonomy category');
    }
    const categoryEnum = taxonomy as Category;
    const taxonomyRepo = await this.helperService.get<ITaxonomyRepository>(TaxonomyRepository);
    const taxonomies = await taxonomyRepo.find({ category: categoryEnum });

    const formattedTerms = {};
   

    if(appType === 'mobile') {
        taxonomies.forEach((taxonomy) => {
            formattedTerms[taxonomy.tid] = taxonomy.name; 
        });
    } else {
       taxonomies.forEach((taxonomy) => {
          formattedTerms[taxonomy._id.toString()] = taxonomy.name; 
      });
    }

    return formattedTerms;
  } catch (err: any) {
    Logger.error('Error fetching taxonomy terms', {
      METHOD: `${this.constructor.name}@${this.getTermsListByTaxonomyCategory.name}`,
      MESSAGE: err?.message,
      CATEGORY: taxonomy,
      STACK: err?.stack,
      TIMESTAMP: new Date().toISOString(),
    });
  }
}

async forgetPassword(email: string, param?: { fm?: number; appType?: string, client_id?: string, linkOnly?: number }): Promise<boolean | {type: string; msg?: string; link?: string }> {
    try {
      const [userRepository, paperclipService, authHelper, cacheService, emailHelper, lrsInstance] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<PaperclipService>(PaperclipService),
        this.helperService.getHelper('AuthHelper'),
        this.helperService.get<CachingService>(CachingService),
        this.helperService.getHelper('EmailHelper'),
        this.helperService.getHelper('lrsHelper')
      ]);
      const user = await userRepository.getUserByEmail(email);
      if (!user ) {
        throw new BadRequestException('UserNotFoundException');
      }
      if (!user.status) {
        throw new BadRequestException('UserDisabled');
      }
    
const attemptResponse = await this.forgetPasswordAttemptLimit(user?.email)
 
  if(attemptResponse.type == 'notice') {

    throw new BadRequestException('AttemptLimitExceed');
  
  }
      const resetResponse = await authHelper.getUserPassResetUrl(user?.uid);
      await cacheService.set(`${user.uid}`, resetResponse.token, this.configService.get<number>('resetResponseTime')); 
      if (param?.linkOnly === 1) {
        const resetUrl = param.fm === 1 ? `${resetResponse.url}?fm=1&appType=${param.appType}` : resetResponse.url;
      if (resetUrl){
        const invalidateToken = await paperclipService.invalidateTokenForAppUser(user.uid);
        if (!invalidateToken) {
          Logger.error('INVALIDATE TOKEN DATA AT LINK', {
            method: this.constructor.name + '@' + this.forgetPassword.name,
            invalidateToken,
            uid: user.uid,
          });
        }
        return { type: 'success', link: resetUrl };

      } else{
        return { type: 'error', msg: 'Error occurred while generating forgot password link.' };
      }
       
      } 
      else{
      const emailResponse = await emailHelper.sendEmail(email, 'forgotPassword', {
        resetPasswordUrl: param?.fm === 1 ? `${resetResponse?.url}?fm=1&appType=${param?.appType}` : resetResponse?.url,
        displayName: user?.display_name,
      });
      if (emailResponse) {
      
        await cacheService.set(`${user?.uid}`, resetResponse?.token, this.configService.get<number>('resetResponseTime')); //store the token in memcache for expiry
      
      
      const  invalidateToken = await paperclipService.invalidateTokenForAppUser(user.uid);
       if (!invalidateToken) {
        Logger.error('INVALIDATE TOKEN DATA AT REST', {
          method: this.constructor.name + '@' + this.forgetPassword.name,
          invalidateToken,
          uid: user.uid,
        });
      }
      const userLrsData = {
        id: user?.uid,
        email: user?.email,
        name: user?.display_name || '',
        roles: user?.roles,
      };
      const lrsData = {
        verb: 'forgot-password',
        objectType: this.#lrsObjectType,
        objectId: user?.uid,
        dataVals: {
          client_id: param?.client_id,
          redirect_url: resetResponse?.url,
          from_mobile: param?.fm || 0,
          appType: param?.fm === 1 ? param?.appType : '',
        },
      };
      lrsInstance.sendDataToLrs(userLrsData, lrsData);
      // End LRS Data
    
      return emailResponse ? true : false;
    }  else {
      return { type: 'error', msg: 'Error occurred while sending email.' };
     
     }
  
    }
  } catch (error: any) {
      Logger.error('forgetPassword', {
        METHOD: this.constructor.name + '@' + this.forgetPassword.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async resetPassword(resetPwd: {uid:string, pass: string }): Promise<{ success: boolean, msg: string}> {
    try {
      const uid =  resetPwd?.uid;
      const hashPassword = drupalHash.hashPassword(resetPwd?.pass);
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await  userRepository.findOneAndUpdate(
        { uid },
        { user_options: 1, password: hashPassword }
      );
      const  userHelper = await this.helperService.getHelper('UserHelper')
     
      if (!user) {
        return {
          success: false,
          msg: 'User account does not exist.'
        };
      }

      const passStatus = await userHelper.updateCloud6SentinelByUidOrMail({uid , pass: hashPassword, user_options: 1});

      if (this.configService.get('enableDrupalSync') && user) {
        const syncUser = {
          uid,
          password: resetPwd?.pass,
          user_options: 1,
        };
        await userHelper.syncUserDataWithMySQLDrupal(syncUser);
      }

      if(!passStatus || !user){
        Logger.error('resetPassword', {
          METHOD: this.constructor.name + '@' + this.resetPassword.name,
          MESSAGE: "Password not changed",
          REQUEST: resetPwd,
          TIMESTAMP: new Date().getTime(),
        });
        return { success: false, msg: 'Password not updated.' };
      }
      return {
        success: true,
        msg: 'Password reset successful.'
       
      };
    } catch (error: any) {
      Logger.error('resetPassword', {
        METHOD: this.constructor.name + '@' + this.resetPassword.name,
        MESSAGE: error.message,
        REQUEST: resetPwd,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async validateToken(data: {
    client_id: string;
    redirect_url?: string;
    token: string;
  }): Promise<any> {
    let response = {
      type: 'error',
      msg: 'Error occurred while validating token.',
    };
  
    const currentTime =  new Date();;
    const clientKey = (data?.client_id || '').trim();
    const redirectUrl = (data?.redirect_url || '').trim();
    let responseType : 'json' | 'redirect' = Utility.isEmpty(redirectUrl)  ? 'json' : 'redirect';
    Logger.info('validateToken', {responseType})
    try {
      Utility.validateClientRequest( clientKey, this.configService.get('clientSecret'), redirectUrl);
      const jwtToken = (data?.token || '').trim();
    
      if(jwtToken){
        const [
          tokenService,
          userRepository,
          userHelper
        ] = await Promise.all([
          this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
          this.helperService.get<IUserRepository>(UserRepository),
          this.helperService.getHelper('UserHelper'),
        ]);
      const payload = await tokenService.decodeToken(jwtToken);

  if (!payload?.ckey || payload?.ckey !== clientKey) {
    response.msg = 'Invalid request.';
    const cookieHelper = await this.helperService.getHelper('CookieHelper');
      await cookieHelper.setCookie(this.configService.get('jwtCookieName'), '', {
        expires: 3600,
        path: '/',
        domain: this.configService.get('ssoCookieDomain'),
      });// controller will handle cookie deletion
      
    return response;
  }
  const user: Partial<User> = await userRepository.getUserByEmail(payload?.email);
     
      const userFields = {
        access: currentTime,
      };
      
      const userTokenDetail = await tokenService.generateSessionTokens(user, clientKey);
  if (userTokenDetail?.idToken) {
  await Promise.all([
    userRepository.findOneAndUpdate({ uid : user?.uid }, userFields),
    userHelper.updateCloud6SentinelByUidOrMail({ uid : user?.uid, ...userFields})
  ])
  return { type: 'success', _t: userTokenDetail?.idToken};
 }}
    
      else {
        response.msg = 'please provide valid token'
        return response
      }
    } catch (error: any) {
      Logger.error('validateToken', {
        METHOD: this.constructor.name + '@validateToken',
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
      });
      response.msg = error.message;
      return response;
    }
  }

  async saveProfileAndFormData(
    updateDetails: {
      btnSignup?: string;
      userId?: number;
      userEmail?: string;
      isB2bStudent?: boolean;
    },
    inputParams: any
  ) {
  	const response = {
  		type: 'error',
  		msg: 'Invalid Request.'
  	}
  	try {
  		// Use updateDetails if needed in the future
  		delete inputParams.module;
  		delete inputParams.controller;
  		delete inputParams.action;
  		delete inputParams.q;

  		const [userRepository, userApiV1Helper, profileHelper, lrsInstanse, userHelper] = await Promise.all([
  			this.helperService.get < IUserRepository > (UserRepository),
  			this.helperService.getHelper('UserApiV1Helper'),
  			this.helperService.getHelper('ProfileHelper'),
  			this.helperService.getHelper('lrsHelper'),
        this.helperService.getHelper('UserHelper')
  		]);
  		const user = await userRepository.findByUID(updateDetails?.userId);
  		if (!user) {
  			throw new BadRequestException('UserNotFoundException');
  		}
  		let profileUpdateObj = {};
  		let postKafkaObj = {};

  		// Validate lgid logic
  		let lgid = 0
  		if (user?.user_groups.length) {
  			lgid = user?.user_groups.length === 1 ? user?.user_groups[0] : Math.max(...user?.user_groups);
  		}
  		// below need to verify "Profile Details" or "profile_details"
  		const profile = await userApiV1Helper.getLmsSetting(this.configService.get('profileLmsSettingIdentifier'), lgid || 0) || 'yes';
  		const showProfile = updateDetails?.isB2bStudent && !(!Utility.isEmpty(profile) && profile.trim().toLowerCase() !== 'no');
  		let updateResponse;
  		switch (updateDetails.btnSignup) {
  			case 'basic':
  				updateResponse = await this.handleBasic(
  					inputParams,
  					updateDetails,
  					showProfile,
  					profileUpdateObj,
  					postKafkaObj,
  					userApiV1Helper,
  				)
  				break;
  			case 'contact':
  				updateResponse = await this.handleContact(
  					inputParams,
  					updateDetails,
  					showProfile,
  					profile,
  					lgid,
  					profileUpdateObj,
  					postKafkaObj,
  					userApiV1Helper,
  				)
  				break;
  			case 'outcome':
  				updateResponse = await this.handleOutcome(
  					inputParams,
  					updateDetails,
  					profileUpdateObj,
  					postKafkaObj,
  					userApiV1Helper,
  					profileHelper
  				)
  				break;
  			case 'professional':
  				updateResponse = await this.handleProfessional(
  					inputParams,
  					updateDetails,
  					profileUpdateObj,
  					postKafkaObj,
  					userApiV1Helper,
  					profileHelper
  				)
  				break;
  			case 'academics':
  				updateResponse = await this.handleAcademics(
  					inputParams,
  					updateDetails,
  					profileUpdateObj,
  					postKafkaObj,
  					userApiV1Helper,
  					profileHelper
  				)
  				break;
  			default:
  				throw new Error('Invalid btnSignup value');
  		}

  		profileUpdateObj = updateResponse.profileUpdateObj || profileUpdateObj;
  		postKafkaObj = updateResponse.postKafkaObj || postKafkaObj
  		if (profileUpdateObj) {
  			let result = await profileHelper.saveProfileData({
  				edit_type: updateDetails?.btnSignup,
  				...profileUpdateObj
  			}, user);
  			if (result) {
          // drupal profile table sync
          if (this.configService.get('enableDrupalSync')) {
             const updatedUserData = await profileHelper.fetchProfileData(updateDetails?.userId , 1);
            if(["professional", "academics", "outcome"].includes(updateDetails.btnSignup)) {
                Promise.all([
                profileHelper.syncTaxonomyDataWithMySQLDrupal(updatedUserData, [updateDetails.btnSignup]),
                userHelper.syncUserDataWithMySQLDrupal(updateDetails),
                ]);
            } else {
              userHelper.syncUserDataWithMySQLDrupal(updateDetails);
            }             
          }

  				// send to kafka
  				lrsInstanse.sendDataToKafka(this.configService.get('profileTopic'), postKafkaObj);
  				response['type'] = 'success';
  				response['msg'] = 'Your profile has been successfully updated.';
  				return response;
  			} else {
  				response['msg'] = 'Error occurred while updating profile. Please try again.';
  			}
  		}
  		return response;
  	} catch (error: any) {
  		Logger.error('saveProfileAndFormData', {
  			METHOD: this.constructor.name + '@' + this.saveProfileAndFormData.name,
  			MESSAGE: error.message,
  			REQUEST: {
  				updateDetails,
  				inputParams
  			},
  			RESPONSE: error.stack,
  			TIMESTAMP: new Date().getTime(),
  		});
  		if (error instanceof BadRequestException) {
  			response['msg'] = error.message
  		}
  		return response
  	}
  }

  async handleBasic(
  	inputParams,
  	updateDetails,
  	showProfile,
  	profileUpdateObj,
  	postKafkaObj,
  	userApiV1Helper,
  ) {
    try {
      // Handle profile picture upload if provided
      if (inputParams.profile_picture?.trim()) {
        const profileImage = inputParams.profile_picture.trim();
        const profileImageObj = await this.helperService.getHelper('UserMgmt_ProfileImage');
        let profilePic: string | null = null;

        try {
          const decodedProfilePicture = JSON.parse(profileImage);
          profilePic = decodedProfilePicture?.attachment || profileImage;
        } catch {
          profilePic = profileImage;
        }

        if (profilePic) {
          const uploadedProfile = await profileImageObj.uploadMobileUserProfilePic(updateDetails.userId, profilePic);
          if (uploadedProfile?.isFileUploaded) {
          profileUpdateObj = {
            ...profileUpdateObj,
            profile_picture: uploadedProfile.profileImage,
          };
          }
        } else {
          profileUpdateObj = {
          ...profileUpdateObj,
          profile_picture: profileImageObj.getUserProfileFile({ uid: updateDetails.userId }),
          };
        }
      }
  		let firstName = inputParams.first_name?.trim() || '';
  		let lastName = inputParams.last_name?.trim() || '';
  		let displayName = firstName;
  		let trainingFundedBy = inputParams.training_funded_by?.trim() || '';
  		if (lastName) {
  			displayName += ` ${lastName}`;
  		}


  		// the below will validate and throw the bad request error
  		await userApiV1Helper.validateEditProfileBasic(
  			inputParams,
  			showProfile,
  		);

  		// Profile pic has to be added after development is completed.
  		profileUpdateObj = {
  			...profileUpdateObj,
  			first_name: firstName,
  			last_name: lastName,
  			display_name: displayName,
  			title: inputParams.title?.trim() || '',
  			middle_name: inputParams.middle_name?.trim() || '',
  			training_funded_by: trainingFundedBy,
  			linkedin_url: inputParams.user_linkedin_url?.trim() || '',
  		};

      if(showProfile) {
          profileUpdateObj = {
            ...profileUpdateObj,
            first_name: firstName,
            last_name: lastName,
            display_name: displayName,
            title: inputParams.title?.trim() || '',
            middle_name: inputParams.middle_name?.trim() || '',
            training_funded_by: trainingFundedBy,
            linkedin_url: inputParams.user_linkedin_url?.trim() || '',
          }
       } else {
          profileUpdateObj = {
            ...profileUpdateObj,
            first_name: firstName,
            last_name: lastName,
            display_name: displayName,
            title: inputParams.title?.trim() || '',
            middle_name: inputParams.middle_name?.trim() || '',
            training_funded_by: trainingFundedBy,
            linkedin_url: inputParams.user_linkedin_url?.trim() || '',
            dob: inputParams?.dob || '',
            gender: (inputParams.gender || '').toLowerCase() === 'm'
              ? 'Male'
              : (inputParams.gender || '').toLowerCase() === 'f'
              ? 'Female'
              : (inputParams.gender || '').toLowerCase() === 'o'
              ? 'Other'
              : inputParams.gender ? inputParams.gender : '',
            }
       }  

  		// kafka payload 
  		postKafkaObj = {
  			...postKafkaObj,
  			user_email: updateDetails?.userEmail,
  			user_id: updateDetails?.userId,
  			first_name: firstName,
  			last_name: lastName,
  			display_name: displayName,
  			training_funded_by: trainingFundedBy
  		};
  		return {
  			profileUpdateObj,
  			postKafkaObj
  		};
  	} catch (error: any) {
  		Logger.error('handleBasic', {
  			METHOD: this.constructor.name + '@' + this.handleBasic.name,
  			MESSAGE: error.message,
  			REQUEST: {
  				updateDetails,
  				inputParams
  			},
  			RESPONSE: error.stack,
  			TIMESTAMP: new Date().getTime(),
  		});
  		throw error;
  	}
  }

  async handleContact(
  	inputParams,
  	updateDetails,
  	showProfile,
  	profile,
  	lgid,
  	profileUpdateObj,
  	postKafkaObj,
  	userApiV1Helper,
  ) {
  	try {
  		const validity = await userApiV1Helper.getLmsSetting(this.configService.get('phoneLmsSettingIdentifier'), lgid || 0) || 'yes';

  		// the below will validate and throw the bad request error  
  		await userApiV1Helper.validateEditProfileContact(inputParams, validity, showProfile);

  		const phoneNo = inputParams.phone_no?.trim() || 0;
  		const countryCode = inputParams.country_code?.trim() || '';

  		profileUpdateObj = {
  			...profileUpdateObj,
  			phone_no: phoneNo,
  			country_code: countryCode,
  			country_of_residence: inputParams.country_of_residence?.trim() || '',
  			state: inputParams.state?.trim() || '',
  			location: inputParams.location?.trim() || '',
  			correspondence_address: inputParams.correspondence_address?.trim() || '',
  			timezone: inputParams.timezone?.trim() || '', // or fetch from API if needed
  		};
      if (Utility.isEmpty(inputParams.timezone)) {
        const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
        profileUpdateObj.timezone = await userMgmtUtilityHelperInstance.getTimezoneFromCountryCode(countryCode);
      }

  		// kafka payload
  		postKafkaObj = {
  			...postKafkaObj,
  			user_email: updateDetails?.userEmail,
  			user_id: updateDetails?.userId,
  			timezone: profileUpdateObj.timezone
  		};

  		if (updateDetails.isB2bStudent && !(!Utility.isEmpty(profile) && profile.trim().toLowerCase() !== 'no')) {
  			if (!Utility.isEmpty(validity) && validity.toLowerCase() !== 'no') {
  				profileUpdateObj = {
  					...profileUpdateObj,
  					phone_no: phoneNo,
  					country_code: countryCode,
  				};

  				// kafka payload
  				postKafkaObj['country'] = countryCode;
  				postKafkaObj['mobile_no'] = phoneNo;
  			}
  		} else {
  			if (
  				!updateDetails.isB2bStudent ||
  				(!Utility.isEmpty(validity) && validity.toLowerCase() !== 'no')
  			) {
  				profileUpdateObj = {
  					...profileUpdateObj,
  					phone_no: phoneNo,
  					country_code: countryCode,
  				};

  				// kafka payload
  				postKafkaObj['country'] = countryCode;
  				postKafkaObj['mobile_no'] = phoneNo;
  			}
  		}
  		return {
  			profileUpdateObj,
  			postKafkaObj
  		};
  	} catch (error: any) {
  		Logger.error('handleContact', {
  			METHOD: this.constructor.name + '@' + this.handleContact.name,
  			MESSAGE: error.message,
  			REQUEST: {
  				updateDetails,
  				inputParams,
  			},
  			RESPONSE: error.stack,
  			TIMESTAMP: new Date().getTime(),
  		});
  		throw error;
  	}
  }

  async handleOutcome(
  	inputParams,
  	updateDetails,
  	profileUpdateObj,
  	postKafkaObj,
  	userApiV1Helper,
  	profileHelper
  ) {
  	try {

  		// the below will validate and throw the bad request error  
  		await userApiV1Helper.validateEditProfileOutcome(inputParams);
  		// removed usage of "interests"

  		const objectiveData = inputParams?.objective_of_taking_course?.trim();
  		const validobjectiveData = await profileHelper.getOneTaxonomy(objectiveData);
  		if (objectiveData && validobjectiveData) {
  			profileUpdateObj = {
  				...profileUpdateObj,
  				objective_taking_course: objectiveData,
  			};

  			// kafka payload
  			postKafkaObj['objective_of_training'] = objectiveData;
  		}

  		// kafka payload
  		if (Object.keys(postKafkaObj).length > 0) {
  			postKafkaObj['user_email'] = updateDetails.userEmail;
  			postKafkaObj['user_id'] = updateDetails.userId;
  		}
  		return {
  			profileUpdateObj,
  			postKafkaObj
  		};
  	} catch (error: any) {
  		Logger.error('handleOutcome', {
  			METHOD: this.constructor.name + '@' + this.handleOutcome.name,
  			MESSAGE: error.message,
  			REQUEST: {
  				updateDetails,
  				inputParams,
  			},
  			RESPONSE: error.stack,
  			TIMESTAMP: new Date().getTime(),
  		});
  		throw error;
  	}
  }

  async handleProfessional(
  	inputParams,
  	updateDetails,
  	profileUpdateObj,
  	postKafkaObj,
  	userApiV1Helper,
  	profileHelper
  ) {
  	try {
  		const validateProfessionalResult = await userApiV1Helper.validateEditProfileProfessional(inputParams);
  		if (validateProfessionalResult !== true && Array.isArray(validateProfessionalResult)) {
  			return {
  				type: 'error',
  				status: false,
  				msg: validateProfessionalResult
  			};
  		}

  		const companyName = JSON.parse(inputParams.company_name) || [];
  		const designation = JSON.parse(inputParams.company_designation) || [];
  		const industryData = JSON.parse(inputParams.industry) || [];
  		const jobFunctionData = JSON.parse(inputParams.job_function) || [];
  		const currentRoleStatus = inputParams?.current_company?.[0] === 'on'

  		const processedCurentRoleList = industryData.map(() => '0')

  		// validate after the actual payload
  		if (currentRoleStatus) {
  			const index = inputParams?.exp_to_month.indexOf("");
  			if (index !== -1 && index === inputParams?.exp_to_year.indexOf("")) {
  				processedCurentRoleList[index] = '1';
  			}
  		}

  		profileUpdateObj = {
  			...profileUpdateObj,
  			company_designation: designation,
  			company_name: companyName,
  			job_function: jobFunctionData,
  			industry: industryData,
  			exp_from_month: JSON.parse(inputParams.exp_from_month) || [],
  			exp_from_year: JSON.parse(inputParams.exp_from_year) || [],
  			current_role: processedCurentRoleList,
  			exp_to_month: JSON.parse(inputParams.exp_to_month) || [],
  			exp_to_year: JSON.parse(inputParams.exp_to_year) || [],
  			edit_type: updateDetails?.btnSignup,
  		}

  		if (inputParams?.where_are_you_in_career) {
  			let TaxonomyDetails = await profileHelper.getOneTaxonomy(inputParams?.where_are_you_in_career)
  			profileUpdateObj = {
  				...profileUpdateObj,
  				where_are_you_in_career: (TaxonomyDetails._id).toString()
  			};
  		}

  		postKafkaObj = {
  			user_email: updateDetails.userEmail,
  			user_id: updateDetails.userId,
  			...(designation[0] && {
  				designation: designation[0]
  			}),
  			...(companyName[0] && {
  				company: companyName[0]
  			}),
  			...(jobFunctionData[0] && {
  				department: jobFunctionData[0]
  			}),
  			...(industryData[0] && {
  				industry: industryData[0]
  			}),
  		};
  		return {
  			profileUpdateObj,
  			postKafkaObj
  		};
  	} catch (error: any) {
  		Logger.error('handleProfessional', {
  			METHOD: this.constructor.name + '@' + this.handleProfessional.name,
  			MESSAGE: error.message,
  			REQUEST: {
  				updateDetails,
  				inputParams,
  			},
  			RESPONSE: error.stack,
  			TIMESTAMP: new Date().getTime(),
  		});
  		throw error;
  	}
  }

  async handleAcademics(
  	inputParams,
  	updateDetails,
  	profileUpdateObj,
  	postKafkaObj,
  	userApiV1Helper,
  	profileHelper
  ) {
  	try {
  		const validateAcademicsResult = await userApiV1Helper.validateEditProfileAcademics(inputParams);
  		if (validateAcademicsResult !== true && Array.isArray(validateAcademicsResult)) {
  			return {
  				type: 'error',
  				status: false,
  				msg: validateAcademicsResult
  			};
  		}

  		const instituteName = JSON.parse(inputParams.institute_name) || [];
  		const specialization = JSON.parse(inputParams.field_specialization) || [];


  		profileUpdateObj = {
  			field_qualification: JSON.parse(inputParams.field_qualification) || [],
  			institute_name: instituteName,
  			field_specialization: specialization,
  			course_from_month: JSON.parse(inputParams.course_from_month) || [],
  			course_from_year: JSON.parse(inputParams.course_from_year) || [],
  			course_to_month: JSON.parse(inputParams.course_to_month) || [],
  			course_to_year: JSON.parse(inputParams.course_to_year) || [],
  			edit_type: updateDetails?.btnSignup,
  		}

  		if (inputParams?.highest_level_of_education) {
  			let TaxonomyDetails = await profileHelper.getOneTaxonomy(inputParams?.highest_level_of_education)
  			profileUpdateObj = {
  				...profileUpdateObj,
  				highest_level_of_education: (TaxonomyDetails._id).toString()
  			}
  		}

  		postKafkaObj = {
  			user_email: updateDetails.userEmail,
  			user_id: updateDetails.userId,
  			...(specialization[0] && {
  				specialization: specialization[0]
  			}),
  			...(instituteName[0] && {
  				college: instituteName[0]
  			}),
  		};
  		return {
  			profileUpdateObj,
  			postKafkaObj
  		};
  	} catch (error: any) {
  		Logger.error('handleAcademics', {
  			METHOD: this.constructor.name + '@' + this.handleAcademics.name,
  			MESSAGE: error.message,
  			REQUEST: {
  				updateDetails,
  				inputParams,
  			},
  			RESPONSE: error.stack,
  			TIMESTAMP: new Date().getTime(),
  		});
  		throw error;
  	}
  }
  
  
  
  async updateAndFetchUserRoles(reqBody: { uid: number; roles: string[] }) {
    try {
      // Retrieve repositories
      const [roleRepository, userRepository] = await Promise.all([
        this.helperService.get<IRoleRepository>(RoleRepository),
        this.helperService.get<IUserRepository>(UserRepository),
      ]);
  
      // Fetch the user document
      const userDoc = await userRepository.findByUID(reqBody.uid);
      if (!userDoc) {
        throw new BadRequestException('UserNotFoundException');
      }
  
      const existingRoles = userDoc.roles || [];
  
      // Convert existing role objects to role IDs
      const existingRoleIds = existingRoles.map(role => role['_id'].toString());
  
      // Map incoming role IDs to strings
      const incomingRoleIds = reqBody.roles.map(id => id.toString());
  
      // Determine roles to add by comparing incoming and existing roles
      const rolesToAdd = incomingRoleIds.filter(id => !existingRoleIds.includes(id));
  
      // Fetch role documents for roles to add
      const rolesToAddDocs = await roleRepository.findAll({ _id: { $in: rolesToAdd } });
  
      // Filter out invalid roles (null values in case of invalid role IDs)
      const validRolesToAdd = rolesToAddDocs.filter(role => role);
  
      // Combine existing roles with valid new roles, filtering out removed ones
      const updatedRoles = [
        ...existingRoles.filter(role => incomingRoleIds.includes(role['_id'].toString())),
        ...validRolesToAdd,
      ];
  
      // Update the user document with the new roles
      userDoc.roles = updatedRoles;
      await userRepository.findOneAndUpdate({ uid: reqBody.uid }, { roles: updatedRoles });
  
      // Get helper service for syncing roles
      const userHelper = await this.helperService.getHelper('UserHelper');
  
      // Roles to delete from Cloud6 (those not in the incoming list)
      const deleteRoleList = existingRoles
        .filter(role => !incomingRoleIds.includes(role['_id'].toString()))
        .map(role => role.rid);

  
      // Prepare payload for syncing roles with Cloud6
      const mysqlPayload = updatedRoles.map(role => ({
        uid: userDoc.uid,
        rid: role.rid,
      }));
      await Promise.all([
        deleteRoleList.length > 0
          ? userHelper.clearCloud6SentinelUserRole(userDoc.uid, deleteRoleList)
          : Promise.resolve(),
        userHelper.syncCloud6SentinelUserRole(mysqlPayload),
      ]);
    
      if (this.configService.get('enableDrupalSync')) {
        const rolesObject: Record<string, string> = {};
        for (const role of updatedRoles) {
          if (role.rid && role.roleName) {
            rolesObject[role.rid.toString()] = role.roleName;
          }
        }
        const userHelper = await this.helperService.getHelper('UserHelper');
        const syncUserPayload = {
          uid: userDoc.uid,
          roles: rolesObject,
        };
        await userHelper.syncUserDataWithMySQLDrupal(syncUserPayload);
      }
      
      // Return the updated role IDs
      return updatedRoles.map(role => role['_id'].toString());
  
    } catch (error: any) {
      // Log error with more context
      Logger.error('updateAndFetchUserRoles', {
        METHOD: `${this.constructor.name}@updateAndFetchUserRoles`,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;  // Propagate the error to be handled by the caller
    }
  }
  
  async removeUserRoles(reqBody) {
    try {

      const [roleRepository, userRepository, userHelper] = await Promise.all([
        this.helperService.get<IRoleRepository>(RoleRepository),
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
      ]);
      // Check if uid and rid are valid
      const [user, role] = await Promise.all([
        userRepository.findOne({ uid: reqBody.uid }),
        roleRepository.findAll({ rid: reqBody.rid }),
      ]);

      if (!user || !role) {
        return false;
      }

      //mapping the object Id to string
      const deleteRoleIds = role.map((role) => role['_id'].toString());
      
      //filtering the user roles by removing the deleteRoleIds
      const updatedRoles = user.roles
        .map((role) => role['_id'].toString())
        .filter((roleId) => !deleteRoleIds.includes(roleId));

        //update Database
        const updatedResult = await userHelper.removeRoles(reqBody.uid, reqBody.rid, updatedRoles); 
        if (updatedResult == false) {
          return false;
        }
        if (this.configService.get('enableDrupalSync')) {
          // Get full role info to prepare Drupal roleMap (rid => roleName)
          const remainingRoles = updatedRoles.length > 0
          ? await roleRepository.findAll({ _id: { $in: updatedRoles } })
          : [];
          const roleMap = {};
          for (const role of remainingRoles) {
            roleMap[role.rid] = role.roleName;
          }
          const updatedUser = await userRepository.findByUID( reqBody.uid );
          await userHelper.syncUserDataWithMySQLDrupal({
            uid: updatedUser.uid,
            roles: roleMap,
          });
        }
        return true;  
    } catch (error: any) {
      Logger.error('removeUserRoles', {
        METHOD: this.constructor.name + '@' + this.removeUserRoles.name,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  // Creates a new role in both MySQL and MongoDB after validating non-existence
  async createRole(roleDto: RoleDto): Promise<any> {
    try{
    const { rid, roleName } = roleDto;
    const [roleRepository, userHelper] = await Promise.all([
      this.helperService.get<IRoleRepository>(RoleRepository), 
      this.helperService.getHelper("UserHelper"),
    ]);
    // Check for existing role 
    const existingRole = await roleRepository.findOne({ where: { rid } });
    if (existingRole) {
      throw new Error('Role already exists');
    }
    // Create role in both MySQL and MongoDB
    const [ mysqlSave , mongoSave] = await Promise.all([
      userHelper.createCloud6SentinelRole({ rid, name: roleName }),
      roleRepository.saveRoles({ rid, roleName })
    ]);

    // Return saved role if both operations succeeded
    if(mysqlSave && mongoSave.rid) {
      return {
        rid: mongoSave.rid, 
        roleName: mongoSave.roleName, 
      };
    } else {
      throw new BadRequestException("Something went wrong.")
    }
  } catch (error: any) {
    Logger.error('createRole', {
      METHOD: this.constructor.name + '@' + this.createRole.name,
      MESSAGE: error.message,
      REQUEST: roleDto,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });
    return false;
  }}

// Updates an existing role in both MySQL and MongoDB
  async updateRole(roleDto: RoleDto): Promise<any> {
    try{
    const { rid, roleName } = roleDto;
    
    const [roleRepository, userHelper] = await Promise.all([
      this.helperService.get<IRoleRepository>(RoleRepository),
      this.helperService.getHelper("UserHelper"),
    ]);
    const existingRole = await roleRepository.findOne({rid });
    if (!existingRole) {
      throw new Error('Role does not exist');
    }
    
    // Update the role in both MySQL and MongoDB
    const [ mysqlSave , mongoSave] = await Promise.all([
      userHelper.updateCloud6SentinelRole({ rid, name: roleName }),
      roleRepository.updateRole({ rid }, { roleName })
    ])

    // Return updated role if both operations succeeded
    if(mysqlSave && mongoSave.rid) {
      return {
        rid: mongoSave.rid, 
        roleName: mongoSave.roleName, 
      };
    } else {
      throw new BadRequestException("Something went wrong.")
    };
  } catch (error: any) {
    Logger.error('updateRole', {
      METHOD: this.constructor.name + '@' + this.updateRole.name,
      MESSAGE: error.message,
      REQUEST: roleDto,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });
    return false;
  }}
  
}
