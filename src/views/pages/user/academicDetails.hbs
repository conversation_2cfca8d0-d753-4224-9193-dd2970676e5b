<script>
  const selectedEducationId = "{{selectedEducationId}}";
    const academicExperience = {{{ safeJson academicExperience }}};
</script>
<div class="tab_inner academics_tab tab-pane active" id="academics_tab">
    <form name="form_academics_info" id="form_academics_info" class="edit_form track_changes" method="post">
        <div class="form_details">
            <div class="form_info">
                <h3 id="academics_details_label">Academics</h3>
            </div>

            <div class="form-wrap" id ="js_academic_exp">
                <div class="frm_abel">
                    <label for="highest_level_of_education">Highest level of education<i>*</i></label>
                </div>
                <div class="right-col">
                    <select id="highest_level_of_education" name="highest_level_of_education"
                        class="custom_select1 qualification_details input_field_highest_level_of_education" 
                        required aria-required="true" aria-labelledby="academics_details_label">
                    </select>
                    <p class="p_error error hide">Required</p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">Academic Experience(s)</div>
                <div class="right-col">
                    <a id="add_academics_exp" href="javascript:void(0)" class="add_plus" role="button" aria-label="Add academic experience">
                        <i>+</i>Add
                    </a>
                </div>
            </div>

            <div class="work-info" id="js_div_qualification">
                {{#each academicExperience}}
                <div class="form-wrap exp_main_wrap academic" id="exp_main_wrap_i_{{@index}}">
                    <div class="exp-inner-wrap">
                        <div class="frm_abel exp_label"><b>Academic Experience <font class="academics_exp_box_counter">{{increment @index}}</font></b></div>
                        <div class="right-col">
                            <a href="javascript:void(0)" class="delete" role="button" 
                               onclick="removeAcademics('exp_main_wrap_i_{{@index}}');"
                               aria-label="Delete academic experience {{increment @index}}">
                                Delete
                            </a>
                        </div>
                    </div>

                    <div class="exp-inner-wrap">
                        <div class="frm_abel">
                            <label for="field_qualification_{{@index}}">Qualification<i>*</i></label>
                        </div>
                        <div class="right-col">
                            <select id="field_qualification_{{@index}}" name="field_qualification[]" 
                                    class="field_qualification_populate custom_select1 qualification_details input_field_field_qualification" 
                                    required aria-required="true">
                            </select>
                            <p class="p_error error hide">Required</p>
                        </div>
                    </div>

                    <div class="exp-inner-wrap">
                        <div class="frm_abel">
                            <label for="institute_name_{{@index}}">School/College<i>*</i></label>
                        </div>
                        <div class="right-col">
                            <input type="text" id="institute_name_{{@index}}" name="institute_name[]" 
                                   class="institute_name input_field_institute_name qualification_details"
                                   value="{{this.institute}}" required aria-required="true"/>
                            <p class="p_error error hide">Required</p>
                        </div>
                    </div>

                    <div class="exp-inner-wrap">
                        <div class="frm_abel">
                            <label for="field_specialization_{{@index}}">Specialization<i>*</i></label>
                        </div>
                        <div class="right-col">
                            <input type="text" id="field_specialization_{{@index}}" name="field_specialization[]" 
                                   class="field_specialization input_field_field_specialization qualification_details"
                                   value="{{this.specialization}}" required aria-required="true"/>
                            <p class="p_error error hide">Required</p>
                        </div>
                    </div>

                    <div class="exp-inner-wrap select_dates">
                        <div class="frm_abel" for="a_from_{{@index}}" >Start Date<i>*</i></div>
                        <div class="right-col">
                            <div class="flex-date">
                                <div class="select_wraper month">
                                    <select name="course_from_month[]" id="a_from_month_{{@index}}" class="course_from_month qualification_details input_field_course_from_month" required>
                                    </select>
                                </div>
                                <div class="select_wraper year">
                                    <select name="course_from_year[]" id="a_from_year_{{@index}}" class="course_from_year qualification_details input_field_course_from_year" required>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="exp-inner-wrap select_dates">
                        <div class="frm_abel" for="a_to_{{@index}}">End Date<i>*</i>
                            <span>If you are still studying, please enter your expected graduation date</span>
                        </div>
                        <div class="right-col">
                            <div class="flex-date">
                                <div class="select_wraper month">
                                    <select name="course_to_month[]" id="a_to_month_{{@index}}" class="course_to_month qualification_details input_field_course_to_month">
                                    </select>
                                </div>
                                <div class="select_wraper year">
                                    <select name="course_to_year[]" id="a_to_year_{{@index}}" class="course_to_year qualification_details input_field_course_to_year">
                                    </select>
                                </div>
                            </div>
                            <p class="p_error error hide">Required</p>
                            <p class="academics_exp_err error hide"></p>
                        </div>
                    </div>
                </div>
                {{/each}}
            </div>

            <div class="form-wrap">
                <div class="frm_abel"></div>
                <div class="right-col">
                    <div class="btn-group">
                        <input type="hidden" name="edit_type" class="edit_type" value="academics">
                        <button class="btn discard" type="button" disabled aria-disabled="true">Discard</button>
                        <button class="btn save" disabled aria-disabled="true">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
