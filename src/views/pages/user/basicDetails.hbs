<div class="tab_inner basic_tab tab-pane active" id="basic_tab">
    <form name="form_basic_details" id="form_basic_details" class="edit_form track_changes" method="post" role="form" aria-labelledby="basic_details_heading" enctype="multipart/form-data">
        <div class="form_details">
            <div class="form_info">
                <h3 id="basic_details_heading">Basic details</h3>
                <span>The following details will be required for issuing certificates. Please enter them carefully.</span>
            </div>

            <div class="form-wrap">
                <div class="frm_abel" id="title_label">Title<i>*</i></div>
                <div class="right-col">
                    <select name="title" id="title" class="custom_select1 basic_details input_field_title" aria-labelledby="title_label">
                    </select>
                    <p class="p_error error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel" id="first_name_label">First name<i>*</i></div>
                <div class="right-col">
                    <input type="text" id="first_name" name="first_name" value="{{userData.first_name}}" class="input_field_first_name basic_details" aria-labelledby="first_name_label" required />
                    <p class="p_error error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">Middle name</div>
                <div class="right-col">
                    <input type="text" id="middle_name" name="middle_name" value="{{userData.middle_name}}" class="input_field_middle_name" />
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel" id="last_name_label">Last name<i>*</i></div>
                <div class="right-col">
                    <input type="text" id="last_name" name="last_name" value="{{userData.last_name}}" class="input_field_last_name basic_details" aria-labelledby="last_name_label" required />
                    <p class="p_error error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel" id="gender_label">Gender<i>*</i></div>
                <div class="right-col">
                    <select name="gender" id="gender" class="input_field_gender custom_select1 basic_details" aria-labelledby="gender_label">
                    </select>
                    <p class="p_error error_image error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">Date of birth<i>*</i>
                    <span id="dob_help_text">Safely used to understand your learning profile. We respect and protect your privacy.</span>
                </div>
                <div class="right-col date-picker">
                    <input type="text" id="datepicker" name="dob" value="{{timestampToDate userData.dob}}" class="input_field_dob basic_details" placeholder="DD/MM/YYYY" aria-describedby="dob_help_text" required />
                    <i class="dob_icon"></i>
                    <p class="p_error error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">Upload your picture</div>
                <div class="right-col profile-view">
                    <img id="profile_picture" class="profile_picture" src="{{fullProfilePicUrl userData.profile_pic.filename}}" alt="Profile Picture">
                    <input type="file"  id="file_input"  class="hide" name="profile_pic"  accept=".png, .jpg, .jpeg, .gif">
                    <div class="prof-name">
                        <div id="change_picture" class="change_picture"><a href="javascript:void(0)">Change picture</a></div>
                        {{#if userData.profile_pic.filename}}
                        <a href="javascript:void(0)" id="delete_picture">Remove</a>
                        {{/if}}
                    </div>
                    <p class="p_error error_image error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">LinkedIn profile link
                    <span>We recommend creating a LinkedIn account in case you have not created one yet.</span>
                </div>
                <div class="right-col linkedin_profile">
                    <input type="text" id="user_linkedin_url" name="linkedin_url" value="{{userData.linkedin_url}}" class="input_field_user_linkedin_url basic_details" placeholder="Your LinkedIn Profile URL">
                    <p class="p_error error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">How is your training funded?<i>*</i></div>
                <div class="right-col">
                    <div class="radio_wrap">
                        <input type="radio" id="self" name="training_funded_by" value="self" class="input_field_training_funded_by basic_details" aria-labelledby="self_funding" checked>
                        <label id="self_funding" for="self">Self</label>
                    </div>
                    <div class="radio_wrap">
                        <input type="radio" id="organisation" name="training_funded_by" value="organisation" class="input_field_training_funded_by basic_details" aria-labelledby="org_funding">
                        <label id="org_funding" for="organisation">Organisation</label>
                    </div>
                    <p class="p_error error hide"></p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel"></div>
                <div class="right-col">
                    <div class="btn-group">
                        <input type="hidden" name="edit_type" class="edit_type" value="basic">
                        <button class="btn discard" type="button" disabled>Discard</button>
                        <button class="btn save" disabled>Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
