import { Body, Controller, Inject, Post, UseFilters } from "@nestjs/common";
import { UserDataSyncService } from "../services/user-data-sync.service"
import { APILog } from "../../logging/logger";
import { HttpExceptionFilter } from "../../common/filters/http.exception.filter";

@Controller("internal/user-data-sync")
export class UserDataSyncController {
    @Inject(UserDataSyncService) private readonly userDataSyncService: UserDataSyncService;

    @Post("/sync-user-data-to-sentinel")
    @UseFilters(HttpExceptionFilter)
    async syncUserDataToSentinel(@Body() userParams) {
     try {
        const result = await this.userDataSyncService.syncUserDataToSentinel(userParams);
        if (result instanceof Error){
            return result;
        }
        return result;
     } catch(error:any) {
        APILog.error('syncUserDataToSentinel', {
            METHOD: `${this.constructor.name}@${this.syncUserDataToSentinel.name}`,
            MESSAGE: error.message,
            REQUEST: userParams,
            RESPONSE: error.stack,
            TIMESTAMP: new Date().getTime(),
        });
        return error;
     }
    }

    @Post("/sync-taxonomy-data-to-sentinel")
    @UseFilters(HttpExceptionFilter)
    async syncTaxonomyDataToSentinel(@Body() userParams) {
        try {
            const result = await this.userDataSyncService.syncTaxonomyDataToSentinel(userParams);
            if (result instanceof Error){
                return result;
            }
            return result;
        } catch(error:any) {
            APILog.error('syncTaxonomyDataToSentinel', {
                METHOD: `${this.constructor.name}@${this.syncTaxonomyDataToSentinel.name}`,
                MESSAGE: error.message,
                REQUEST: userParams,
                RESPONSE: error.stack,
                TIMESTAMP: new Date().getTime(),
            });
            return error;
        }
    }     
}