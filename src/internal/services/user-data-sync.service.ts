import { Inject } from '@nestjs/common';
import { Logger } from '../../logging/logger';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import * as drupalHash from 'drupal-hash';
import { UserSocial } from '../../db/mongo/schema/user/social.schema';

export class UserDataSyncService {
    @Inject(HelperService) private readonly helperService: HelperService;
    async syncUserDataToSentinel(userParams) {
        try {
            let response = { type: "error", msg: "Invalid Request" };
            let hashPassword;

            const [userRepository, userMgmtHelper, userHelper] = await Promise.all([
                this.helperService.getHelper<IUserRepository>(UserRepository),
                this.helperService.getHelper('UserMgmtUtilityHelper'),
                this.helperService.getHelper('UserHelper'),
            ]);

            //Prepare user social data
            let userSocialData: UserSocial[];
            if (userParams?.user_social_data) {
                let formatedUserData = Array.isArray(userParams?.user_social_data) ? userParams?.user_social_data : [userParams?.user_social_data];
                userSocialData = formatedUserData.map((social: any) => ({
                    type: social.type || '',
                    sub: social.sub || '',
                    email: social.email_address || '',
                    source: social.source || '',
                    status: social.status || '',
                    created_on: social.created_on || '',
                }));
            } else {
                userSocialData = [];
            }
            //Checking if email already exist
            const userInfo = await userRepository.getUserByEmail(userParams?.mail);            

            //Hashing password
            if (userParams.pass !== undefined) {
                hashPassword = drupalHash.hashPassword(userParams.pass);
            }

            //Prepare user roles
            const userRoles = userParams?.roles ? await userMgmtHelper.prepareAssignRoleList(userParams?.roles) : [];

            // get all unique social data types
            if (userInfo) {
                const existingSocialDataType = userInfo?.user_social_data.map(social => social.type) || [];
                if (userInfo?.user_social_data && userSocialData.length > 0) {
                    if (existingSocialDataType.length > 0 && !existingSocialDataType.includes(userSocialData[0]?.type)) {
                        // get the existing user social data
                        const existingUserSocialData = userInfo.user_social_data || [];
                        // if existing social data is empty, keep only new social data; else merge both
                        userSocialData = existingUserSocialData.length === 0
                        ? userSocialData
                        : [...userSocialData, ...existingUserSocialData];
                    } else {
                        //if the type already exists, update the existing social data
                        const indexToUpdate = existingSocialDataType.indexOf(userSocialData[0]?.type);
                        if (indexToUpdate !== -1) {
                            userInfo.user_social_data[indexToUpdate] = {...userSocialData[0] };
                            userSocialData = userInfo.user_social_data;
                        }
                    }
                }
            }

            let userData: any = {};
            userData = {
            ...(userParams.uid && { uid: Number(userParams.uid) }),
            ...(userParams.name && { name: userParams.name }),
            ...(userParams.mail && { email: userParams.mail }),
            ...(userParams.pass && { password: hashPassword }),
            ...(userParams.field_display_name && { display_name: userParams.field_display_name }),
            ...(userParams.field_phone_no && { phone_no: userParams.field_phone_no }),
            ...(userParams.field_country_code && { country_code: userParams.field_country_code }),
            ...(userParams.field_city_id && { location: userParams.field_city_id }),
            ...(userParams.field_accept_agreement && { accept_agreement: Boolean(userParams.field_accept_agreement) }),
            ...(userParams.field_user_options && { user_options: Number(userParams.field_user_options) }),
            ...(userParams.language && { language: userParams.language }),
            ...(userParams.status && { status: Number(userParams.status) }),
            ...(userParams.field_account_setup && { account_setup: Number(userParams.field_account_setup) }),
            ...(userParams.timezone && { timezone: userParams.timezone }),
            ...(userParams.field_password_created && { password_created: Number(userParams.field_password_created) }),
            ...(userParams.created && { created: Number(userParams.created) }),
            ...(userParams.signature !== undefined && { signature: userParams.signature }),
            ...(userParams.access !== undefined && { access: userParams.access !== '0' ? userParams.access : '' }),
            ...(userParams.login !== undefined && { login: userParams.login !== '0' ? userParams.login : '' }),
            ...(userParams.field_profile_visibility && { profile_visibility: userParams.field_profile_visibility }),
            ...(userParams.field_newsletter && { newsletter: Number(userParams.field_newsletter) }),
            ...(userParams.field_user_category && { user_category: userParams.field_user_category }),
            ...(userParams?.roles && { roles: userRoles }),
            ...(userParams.user_groups && { user_groups: userParams.user_groups ? userParams.user_groups : [] }),
            };

            if (userSocialData && userSocialData.length > 0) {
                userData = {
                    ...userData,
                    user_social_data: userSocialData,
                }
            }
            //Save user data to MongoDB
            const createUser: any = await userRepository.upsert(userData);
            if (createUser && createUser.email) {
                if (userData.password !== undefined ) {
                    userData.pass = userData.password;
                    delete userData.password;
                }
                if(userData.email !== undefined) {
                    userData.mail = userData.email;
                    delete userData.email;
                }
                if(userData.signature !== undefined) {
                    delete userData.signature;
                }
                if(userData.roles) {
                    delete userData.roles;
                }                
                if(userData.user_groups) {
                    delete userData.user_groups;
                }
                if(userData.user_social_data) {
                    delete userData.user_social_data;
                }                

                //Sync user data to Sentinel users table CREATE/UPDATE
                let cUser;
                if(!userInfo) {
                    // If user does not exist, create a new user in Sentinel
                    cUser = await userHelper.createCloud6SentinelUser(userData);
                } else {
                    // If user exists, update the existing user in Sentinel
                    cUser = await userHelper.updateCloud6SentinelByUidOrMail(userData);
                }


                // mysql sync action check
                if (!cUser) {
                    // If user creation or update in Sentinel fails, delete the user from MongoDB
                    Logger.error('Failed to sync with Sentinel database', {
                        METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                        MESSAGE: 'Failed to create or update user in Sentinel Mysql',
                        REQUEST: userData,
                        RESPONSE: cUser,
                        TIMESTAMP: new Date().getTime(),
                    });
                    response.msg = "Failed to sync with Sentinel database";
                    return response;
                } else {
                    response.type = "success";
                    response.msg = "User Sync successful with Sentinel";
                }

                // updating user roles
                if (userParams?.roles) {
                    // Sync user roles to sentinel users roles table
                    const createRoleObjArray = await userMgmtHelper.prepareSentinelUserRoleList(userRoles, cUser?.uid);
                    await userHelper.syncCloud6SentinelUserRole(createRoleObjArray);
                }
            }
            return response;
        } catch (error: any) {
            Logger.error('syncUserDataToSentinel', {
                METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                MESSAGE: error.message,
                REQUEST: userParams,
                RESPONSE: error.stack,
                TIMESTAMP: new Date().getTime(),
            });
            return error;
        }
    }

    async syncTaxonomyDataToSentinel(taxonomyParams) {
        try {
            const profileHelper = await this.helperService.getHelper('ProfileHelper');

            // Prepare academic update object
            let academicUpdateObj: any = {
                ...(taxonomyParams.field_qualification && { field_qualification: JSON.parse(taxonomyParams.field_qualification) || [] }),
                ...(taxonomyParams.institute_name && { institute_name: JSON.parse(taxonomyParams.institute_name) || [] }),
                ...(taxonomyParams.field_specialization && { field_specialization: JSON.parse(taxonomyParams.field_specialization) || [] }),
                ...(taxonomyParams.course_from_month && { course_from_month: JSON.parse(taxonomyParams.course_from_month) || [] }),
                ...(taxonomyParams.course_from_year && { course_from_year: JSON.parse(taxonomyParams.course_from_year) || [] }),
                ...(taxonomyParams.course_to_month && { course_to_month: JSON.parse(taxonomyParams.course_to_month) || [] }),
                ...(taxonomyParams.course_to_year && { course_to_year: JSON.parse(taxonomyParams.course_to_year) || [] }),
                edit_type: 'academics',
            };

            // Add highest_level_of_education if present
            if (taxonomyParams?.highest_level_of_education) {
                const taxonomyDetails = await profileHelper.getOneTaxonomy(taxonomyParams?.highest_level_of_education);
                if(taxonomyDetails){
                    academicUpdateObj = {
                        ...academicUpdateObj,
                        highest_level_of_education: (taxonomyDetails._id).toString(),
                    };                    
                }
            }

            // Determine current role status
            const processedCurentRoleList = JSON.parse(taxonomyParams.company_name).map(() => '0');
            const index = taxonomyParams?.exp_to_month.indexOf("");
            if (index !== -1 && index === taxonomyParams?.exp_to_year.indexOf("")) {
                processedCurentRoleList[index] = '1';
            }

            // Prepare professional update object
            let proffestionalUpdateObj: any = {
                ...(taxonomyParams.company_name && { company_name: JSON.parse(taxonomyParams.company_name) || [] }),
                ...(taxonomyParams.company_designation && { company_designation: JSON.parse(taxonomyParams.company_designation) || [] }),
                ...(taxonomyParams.job_function && { job_function: JSON.parse(taxonomyParams.job_function) || [] }),
                ...(taxonomyParams.industry && { industry: JSON.parse(taxonomyParams.industry) || [] }),
                ...(taxonomyParams.exp_from_month && { exp_from_month: JSON.parse(taxonomyParams.exp_from_month) || [] }),
                ...(taxonomyParams.exp_from_year && { exp_from_year: JSON.parse(taxonomyParams.exp_from_year) || [] }),
                ...(taxonomyParams.exp_to_month && { exp_to_month: JSON.parse(taxonomyParams.exp_to_month) || [] }),
                ...(taxonomyParams.exp_to_year && { exp_to_year: JSON.parse(taxonomyParams.exp_to_year) || [] }),
                current_role: processedCurentRoleList,
                edit_type: 'professional',
            };

            // Add where_are_you_in_career if present
            if (taxonomyParams?.where_are_you_in_career) {
                const taxonomyDetails = await profileHelper.getOneTaxonomy(taxonomyParams?.where_are_you_in_career);
                if(taxonomyDetails){
                    proffestionalUpdateObj = {
                        ...proffestionalUpdateObj,
                        where_are_you_in_career: (taxonomyDetails._id).toString(),
                    };                    
                }
            }

            // Prepare outcome update object if objective_of_taking_course is present
            let outcomeUpdateObj: any = {};
            if (taxonomyParams?.objective_of_taking_course?.trim()) {
                const validobjectiveData = await profileHelper.getOneTaxonomy(taxonomyParams?.objective_of_taking_course?.trim());
                if (validobjectiveData) {
                    outcomeUpdateObj = {
                        objective_taking_course: (validobjectiveData._id).toString(),
                        edit_type: 'outcome',
                    };
                }
            }

            // Save updates
            const updatePromises = [];
            if (academicUpdateObj && Object.keys(academicUpdateObj).length > 0) {
                updatePromises.push(profileHelper.saveProfileData(academicUpdateObj, { uid: taxonomyParams?.uid }));
            }
            if (proffestionalUpdateObj && Object.keys(proffestionalUpdateObj).length > 0) {
                updatePromises.push(profileHelper.saveProfileData(proffestionalUpdateObj, { uid: taxonomyParams?.uid }));
            }
            if (outcomeUpdateObj && Object.keys(outcomeUpdateObj).length > 0) {
                updatePromises.push(profileHelper.saveProfileData(outcomeUpdateObj, { uid: taxonomyParams?.uid }));
            }
            const updateResults = await Promise.all(updatePromises);
            const [academicUpdate, professionalUpdate, outcomeUpdate] = [
                updateResults[0],
                updateResults[1],
                updateResults[2],
            ];
            return { academicUpdate, professionalUpdate, outcomeUpdate };
        } catch (error: any) {
            Logger.error('syncTaxonomyDataToSentinel', {
                METHOD: this.constructor.name + '@' + this.syncTaxonomyDataToSentinel.name,
                MESSAGE: error.message,
                REQUEST: taxonomyParams,
                RESPONSE: error.stack,
                TIMESTAMP: new Date().getTime(),
            });
            return error;
        }
    }
}