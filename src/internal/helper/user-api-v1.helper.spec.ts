import { Test, TestingModule } from '@nestjs/testing';
import { UserApiV1Helper } from './user-api-v1.helper';
import { IUserRepository } from '../../user/repositories/user/user.repository';
import { IRoleRepository } from '../../user/repositories/role/role.repository';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { User } from '@node-saml/passport-saml/lib/types';

describe('UserApiV1Helper', () => {
  let userApiV1Helper: UserApiV1Helper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserApiV1Helper,
        {
          provide: 'IUserRepository',
          useValue: {
            findByUID: jest.fn(),
            findOneAndUpdate: jest.fn(),
          },
        },
        {
          provide: 'IRoleRepository',
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: HelperService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    userApiV1Helper = module.get<UserApiV1Helper>(UserApiV1Helper);
  });

  it('should be defined', () => {
    expect(userApiV1Helper).toBeDefined();
  });

  describe('updateEngagexRole', () => {
    it('should throw BadRequestException for invalid role', async () => {
      jest.spyOn(userApiV1Helper['configService'], 'get').mockReturnValueOnce(['role1', 'role2']);

      await expect(userApiV1Helper.updateEngagexRole('userId', 'invalidRole')).rejects.toThrowError(
        BadRequestException,
      );
    });

    // Add more test cases as needed
  });
});
