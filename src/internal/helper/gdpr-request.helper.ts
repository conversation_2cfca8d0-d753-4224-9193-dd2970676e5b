import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logger } from '../../logging/logger';
import { GdprRequest } from '../../db/mysql/entity/gdpr-request.entity'; // The entity class that maps to MySQL table
import { HelperService } from '../../helper/helper.service';

@Injectable()
export class GdprRequestHelper {
  @InjectRepository(GdprRequest) private gdprRequestRepository: Repository<GdprRequest> // TypeORM repository for MySQL
  private readonly actions = ['edit', 'export', 'delete'];
   @Inject(HelperService) private readonly helperService: HelperService;
  // Method to add GDPR request log to MySQL
  async addRequest(uid: number, action: string): Promise<number | boolean> {
    try {
      if (!uid || !action || !this.actions.includes(action)) {
        Logger.debug('addRequest', {
          METHOD: this.constructor.name + '@' + this.addRequest.name,
          MESSAGE: 'Invalid GDPR request for log entry',
          REQUEST: { uid, action },
          TIMESTAMP: new Date().getTime(),
        });
        return false;
      }
      const currentTime = Math.floor(Date.now() / 1000);
      const userHelper = await this.helperService.getHelper('UserHelper');
      const inputParams = {uid: uid,
        action: action,
        state: 'inqueue',
        requestedOn: currentTime,
        modifiedOn: currentTime,
      };
      const result = userHelper.createGdprRequest(inputParams);
      return result.id ? result.id : false; // Return the id of the saved entry if successful
    } catch (error: any) {
      Logger.error('addRequest', {
        METHOD: this.constructor.name + '@' + this.addRequest.name,
        MESSAGE: error.message,
        EXCEPTION: error.stack,
        REQUEST: { uid, action },
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  // Method to update the action status in GDPR request log in MySQL
  async updateActionStatus(uid: number, action: string, responseData: string): Promise<{
    status: string;
    msg: string;
    data: number | null;
  }> {
    const result = {
      status: 'failed',
      msg: 'Some error occurred while updating data in GDPR log table.',
      data: null,
    };

    try {
      if (!uid || !action || !responseData || !this.actions.includes(action)) {
        result.msg = 'Invalid input for updating data in GDPR log table.';
        return result;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const data = {
        state: 'done',
        result: responseData,
        modifiedOn: currentTime,
      };

      const updateResult = await this.gdprRequestRepository.update(
        { uid, action },
        data
      );

      if (updateResult.affected > 0) { // Check if any rows were updated
        return {
          status: 'success',
          msg: 'OK',
          data: updateResult.affected, // Return number of rows affected
        };
      } else {
        Logger.debug('updateActionStatus', {
          METHOD: this.constructor.name + '@' + this.updateActionStatus.name,
          MESSAGE: 'GDPR entry may not exist.',
          REQUEST: { uid, action },
          TIMESTAMP: new Date().getTime(),
        });
      }
    } catch (error: any) {
      Logger.error('updateActionStatus', {
        METHOD: this.constructor.name + '@' + this.updateActionStatus.name,
        MESSAGE: error.message,
        EXCEPTION: error.stack,
        REQUEST: { uid, action, responseData },
        TIMESTAMP: new Date().getTime(),
      });
    }

    return result;
  }
}
