import { Modu<PERSON> } from '@nestjs/common';
import { UserApiV1Controller } from './user-api-v1/user-api-v1.controller';
import { EnterpriseLmsV1Controller } from './enterprise-lms-v1/enterprise-lms-v1.controller';
import { EnterpriseLMSService } from './services/enterprise-lms.service';
import { UserApiInternalService } from './services/user.api.internal.service';
import { CachingService } from './../caching/caching.service';
import { CommunicationController } from './communication/communication.controller';
import { CommunicationService } from './services/communication.service';
import { UserDataSyncController } from './user-data-sync/user-data-sync.controller';
import { UserDataSyncService } from './services/user-data-sync.service';

@Module({
  imports: [],
  controllers: [UserApiV1Controller, EnterpriseLmsV1Controller, CommunicationController, UserDataSyncController], // Specify the controllers used in this module
  providers: [EnterpriseLMSService, UserApiInternalService, CommunicationService, CachingService, UserDataSyncService], // Specify the providers (services) used in this module
})
export class InternalModule {}