import { Test, TestingModule } from '@nestjs/testing';
import { CommunicationController } from './communication.controller';
import { CommunicationService } from '../services/communication.service';
import { HelperService } from '../../helper/helper.service';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { APILog } from '../../logging/logger';
import { BadRequestException } from '@nestjs/common';

describe('CommunicationController', () => {
  let controller: CommunicationController;
  let communicationService: CommunicationService;
  let helperService: HelperService;
  let cloud6Service: Cloud6Service;

  beforeEach(async () => {
    const mockCloud6Service = {
      getGroupNameByIds: jest.fn(),
      getUserNameByUids: jest.fn(),
      getUserTeamByEmail: jest.fn(),
      getUserGroupByEmail: jest.fn(),

    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommunicationController],
      providers: [
        {
          provide: CommunicationService,
          useValue: {
            getDisplayNamesByIds: jest.fn(),
            getUserIdByEmail: jest.fn(),
          },
        },
        {
          provide: HelperService,
          useValue: {
            get: jest.fn().mockResolvedValue(mockCloud6Service),
          },
        },
      ],
    }).compile();

    controller = module.get<CommunicationController>(CommunicationController);
    communicationService = module.get<CommunicationService>(CommunicationService);
    helperService = module.get<HelperService>(HelperService);
    cloud6Service = await helperService.get<Cloud6Service>(Cloud6Service);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getDisplayNamesByIds', () => {
    it('should return display names when communicationService resolves data', async () => {
      const reqData = { ids: ['1', '2', '3'] };
      const mockResponse = [{ uid: '1', displayName: 'John Doe' }];
      jest.spyOn(communicationService, 'getDisplayNamesByIds').mockResolvedValue(mockResponse);

      const result = await controller.getDisplayNamesByIds(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should return failure response when communicationService throws an error', async () => {
      const reqData = { ids: ['1', '2', '3'] };
      jest.spyOn(communicationService, 'getDisplayNamesByIds').mockRejectedValue(new Error('Unexpected error'));

      const result = await controller.getDisplayNamesByIds(reqData);
      expect(result).toEqual({
        status: 'failed',
        data: [],
        message: 'Some error occured while fetching the records.',
      });
    });

    it('should return empty data array when BadRequestException is thrown', async () => {
      const reqData = { ids: ['1', '2', '3'] };
      const mockError = new BadRequestException('Invalid input');

      jest.spyOn(communicationService, 'getDisplayNamesByIds').mockRejectedValue(mockError);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const result = await controller.getDisplayNamesByIds(reqData);

      expect(result).toEqual([]); // Because return result.data when it's BadRequestException
      expect(logSpy).toHaveBeenCalledWith('getDisplayNamesByIds', expect.objectContaining({
        METHOD: expect.stringContaining('getDisplayNamesByIds'),
        MESSAGE: mockError.message,
        REQUEST: reqData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

  });

  describe('getUserIdByEmail', () => {
    it('should return user IDs on successful service response', async () => {
      const reqData = { emails: ['<EMAIL>'] };
      const mockResponse = { uid: 1 };
      jest.spyOn(communicationService, 'getUserIdByEmail').mockResolvedValue(mockResponse);

      const result = await controller.getUserIdByEmail(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should return failure response when an error occurs', async () => {
      const reqData = { emails: ['<EMAIL>'] };
      jest.spyOn(communicationService, 'getUserIdByEmail').mockRejectedValue(new Error('Fetch failed'));

      const result = await controller.getUserIdByEmail(reqData);
      expect(result).toEqual({
        status: 'failed',
        data: [],
        message: 'Some error occured while fetching the records.',
      });
    });

    it('should return empty data array when BadRequestException is thrown', async () => {
      const reqData = { emails: ['<EMAIL>'] };
      const mockError = new BadRequestException('Invalid email');

      jest.spyOn(communicationService, 'getUserIdByEmail').mockRejectedValue(mockError);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const result = await controller.getUserIdByEmail(reqData);

      expect(result).toEqual([]); // Because result.data is returned in BadRequestException
      expect(logSpy).toHaveBeenCalledWith('getUserIdByEmail', expect.objectContaining({
        METHOD: expect.stringContaining('getUserIdByEmail'),
        MESSAGE: mockError.message,
        REQUEST: reqData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

  });

  describe('getGroupNamesByIds', () => {
    it('should return group names when Cloud6Service is successful', async () => {
      const reqData = { ids: ['1', '2'] };
      const mockResponse = [{ gid: '1', title: 'Group One' }];
      jest.spyOn(cloud6Service, 'getGroupNameByIds').mockResolvedValue(mockResponse);

      const result = await controller.getGroupNamesByIds(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors and log them correctly', async () => {
      const reqData = { ids: ['1', '2'] };
      jest.spyOn(cloud6Service, 'getGroupNameByIds').mockRejectedValue(new Error('Database error'));

      const result = await controller.getGroupNamesByIds(reqData);
      expect(result).toBeInstanceOf(Error);
    });
  });

  describe('getUserNamesByUids', () => {
    it('should return user names when Cloud6Service is successful', async () => {
      const reqData = { uids: ['1', '2'] };
      const mockResponse = [{ uid: '1', displayName: 'Alice' }];
      jest.spyOn(cloud6Service, 'getUserNameByUids').mockResolvedValue(mockResponse);

      const result = await controller.getUserNamesByUids(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors correctly', async () => {
      const reqData = { uids: ['1', '2'] };
      jest.spyOn(cloud6Service, 'getUserNameByUids').mockRejectedValue(new Error('Service unavailable'));

      const result = await controller.getUserNamesByUids(reqData);
      expect(result).toBeInstanceOf(Error);
    });
  });

  describe('getTeamIdByEmail', () => {
    const reqData = { email: '<EMAIL>' };

    it('should return result from cloud6Service on success', async () => {
      const mockResponse = {
        status: 'success',
        data: [{ teamId: 'T123', name: 'Engineering' }],
      };

      const mockCloud6Service = await helperService.get<Cloud6Service>(Cloud6Service);
      jest.spyOn(mockCloud6Service, 'getUserTeamByEmail').mockResolvedValue(mockResponse);

      const result = await controller.getTeamIdByEmail(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should return default failed response and log error when exception is thrown', async () => {
      const mockError = new Error('Cloud6 down');
      const expectedResponse = {
        status: 'failed',
        data: [],
        message: 'Some error occurred while fetching the records.',
      };

      const logSpy = jest.spyOn(APILog, 'error').mockImplementation(); // Mock logger

      const mockCloud6Service = await helperService.get<Cloud6Service>(Cloud6Service);
      jest.spyOn(mockCloud6Service, 'getUserTeamByEmail').mockRejectedValue(mockError);

      const result = await controller.getTeamIdByEmail(reqData);
      expect(result).toEqual(expectedResponse);

      expect(logSpy).toHaveBeenCalledWith('getTeamIdByEmail', expect.objectContaining({
        METHOD: expect.stringContaining('getTeamIdByEmail'),
        MESSAGE: mockError.message,
        REQUEST: reqData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('getUserGroupByEmail', () => {
    const reqData = { email: '<EMAIL>' };

    it('should return result from cloud6Service on success', async () => {
      const mockResponse = {
        status: 'success',
        data: [{ gid: 'G123', name: 'Learners' }],
      };

      jest.spyOn(cloud6Service, 'getUserGroupByEmail').mockResolvedValue(mockResponse);

      const result = await controller.getUserGroupByEmail(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should return default failed response and log error when exception is thrown', async () => {
      const mockError = new Error('Cloud6 group lookup failed');
      const expectedResponse = {
        status: 'failed',
        data: [],
        message: 'Some error occurred while fetching the records.',
      };

      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      jest.spyOn(cloud6Service, 'getUserGroupByEmail').mockRejectedValue(mockError);

      const result = await controller.getUserGroupByEmail(reqData);

      expect(result).toEqual(expectedResponse);

      expect(logSpy).toHaveBeenCalledWith('getUserGroupByEmail', expect.objectContaining({
        METHOD: expect.stringContaining('getUserGroupByEmail'),
        MESSAGE: mockError.message,
        REQUEST: reqData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

});
