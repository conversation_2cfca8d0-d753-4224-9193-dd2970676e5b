import {BadRequestException, Body, Controller, Inject, Post, UseFilters } from "@nestjs/common";
import { HttpExceptionFilter } from "../../common/filters/http.exception.filter";
import { APILog } from "../../logging/logger";
import { CommunicationService } from "../services/communication.service";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Cloud6Service } from "../../common/services/communication/cloud6/cloud6.service";
import { HelperService } from '../../helper/helper.service';
@ApiTags('Internal Communication API')
@Controller('internal/communication')
export class CommunicationController{
  @Inject() private readonly communicationService: CommunicationService;
  @Inject(HelperService) private readonly helperService: HelperService;

  //Consumed by communication
  @Post('/get-display-names-by-ids')
  @ApiOperation({
      summary: 'Gets all display names by ids',
      description: 'This API fetches all display names by ids and provides the response with uid and display name',
    })
  @ApiResponse({ status: 200, description: 'Display name fetch successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getDisplayNamesByIds(@Body() reqData) {
    let result = {
      'status': 'failed',
      'data': [],
      'message': 'Some error occured while fetching the records.'
    };
    try {
      const displayNames = await this.communicationService.getDisplayNamesByIds(reqData);
      return displayNames;
    } catch (error: any) {
        APILog.error('getDisplayNamesByIds', {
        METHOD: `${this.constructor.name}@${this.getDisplayNamesByIds.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
        });
        if (error instanceof BadRequestException) {
          return result.data;
        } else {
          return result;
        }
    }
  }
  
//Consumed by Communication
  @Post('/get-user-id-by-email')
  @ApiOperation({
      summary: 'Gets all User Ids by email',
      description: 'This API fetches all user IDs by email and provides the response with uid',
    })
  @ApiResponse({ status: 200, description: 'User Ids fetch successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getUserIdByEmail(@Body() reqData) {
    let result = {
      'status': 'failed',
      'data': [],
      'message': 'Some error occured while fetching the records.'
    };
    try {
      const userIds = await this.communicationService.getUserIdByEmail(reqData);
      return userIds;
    } catch (error: any) {
        APILog.error('getUserIdByEmail', {
        METHOD: `${this.constructor.name}@${this.getUserIdByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
        });

        if (error instanceof BadRequestException) {
          return result.data;
        }
        return result;
    }
  }

  /**
 * @summary Fetch Team ID by Email
 * @description This endpoint calls the internal Cloud6 communication API to retrieve the team ID 
 * associated with a given email address.
 *
 * @param {Object} reqData - Request payload containing the user's email
 * @returns {object} Response indicating success or failure of the operation,
 * typically containing the fetched data (if successful)
 */
  @Post('/get-user-team-by-email')
  @ApiOperation({
    summary: 'Fetch Team ID by Email from Cloud6 API',
    description: 'Calls the internal communication API to get Team ID using email',
  })
  @ApiResponse({ status: 200, description: 'Team ID fetched successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getTeamIdByEmail(@Body() reqData: { email: string }) {
    const response = {
      status: 'failed',
      data: [],
      message: 'Some error occurred while fetching the records.',
    };

    try {
      const cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const result = await cloud6Service.getUserTeamByEmail(reqData);
      return result;
    } catch (error: any) {
      APILog.error('getTeamIdByEmail', {
        METHOD: `${this.constructor.name}@${this.getTeamIdByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }


  /**
 * @summary Fetch Group ID by Email
 * @description This endpoint calls the internal Cloud6 communication API to retrieve the gid
 * associated with a given email address.
 *
 * @param {Object} reqData - Request payload containing the user's email
 * @returns {object} Response indicating success or failure of the operation,
 * typically containing the fetched data (if successful)
 */
  @Post('/get-user-group-by-email')
  @ApiOperation({
    summary: 'Fetch Group ID by Email from Cloud6 API',
    description: 'Calls the internal communication API to get Group ID using email',
  })
  @ApiResponse({ status: 200, description: 'GIDs fetched successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getUserGroupByEmail(@Body() reqData: { email: string }) {
    const response = {
      status: 'failed',
      data: [],
      message: 'Some error occurred while fetching the records.',
    };

    try {
      const cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const result = await cloud6Service.getUserGroupByEmail(reqData);
      return result;
    } catch (error: any) {
      APILog.error('getUserGroupByEmail', {
        METHOD: `${this.constructor.name}@${this.getUserGroupByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  @Post('/get-group-names-by-ids')
  @ApiOperation({
      summary: 'Gets all Group names by ids',
      description: 'This API fetches all group names by ids and provides the response with gid and title',
    })
  @ApiResponse({ status: 200, description: 'group Ids fetch successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getGroupNamesByIds(@Body() reqData) {
    try {
      const cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);

      const result = await cloud6Service.getGroupNameByIds(reqData);
      
      return result;
    } catch (error:any) {
      APILog.error('getGroupNamesByIds', {
        METHOD: `${this.constructor.name}@${this.getGroupNamesByIds.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  //Consumed by communication
  @Post('/get-user-names-by-uids')
  @ApiOperation({
      summary: 'Gets all display names by uids',
      description: 'This API fetches all display names by uids and provides the response with uid and displayName',
    })
  @ApiResponse({ status: 200, description: 'userNames fetch successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getUserNamesByUids(@Body() reqData) {
    try {
     const cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);

     const result = await cloud6Service.getUserNameByUids(reqData);
     
     return result; 
    } catch (error: any) {
      APILog.error('getUserNamesByUids', {
        METHOD: `${this.constructor.name}@${this.getUserNamesByUids.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error
    }
  }
}