import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';
import { UserRepository } from '../repositories/user/user.repository';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { AccountUser } from '../../db/mysql/entity/account-user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ChangePasswordDto } from '../../internal/dto/change-password.dto';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
import { Logger } from '@nestjs/common';
import { SetLearnerPasswordDto } from '../../internal/dto/set-learner-password.dto';
import { RoleRepository } from '../repositories/role/role.repository';
const drupalHash = {
  checkPassword: jest.fn(),
  hashPassword: jest.fn(),
};
describe('UserService', () => {
  let userService: UserService;
  const helperServiceMock = {
    // Implement methods or mock data as needed
    getHelper: jest.fn(),
    get: jest.fn(),
  };
  const cloud6UserRepositoryMock = {
    findOne: jest.fn(),
   
  };
  const configServiceMock = {
    // Implement methods or mock data as needed
    get: jest.fn(),
  };

  // Mock the user repository
  const userRepositoryMock = {
    findByUID: jest.fn(),
    findOneAndUpdate: jest.fn(),
    getUserByEmail: jest.fn(),
    saveUser: jest.fn(),
  } as any;

  const roleRepositoryMock = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    getRoles: jest.fn(),
  } as any;
  
  // Mock the Cloud6Service
  const cloud6ServiceMock = {
    resetPassword: jest.fn(),
    synchUser: jest.fn(),
  };
  const cloud6UserRepositoryUpdateMock = {
    update: jest.fn(),
  }

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: UserRepository,
          useValue: userRepositoryMock,
        },
        {
          provide: RoleRepository,
          useValue: roleRepositoryMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: HelperService,
          useValue: helperServiceMock,
        },
        {
          provide: getRepositoryToken(AccountUser),
          useValue: cloud6UserRepositoryMock,
        },
        {provide: getRepositoryToken(SentinelUser), useValue: cloud6UserRepositoryUpdateMock},
        {
          provide: 'Cloud6Service',
          useValue: cloud6ServiceMock,
        },
        { provide: 'CRYPTO_HELPER', useValue: {} },
        // {provide: 'cloud6SentinelUserRepositoryUpdate', useValue: cloud6UserRepositoryUpdateMock}
      ],
    }).compile();
    userService = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(userService).toBeDefined();
  });

  describe('changePassword', () => {
    it('should change the user password and return true', async () => {
      const changePasswordDto: ChangePasswordDto = {
        cur_passwd: 'oldPassword',
        user_id: 'userId',
        gId: 1123,
        new_passwd: 'newPassword',
        confirm_passwd: 'newPassword',
      };

      userRepositoryMock.findByUID.mockResolvedValue({
        password: drupalHash.hashPassword('oldPassword'),
        email: '<EMAIL>',
        status: true,
      });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(true);
      cloud6ServiceMock.resetPassword.mockResolvedValue(true);

      await userService.changePassword(changePasswordDto);

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith('userId');
      // expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });

    it('should handle errors and return an error object', async () => {
      const changePasswordDto: ChangePasswordDto = {
        cur_passwd: 'oldPassword',
        user_id: 'userId',
        gId: 1123,
        new_passwd: 'newPassword',
        confirm_passwd: 'newPassword',
      };

      userRepositoryMock.findByUID.mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await userService.changePassword(changePasswordDto);

      expect(result instanceof Error).toBe(true);
    });
  });


  // describe('syncCloud6SetinelUser', () => {
  //   let userService: UserService;
  //   let sentinelUserRepository: SentinelUser;

  //   it('should sync user with Cloud6 Sentinel and return the user ID', async () => {
  //     const signupDetail = {
  //       email: '<EMAIL>',
  //       password: 'password123',
  //       accept_agreement: true,
  //     };

  //     const hashedPassword = 'hashedPassword';
  //     jest.spyOn(drupalHash, 'hashPassword').mockReturnValue(hashedPassword);

  //     const createdUserMock = { uid: 'mockedUserId' };
  //     userService.syncCloud6SetinelUser = jest.fn().mockReturnValue(createdUserMock);

  //     const result = await userService.syncCloud6SetinelUser(signupDetail);

  //     expect(userService.syncCloud6SetinelUser).toHaveBeenCalledWith({
  //       mail: signupDetail.email,
  //       pass: hashedPassword,
  //       accept_agreement: 1,
  //     });

  //     expect(result).toEqual({ uid: 'mockedUserId' });
  //   });
  // });
  describe('updateCloud6SentinelByUidOrMail', () => {
    
    it('should update the user password and return true', async () => {
      const updateDetails = { uid: '10144', hashPassword: 'hashedPasswordExample' };
  
      (cloud6UserRepositoryUpdateMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await (userService as any).updateCloud6SentinelByUidOrMail(updateDetails);
  
      expect(cloud6UserRepositoryUpdateMock.update).toHaveBeenCalledWith(
        { uid: 10144 },
        { pass: 'hashedPasswordExample' }
      );
      expect(result).toBe(true);
    });
  
    it('should return false when no rows are affected', async () => {
      const updateDetails = { uid: '10144', hashPassword: 'hashedPasswordExample' };
      (cloud6UserRepositoryUpdateMock.update as jest.Mock).mockResolvedValue({ affected: 0 });
  
      const result = await (userService as any).updateCloud6SentinelByUidOrMail(updateDetails);
  
      expect(cloud6UserRepositoryUpdateMock.update).toHaveBeenCalledWith(
        { uid: 10144 },
        { pass: 'hashedPasswordExample' }
      );
      expect(result).toBe(false);
    });
  
  
  });
  
  describe('setLearnerPassword', () => {
    it('should set the learner password and return true', async () => {
      const setLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: 1743576498,
        login: 1743576498,
        user_options: 0,
      };

      const userMock = {
        email: '<EMAIL>',
        uid: '12345',
        status: true,
        roles: [],
      };

      const roleMock = [{ rid: '2345' }];
      const hashPasswordMock = 'hashedPassword';

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      helperServiceMock.getHelper.mockResolvedValue({
        isUserMemberOfGroup: jest.fn().mockResolvedValue(true),
        updateCloud6SentinelByUidOrMail: jest.fn().mockResolvedValue(true),
        syncCloud6SentinelUserRole: jest.fn().mockResolvedValue(true),
      });
      helperServiceMock.get.mockResolvedValue({
        findAll: jest.fn().mockResolvedValue(roleMock),
      });
      drupalHash.hashPassword.mockReturnValue(hashPasswordMock);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(true);

      const result = await userService.setLearnerPassword(setLearnerPasswordDto);

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(helperServiceMock.get).toHaveBeenCalledWith(roleRepositoryMock);
      expect(drupalHash.hashPassword).toHaveBeenCalledWith('newPassword123');
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        {
          password: hashPasswordMock,
          roles: roleMock,
          login: new Date(setLearnerPasswordDto.login),
          access: new Date(setLearnerPasswordDto.access),
          user_options: 0,
        }
      );
      expect(result).toBe(true);
    });

    it('should throw UserNotFoundException if user is not found or inactive', async () => {
      const setLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: 1743576498,
        login: 1743576498,
        user_options: 0,
      };

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      await expect(userService.setLearnerPassword(setLearnerPasswordDto)).rejects.toThrow(
        'UserNotFoundException'
      );
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should throw UserNotGroupMember if user is not a member of the group', async () => {
      const setLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: 1743576498,
        login: 1743576498,
        user_options: 0,
      };

      const userMock = {
        email: '<EMAIL>',
        uid: '12345',
        status: true,
      };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      helperServiceMock.getHelper.mockResolvedValue({
        isUserMemberOfGroup: jest.fn().mockResolvedValue(false),
      });

      await expect(userService.setLearnerPassword(setLearnerPasswordDto)).rejects.toThrow(
        'UserNotGroupMember'
      );
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserHelper');
    });

    it('should log an error and return the error object if an exception occurs', async () => {
      const setLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: 1743576498,
        login: 1743576498,
        user_options: 0,
      };

      userRepositoryMock.getUserByEmail.mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await userService.setLearnerPassword(setLearnerPasswordDto);

      expect(result instanceof Error).toBe(true);
      expect(Logger.error).toHaveBeenCalledWith('setLearnerPassword', expect.any(Object));
    });
  });

});
