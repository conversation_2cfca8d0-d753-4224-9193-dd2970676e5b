import { Test, TestingModule } from '@nestjs/testing';
import { UserMgmtCommunityService } from './usermgmt.community.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { FailedCommunityUser } from '../../../db/mysql/entity/failed-community-user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('UserMgmtCommunityService', () => {
  let userMgmtCommunityService: UserMgmtCommunityService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockCryptoHelper = {
    encrypt: jest.fn(),
    decrypt: jest.fn(),
  };

  const mockHelperService = {
    getHelper: jest.fn(),
  };

  const mockFailedCommunityUserRepository = {
    insert: jest.fn(),
  };

  beforeEach(async () => {
    mockConfigService.get.mockReset();
    mockCryptoHelper.encrypt.mockReset();
    mockCryptoHelper.decrypt.mockReset();
    mockHelperService.getHelper.mockReset();
    mockFailedCommunityUserRepository.insert.mockReset();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserMgmtCommunityService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'TRIBE_CRYPTO_HELPER',
          useValue: mockCryptoHelper,
        },
        {
          provide: HelperService,
          useValue: mockHelperService,
        },
        {
          provide: getRepositoryToken(FailedCommunityUser),
          useValue: mockFailedCommunityUserRepository,
        },
      ],
    }).compile();

    userMgmtCommunityService = module.get<UserMgmtCommunityService>(UserMgmtCommunityService);
  });

  it('should be defined', () => {
    expect(userMgmtCommunityService).toBeDefined();
  });

  // You can similarly write test cases for other methods such as queryTribe, signUpNewUser, insertFailedCommunityUsers, etc.
});
