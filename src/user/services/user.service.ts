import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { APILog, Logger } from '../../logging/logger';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { ChangePasswordDto } from '../../internal/dto/change-password.dto';
import { SetLearnerPasswordDto } from '../../internal/dto/set-learner-password.dto';
import * as drupalHash from 'drupal-hash';
import { IUserRepository, UserRepository } from '../repositories/user/user.repository';
import { Utility } from './../../common/util/utility';
import { User } from '../../db/mongo/schema/user/user.schema';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { ChangeLogService } from '../../common/services/communication/changelog/changelog.service';
import { IRoleRepository, RoleRepository } from '../repositories/role/role.repository';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { CachingService } from '../../caching/caching.service';
import { DeviantsService } from '../../common/services/communication/deviants/deviants.service';
export interface IUserService {
  changePassword(changePasswordDto: ChangePasswordDto): Promise<boolean | Error>;
}
@Injectable()
export class UserService implements IUserService {
  @Inject() configService: ConfigService;
  @Inject(UserRepository) private readonly userRepository: IUserRepository;
  @Inject() private readonly helperService: HelperService;

  #LRS_OBJECT_TYPE = 'enterprise-lms-v1'


  async userRegistration(signUpDto: Partial<User>, userRoles = [] , userType = ''): Promise<Partial<User>> {
    try {
      const [userRepository, userHelper, userMgmtHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper('UserMgmtUtilityHelper'),
      ]);

      const userInfo = await userRepository.getUserByEmail(signUpDto?.email);
      if (userInfo) {
        throw new BadRequestException('UserAlreadyExist');
      }
      // Get user management utility helper instance
      const roles = await userMgmtHelper.prepareAssignRoleList(userRoles , userType);
      signUpDto = { ...signUpDto, roles };
      // Update signupDetail with role IDs
      const createdUser: any = await userRepository.saveUser({
        ...signUpDto,
        password: drupalHash.hashPassword(signUpDto?.password),
      });
      if (createdUser && createdUser.email) {
        // here save data in Mysql (sentinel-user)
        const cUser = await this.syncCloud6SentinelUser({name : createdUser?.name || createdUser?.email,...signUpDto});
        if (cUser && cUser?.uid) {
          const [ updatedUser, createRoleObjArray] = await Promise.all([ 
          // update the user uid in mongo
          userRepository.findOneAndUpdate({ email: signUpDto.email }, { uid: Number(cUser?.uid) }),
          // sync role sentinel user
          userMgmtHelper.prepareSentinelUserRoleList(roles , cUser?.uid)
          ]);
          
          // update role to mysql
          await Promise.all([
          userHelper.syncCloud6SentinelUserRole(createRoleObjArray),
          userHelper.updateUserLoginTime(cUser),
          ]);
          // drupal create user data
          if (this.configService.get('enableDrupalSync')) {
            signUpDto['uid'] = Number(cUser?.uid);
            userHelper.createUserDataWithMySQLDrupal(signUpDto)
          }
          return { ...updatedUser, uid: Number(cUser?.uid) };
        }  else {
          // Due to failure to sync with Mysql reverting the changes to mongoDb
          await userRepository.deleteOneById(createdUser?._id);
          throw new BadRequestException();
        }
      } else {
        throw new BadRequestException();
      }
    } catch (error: any) {
      Logger.error('userRegistration', {
        METHOD: this.constructor.name + '@' + this.userRegistration.name,
        MESSAGE: error.message,
        REQUEST: signUpDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  
  /**
   * Method to change the password for a user
   * @param changePasswordDto - DTO containing current password, user ID, group ID, and new password
   * @returns Promise<boolean | Error> - Returns true if password is successfully changed, otherwise throws an error
   */
  async changePassword(changePasswordDto: ChangePasswordDto): Promise<boolean | Error> {
    try {
      const [cacheService, userHelper] = await Promise.all([
        this.helperService.get<CachingService>(CachingService),
        this.helperService.getHelper('UserHelper'),
      ]);

      const { cur_passwd, user_id, gid, new_passwd } = changePasswordDto;
      const user = await this.userRepository.findByUID(user_id);

      if (drupalHash.checkPassword(cur_passwd, user.password) === false) {
        throw new BadRequestException('NotAuthorizedException');
      }
      
      if (!user || !user.status || user.status !== 1) {
        throw new BadRequestException('UserNotFoundException');
      }
      if (!Utility.isEmpty(gid) && gid !== this.configService.get('defaultGroupId')) {
        const userGroups: number[] = user?.user_groups || [];
        if (!userGroups.includes(Number(gid))) {
          throw new BadRequestException('UserNotGroupMember');
         
        }
        
      }
      // Rate limiting
      const attemptResponse = await userHelper.changePasswordAttemptLimit(user.email);
      if (attemptResponse?.status === 'limit_exceeded') {
        throw new BadRequestException('AttemptLimitExceed');
      }

      const hashPassword = drupalHash.hashPassword(new_passwd);
      var isSentinelUsersUpdated = false;
      const passStatus = await this.userRepository.findOneAndUpdate({ email: user.email }, { user_options: 1, password: hashPassword });
      if(this.configService.get('enableDrupalSync')) {
        await userHelper.syncUserDataWithMySQLDrupal({uid: user?.uid, pass: new_passwd, user_options: 1}); //update the user drupal data
       }
      // Saving data into sentinel users
      if (passStatus) {
        isSentinelUsersUpdated = await userHelper.updateCloud6SentinelByUidOrMail({ mail: user.email, pass: hashPassword, user_options: 1 });
        Logger.info('change-password-event', {
          TIMESTAMP: new Date().getTime(),
          MESSAGES: 'change password event occurred to SentinelUser for user: ' + user_id,
        });
      }
      // Invalidate token for mobile users
      if (isSentinelUsersUpdated !== false) {
        //invalidate sessions
        const cacheKey = `${'sc_'}${user?.uid}`;
        await cacheService.invalidateCache(cacheKey,null,0);
        
        //invalidate token for app user
        const paperclipService: PaperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
        paperclipService.invalidateTokenForAppUser(user.uid);

        // Sending change request log for change password
        var data = {
          userId: user.uid,
          userEmail: user.email,
          module: "User Password Update",
          recordTable: 'users',
          type: 'update',
          oldValue: cur_passwd,
          newValue: hashPassword,
          changeDate: new Date().getTime(),
        };

        const dataChangeLogService: ChangeLogService = await this.helperService.get<ChangeLogService>(ChangeLogService);
        const requestLog = await dataChangeLogService.changeLogRequest(data);

        if (requestLog instanceof Error) {
          throw new BadRequestException('processRequestFailed');
        }
        return passStatus ? true : false;
      }
      return false;
    } catch (error: any) {
      Logger.error('synchUser', {
        METHOD: this.constructor.name + '@' + this.changePassword.name,
        MESSAGE: error.message,
        REQUEST: changePasswordDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
     
    }
  }

  /**
   * Method to set password for B2B learner
   * @param setLearnerPasswordDto
   * @returns
   */
  async setLearnerPassword(setLearnerPasswordDto: SetLearnerPasswordDto): Promise<boolean | Error> {
    try {
      const { email, gids , rids , user_pwd , login , access , user_options} = setLearnerPasswordDto;
      const [authTokenHelper, userHelper, roleRepository , lrsInstanse] = await Promise.all([
        this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.getHelper('UserHelper'),
        this.helperService.get<IRoleRepository>(RoleRepository),
        this.helperService.getHelper('lrsHelper'),
      ]);
      let user = await this.userRepository.getUserByEmail(email);
      const gId = gids[0] || "";
      const clientKey = setLearnerPasswordDto?.client_id || this.configService.get('clientKey');

      if (!user || !user.status) {
        throw new BadRequestException('UserNotFoundException');
      }

      if (Utility.isEmpty(gId) && gId !== this.configService.get('defaultGroupId')) {
        const userHelper = await this.helperService.getHelper('UserHelper');
        const isUserMember = await userHelper.isUserMemberOfGroup(user?.uid, gId);
        if (!isUserMember) {
          throw new BadRequestException('UserNotGroupMember');
        }
      }
      
      const hashPassword = drupalHash.hashPassword(user_pwd);
      const roles = rids.length ? await roleRepository.findAll({ rid: { $in: rids } }) : [];
      const userRolesUpdate = { $addToSet: { roles: { $each: roles.map(role => role) } }}
      const [passStatus] = await Promise.all([
        this.userRepository.findOneAndUpdate(
          { email: user.email },
          {
            password: hashPassword,
            login: new Date(login),
            access: new Date(access),
            user_options,
            ...userRolesUpdate
          }
        ),
        userHelper.updateCloud6SentinelByUidOrMail({
          mail: email,
          pass: hashPassword,
          user_options,
        }),
        userHelper.syncCloud6SentinelUserRole(
          roles.map((role) => {
            return { uid: user?.uid, rid: role.rid };
          })
        ),
      ]);

      // generate JWT token
      user = await this.userRepository.getUserByEmail(email);
      const userTokenDetail = await authTokenHelper.generateSessionTokens(
        user,
        clientKey,
      );
      if(userTokenDetail?.idToken){
        // Update the user table timestamp noting user has logged in.
        // This is also used to invalidate one-time login links.
        await userHelper.updateCloud6SentinelByUidOrMail({
          mail: user.email,
          login,
          access,
        });
      }

      // drupal sync
      const roleMapping = await userHelper.prepareRoleListForDrupalSync(roles);
      if (this.configService.get('enableDrupalSync')) {
        await userHelper.syncUserDataWithMySQLDrupal({
          uid: user?.uid,
          pass: user_pwd,
          login: login,
          access: access,
          user_options: user_options,
          roles: roleMapping,
        }); //update the user drupal data
      }

      /* LRS logging via scheduler - STARTS */
      const lrsUserData = {
        id: user?.uid || '',
        email: user?.email || '',
        name: user?.display_name || '',
        roles: user?.roles || {},
        timezone: user?.timezone || '',
      };
      const lrsData = {
        verb: 'set-learner-password',
        objectType: this.#LRS_OBJECT_TYPE,
        objectId: user?.uid || '',
        dataVals: {
          client_id: clientKey,
          gid: gId,
        },
      }; 
      lrsInstanse.sendDataToLrs(lrsUserData, lrsData);
      /* LRS logging via scheduler - ENDS */

      return !!passStatus;
    } catch (error: any) {
      Logger.error('setLearnerPassword', {
        METHOD: this.constructor.name + '@' + this.setLearnerPassword.name,
        MESSAGE: error.message,
        REQUEST: setLearnerPasswordDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  async syncCloud6SentinelUser(signupDetail: Partial<User>): Promise<{ uid: string }> {
    try {
      const {
        country_code,
        timezone,
        email,
        password,
        first_name,
        last_name,
        accept_agreement,
        phone_no,
        name,
        display_name,
        status,
        user_type,
        account_setup,
        password_created,
      } = signupDetail;

      const sentinelUserData: Partial<SentinelUser> = {
        mail: email,
        pass: drupalHash.hashPassword(password),
        country_code,
        timezone,
        first_name,
        last_name,
        accept_agreement: Number(accept_agreement),
        phone_no,
        name,
        display_name,
        status,
        user_type,
        account_setup,
        password_created,
      };

      
      // sync user with cloud6 table - sentinel_user
      const userHelper = await this.helperService.getHelper('UserHelper');
      const result = await userHelper.createCloud6SentinelUser(sentinelUserData); // Save to DB
      
      if (result && result?.uid) {
        return { uid: String(result.uid) };
      } else {
        throw new BadRequestException('UserNotCreatedException');
      }
    } catch (error: any) {
      Logger.error('syncCloud6SetinelUser', {
        METHOD: this.constructor.name + '@' + this.syncCloud6SentinelUser.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('UserNotCreatedException');
    }
  }

  async confirmDeleteAccount(userId: number): Promise<{ type: string; msg: string }> {
    try {
      let result = {type: 'failed', msg: 'Invalid request'};

      if (!userId) {
        return result;
      }

      const [userRepository, userHelper, paperclipService, gdprRequestHelper, deviantsService] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
        this.helperService.get<PaperclipService>(PaperclipService),
        this.helperService.getHelper('GdprRequestHelper'),
        this.helperService.get<DeviantsService>(DeviantsService),
      ]);

      const userData = await userRepository.findByUID(userId);

      if (userData && userData?.status === 1) {
        
        const [deactivatingUser, updateSentinelUsers] = await Promise.all([
          userRepository.findOneAndUpdate({ uid: userId }, { status: 0 }),
          userHelper.updateCloud6SentinelByUidOrMail({ uid: userId, status: 0 }),
        ]);
       
        if (deactivatingUser && updateSentinelUsers) {
          const messageToQueue = {
            "Email": userData?.email,
            "Q": this.configService.get('qNameGdprUserDelete')
          };
          //expiring app user token
          Promise.all([
            paperclipService.expireAppUsertoken(userId),
            gdprRequestHelper.addRequest(Number(userData.uid), 'delete'),
            deviantsService.pushMessageToQueue(messageToQueue),
          ]);

          result.type = 'success';
          result.msg = 'User account has been blocked successfully.';

        } else {
          APILog.log('confirmDeleteAccount', {
            TRACE: this.constructor.name + '@' + this.confirmDeleteAccount.name,
            MESSAGE: 'GDPR Request failed',
            REQUEST: {uid: userId},
            TIMESTAMP: new Date().getTime(),
          });
          result.msg = "Some error occured. Please try again after sometime."
        }
      }
      
      return result;
    } catch (error:any) {
      Logger.error('confirmDeleteAccount', {
        METHOD: this.constructor.name + '@' + this.confirmDeleteAccount.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('UserNotDeletedException');
    }
  }
}
