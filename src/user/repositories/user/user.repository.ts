import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { User } from '../../../db/mongo/schema/user/user.schema';

export interface IUserRepository {
  find(params: any): Promise<User[]>;
  findOne(params: any): Promise<User>;
  findByUID(uid: number): Promise<Partial<User>>;
  upsert(user: Partial<User>): Promise<User>;
  saveUser(user: Partial<User>): Promise<Partial<User>>;
  getUserByEmail(email: string): Promise<Partial<User>>;
  findOneAndUpdate(filter, user: Partial<User>): Promise<Partial<User>>;
  deleteOneById(_id: string): Promise<{ acknowledged: Boolean; deletedCount: Number }>;
}

@Injectable()
export class UserRepository implements IUserRepository {
  @InjectModel(User.name, 'secure') private secureUserModel: Model<User>;

  async findByUID(uid: number): Promise<Partial<User>> {
    return await this.secureUserModel.findOne({ uid: uid }).populate('roles').lean();
  }

  async upsert(user: User): Promise<User> {
    return await this.secureUserModel.findOneAndUpdate({ email: user.email }, user, { upsert: true }).exec();
  }
  async saveUser(user: User): Promise<User> {
    return await this.secureUserModel.create(user);
  }
  async getUserByEmail(email: string): Promise<Partial<User>> {
     return await this.secureUserModel.findOne({ email: email }).populate('roles').lean();
  }
  async findOneAndUpdate(filter: any, user: Partial<User>): Promise<Partial<User>> {
    return await this.secureUserModel.findOneAndUpdate(filter, user).populate('roles').lean();
  }
  async find(params: any): Promise<User[]> {
    return await this.secureUserModel.find(params).populate('roles').exec();
  }
  async findOne(params: any): Promise<User> {
    return await this.secureUserModel.findOne(params).populate('roles').exec();
  }
  async deleteOneById(_id: any): Promise<{ acknowledged: Boolean; deletedCount: Number }> {
    return await this.secureUserModel.deleteOne({ _id: new mongoose.Types.ObjectId(_id) });
  }
}
