import { Test, TestingModule } from '@nestjs/testing';
import { RoleRepository, IRoleRepository } from './role.repository';
import { Model, Query } from 'mongoose';
import { Role } from '../../../db/mongo/schema/roles/role.schema';
import { getModelToken } from '@nestjs/mongoose';

describe('RoleRepository', () => {
  let roleRepository: IRoleRepository;
  let roleModel: Model<Role>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleRepository,
        {
          provide: getModelToken(Role.name, 'default'), // Replace with the actual token used in your application
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    roleRepository = module.get<RoleRepository>(RoleRepository);
    roleModel = module.get<Model<Role>>(getModelToken(Role.name, 'default')); // Replace with the actual token used in your application
  });

  describe('findAll', () => {
    it('should return an array of roles', async () => {
      const roles: Role[] = [
        {
          rid: 59,
          roleName: 'looper_student',
        },
      ];

      // Create a mock DocumentQuery-like object for find
      const mockQuery: Query<Role[], Role> = {
        lean: jest.fn().mockResolvedValue(roles),
      } as unknown as Query<Role[], Role>;

      jest.spyOn(roleModel, 'find').mockReturnValue(mockQuery);

      const query = {}; // Define your query object

      const result = await roleRepository.findAll(query);

      expect(result).toEqual(roles);
    });
  });

  describe('findOne', () => {
    it('should return a role by name', async () => {
      const roleName = 'TestRole';
      const role: Role = {
        rid: 59,
        roleName: 'looper_student',
      }; // create a mock role object

      // Create a mock DocumentQuery-like object for findOne
      const mockQuery: Query<Role, Role> = {
        lean: jest.fn().mockResolvedValue(role),
      } as unknown as Query<Role, Role>;

      jest.spyOn(roleModel, 'findOne').mockReturnValue(mockQuery);

      const result = await roleRepository.findOne({ roleName });

      expect(result).toEqual(role);
    });

    it('should return null if the role is not found', async () => {
      const roleName = 'NonExistentRole';

      // Create a mock DocumentQuery-like object for findOne that returns null
      const mockQuery: Query<Role, Role> = {
        lean: jest.fn().mockResolvedValue(null),
      } as unknown as Query<Role, Role>;

      jest.spyOn(roleModel, 'findOne').mockReturnValue(mockQuery);

      const result = await roleRepository.findOne({ roleName });

      expect(result).toBeNull();
    });
  });
});
