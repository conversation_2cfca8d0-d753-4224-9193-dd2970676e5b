
import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { TaxonomyRepository } from './taxonomy.repository';
import { Taxonomies } from '../../../db/mongo/schema/taxonomies/taxonomies.schema';

describe('TaxonomyRepository', () => {
  let repository: TaxonomyRepository;

  const mockTaxonomy = {
    category: 'TEST_CATEGORY',
    name: 'Test Taxonomy',
    description: 'Test Description'
  };

  const mockModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    aggregate: jest.fn(),
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TaxonomyRepository,
        {
          provide: getModelToken(Taxonomies.name, 'default'),
          useValue: mockModel
        },
      ],
    }).compile();

    repository = module.get<TaxonomyRepository>(TaxonomyRepository);
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('find', () => {
    it('should return array of taxonomies', async () => {
      const params = { category: 'TEST' };
      mockModel.find.mockReturnValue({ exec: jest.fn().mockResolvedValue([mockTaxonomy]) });
      
      const result = await repository.find(params);
      
      expect(result).toEqual([mockTaxonomy]);
      expect(mockModel.find).toHaveBeenCalledWith(params);
    });
  });

  describe('findOne', () => {
    it('should return a single taxonomy', async () => {
      const params = { category: 'TEST' };
      mockModel.findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockTaxonomy) });
      
      const result = await repository.findOne(params);
      
      expect(result).toEqual(mockTaxonomy);
      expect(mockModel.findOne).toHaveBeenCalledWith(params);
    });
  });

  describe('aggregate', () => {
    it('should perform aggregation', async () => {
      const aggregateQuery = [{ $match: { category: 'TEST' } }];
      mockModel.aggregate.mockReturnValue({ exec: jest.fn().mockResolvedValue([mockTaxonomy]) });
      
      const result = await repository.aggregate(aggregateQuery);
      
      expect(result).toEqual([mockTaxonomy]);
      expect(mockModel.aggregate).toHaveBeenCalledWith(aggregateQuery);
    });
  });
});
