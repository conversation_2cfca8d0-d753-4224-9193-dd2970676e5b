import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from './user.controller';
import { Response } from 'express';
import { Logger } from '../../../logging/logger';

describe('UserController', () => {
  let controller: UserController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [],
    }).compile();

    controller = module.get<UserController>(UserController);
  });

  describe('register', () => {
    it('should log "register"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.register();
      expect(loggerSpy).toHaveBeenCalledWith('register');
    });
  });

  describe('login', () => {
    it('should return an object with token property', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { redirect_url: 'tokenValue', calendar_url: '' };
      const result = await controller.loginRender(queryParam, res);
      expect(redirectSpy).toHaveBeenCalledWith('/auth/login?redirect_url=tokenValue&calendar_url=');
    });
    it('should redirect to /auth/login?calendar_url=VALUE when only calendar_url is present', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { calendar_url: 'SOME_VALUE', redirect_url: '' };

      await controller.loginRender(queryParam, res);

      expect(redirectSpy).toHaveBeenCalledWith('/auth/login?calendar_url=SOME_VALUE&redirect_url=');
    });

    it('should redirect to /auth/login when neither redirect_url nor calendar_url is present', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { redirect_url: '', calendar_url: '' };

      await controller.loginRender(queryParam, res);

      expect(redirectSpy).toHaveBeenCalledWith('/auth/login?redirect_url=&calendar_url=');
    });

    it('should redirect to /auth/login?redirect_url=VALUE&calendar_url=VALUE when both redirect_url and calendar_url are present', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { redirect_url: 'SOME_VALUE', calendar_url: 'OTHER_VALUE' };

      await controller.loginRender(queryParam, res);

      expect(redirectSpy).toHaveBeenCalledWith('/auth/login?redirect_url=SOME_VALUE&calendar_url=OTHER_VALUE');
    });
  });

  describe('registerEmail', () => {
    it('should return an object with token and validationErrors properties', async () => {
      const result = await controller.registerEmail();
      expect(result).toEqual({ token: '', validationErrors: [] });
    });
  });

  describe('registerComplete', () => {
    it('should log "registerComplete"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.registerComplete();
      expect(loggerSpy).toHaveBeenCalledWith('registerComplete');
    });
  });

  describe('multiAccount', () => {
    it('should redirect to /auth/multi-account with query parameters', () => {
      const queryParam = {
        gid: 'group123',
        url: 'example.com',
        calendar_url: 'calendar.example.com',
      };
      const res: Partial<Response> = {
        redirect: jest.fn(),
      };
      controller.multiAccount(queryParam, res as Response);
      expect(res.redirect).toHaveBeenCalledWith(
        '/auth/multi-account?gid=group123&url=example.com&calendar_url=calendar.example.com',
      );
    });
  });

  describe('forgotPassword', () => {
    it('should log "forgotPassword"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.forgotPassword();
      expect(loggerSpy).toHaveBeenCalledWith('forgotPassword');
    });
  });

  describe('reset', () => {
    it('should log "reset" and receive params', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      const params = { userId: '1', requestTime: '123456', requestToken: 'token' };
      controller.reset(params);
      expect(loggerSpy).toHaveBeenCalledWith('reset');
    });
  });

  describe('resetPassword', () => {
    it('should log "resetPassword" and receive params', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      const params = { userId: '1', requestTime: '123456', requestToken: 'token' };
      controller.resetPassword(params);
      expect(loggerSpy).toHaveBeenCalledWith('resetPassword');
    });
  });

  describe('processAppleAuthResponseRegister', () => {
    it('should log "processAppleAuthResponseRegister"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.processAppleAuthResponseRegister();
      expect(loggerSpy).toHaveBeenCalledWith('processAppleAuthResponseRegister');
    });
  });

  describe('processAppleAuthResponseLogin', () => {
    it('should log "processAppleAuthResponseLogin"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.processAppleAuthResponseLogin();
      expect(loggerSpy).toHaveBeenCalledWith('processAppleAuthResponseLogin');
    });
  });

  describe('processSocialAuthResponse', () => {
    it('should log "processSocialAuthResponse"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.processSocialAuthResponse();
      expect(loggerSpy).toHaveBeenCalledWith('processSocialAuthResponse');
    });
  });

  describe('createUserSocialAccountDetails', () => {
    it('should log "createUserSocialAccountDetails"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.createUserSocialAccountDetails();
      expect(loggerSpy).toHaveBeenCalledWith('createUserSocialAccountDetails');
    });
  });

  describe('createUserAccount', () => {
    it('should log "createUserAccount"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.createUserAccount();
      expect(loggerSpy).toHaveBeenCalledWith('createUserAccount');
    });
  });

  describe('socialLink', () => {
    it('should log "socialLink"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.socialLink();
      expect(loggerSpy).toHaveBeenCalledWith('socialLink');
    });
  });

  describe('validateLogin', () => {
    it('should log "validateLogin"', () => {
      const loggerSpy = jest.spyOn(Logger, 'log');
      controller.validateLogin();
      expect(loggerSpy).toHaveBeenCalledWith('validateLogin');
    });
  });

  describe('manageAuthRedirect', () => {
    it('should redirect with the correct URL', () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { token: 'tokenValue' };
      controller.manageAuthRedirect(queryParam, res);
      expect(redirectSpy).toHaveBeenCalledWith('/auth/manage-auth-redirect?token=tokenValue');
    });
  });

  describe('accountSetUp', () => {
    it('should redirect to the correct URL with query parameters', async () => {
      // Arrange
      const queryParam = {
        countryId: 'IN',
        phoneNo: '**********',
        redirect_url: 'http://dockerv2.simplilearn.com:8609/apachedev/git/paperclip/public/dashboard/oob/onboarding',
        referer: 'WebsiteThankYouPage',
        setupAccount: 'true',
        userEmail: '<EMAIL>',
        userName: 'shitalaug',
      };

      const res: Response = {
        redirect: jest.fn(),
      } as unknown as Response;

      // Act
      await controller.accountSetUp(queryParam, res);

      // Assert
      expect(res.redirect).toHaveBeenCalledWith(
        '/auth/account-setup?countryId=IN&phoneNo=**********&redirect_url=http%3A%2F%2Fdockerv2.simplilearn.com%3A8609%2Fapachedev%2Fgit%2Fpaperclip%2Fpublic%2Fdashboard%2Foob%2Fonboarding&referer=WebsiteThankYouPage&setupAccount=true&userEmail=shital1august1%40yopmail.com&userName=shitalaug',
      );
    });
  });
});
