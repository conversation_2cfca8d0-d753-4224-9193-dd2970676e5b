import { Test, TestingModule } from '@nestjs/testing';
import { ProfileController } from './profile.controller';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { JwtService } from '@nestjs/jwt';
import { BadRequestException } from '@nestjs/common';

// Mocks
const mockConfigService = {
  get: jest.fn((key) => {
    const mockValues = {
      ssoCookie: 'mockSsoCookie',
      profileCookie: 'mockProfileCookie',
      defaultGroupId: '1',
      clientKey: 'mockClientKey',
      profileTopic: 'mockProfileTopic',
      ssoCookieDomain: 'mockDomain',
      ic9SiteUrl: 'https://ic9site.com',
      issuer: 'https://issuer.com',
    };
    return mockValues[key];
  }),
};

const mockHelperService = {
  getHelper: jest.fn(),
  get: jest.fn(),
};

const mockJwtService = {
  decode: jest.fn(),
};

let mockCryptoHelper: any = {};
describe('ProfileController', () => {
  let controller: ProfileController;

  beforeEach(async () => {
    mockCryptoHelper = { decrypt: jest.fn() };
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProfileController],
      providers: [
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HelperService, useValue: mockHelperService },
        { provide: JwtService, useValue: mockJwtService },
        { provide: 'CRYPTO_HELPER', useValue: mockCryptoHelper }, ,
      ],
    }).compile();

    controller = module.get<ProfileController>(ProfileController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  //Change password GET
  describe('/change-password', () => {
    let mockReq: any;
    let mockRes: any;

    beforeEach(() => {
      mockReq = {
        user: { uid: 123 },
        cookies: {},
        route: { path: '/change-password' },
      };
      mockRes = {
        clearCookie: jest.fn(),
      };
    });

    it('should return expected view data without clearing cookie when no cookie exists', async () => {
      const result = await controller.changePassword(mockReq, mockRes);

      expect(result).toEqual({
        token: '',
        validationErrors: [],
        route: expect.any(String),
        type: false,
      });
      expect(mockRes.clearCookie).not.toHaveBeenCalled();
    });

    it('should clear cookie and return type true if change-password cookie exists', async () => {
      const key = '123_changePassword';
      mockReq.cookies[key] = 'someValue';

      const result = await controller.changePassword(mockReq, mockRes);

      expect(mockRes.clearCookie).toHaveBeenCalledWith(key, {
        httpOnly: true,
        domain: 'mockDomain', // value from mockConfigService.get('ssoCookieDomain')
      });
      expect(result).toEqual({
        token: '',
        validationErrors: [],
        route: expect.any(String),
        type: true,
      });
    });
  });

  //Change password POST
  describe('/change-password POST', () => {
    let mockReq: any;
    let mockRes: any;
    let mockChangePasswordDto: any;

    beforeEach(() => {
      mockReq = {
        user: { uid: 123 },
      };
      mockRes = {
        contentType: jest.fn(),
      };
      mockChangePasswordDto = { old_pwd: 'old', user_pwd: 'new', confirm_pwd: 'new' };

      // Mock helpers
      mockHelperService.getHelper.mockResolvedValue({
        setCookie: jest.fn(),
      });

      // Mock userService
      mockHelperService.get.mockResolvedValue({
        changePassword: jest.fn(),
      });
    });

    it('should change password and return success script', async () => {
      const mockSetCookie = jest.fn();
      mockHelperService.getHelper.mockResolvedValueOnce({ setCookie: mockSetCookie });
      const mockUserService = { changePassword: jest.fn().mockResolvedValue(true) };
      mockHelperService.get.mockResolvedValueOnce(mockUserService);

      const result = await controller.changePasswordPost(mockChangePasswordDto, mockReq, mockRes);

      expect(mockUserService.changePassword).toHaveBeenCalled();
      expect(mockSetCookie).toHaveBeenCalled();
      expect(mockRes.contentType).toHaveBeenCalledWith('text/html');
      expect(result).toContain('alert("Your password has been successfully changed');
    });

    it('should throw BadRequestException on known error (UserNotFoundException)', async () => {
      mockHelperService.get.mockResolvedValueOnce({
        changePassword: jest.fn().mockResolvedValue(new Error('UserNotFoundException')),
      });

      await expect(controller.changePasswordPost(mockChangePasswordDto, mockReq, mockRes))
        .rejects.toThrow(BadRequestException);
    });

    it('should log error and throw BadRequestException on exception', async () => {
      const err = new Error('Unexpected');
      mockHelperService.get.mockRejectedValueOnce(err);

      await expect(controller.changePasswordPost(mockChangePasswordDto, mockReq, mockRes))
        .rejects.toThrow(BadRequestException);
    });
  });

  //delete profile pic
  describe('/delete-profile-picture', () => {
    let mockReq: any;
    let mockRes: any;

    beforeEach(() => {
      mockReq = {
        user: { uid: 123, email: '<EMAIL>' },
      };
      mockRes = {};

      mockHelperService.getHelper.mockResolvedValue({
        deleteImage: jest.fn().mockResolvedValue({ type: 'success' }),
        setCookie: jest.fn(),
      });
      mockHelperService.get.mockResolvedValue({
        getUserByEmail: jest.fn().mockResolvedValue({}),
        generateSessionTokens: jest.fn().mockResolvedValue({ idToken: 'mockToken' }),
      });
    });

    it('should delete profile picture and update cookies', async () => {
      const result = await controller.deleteProfilePicture(mockReq, mockRes);
      expect(result).toEqual({ type: 'success' });
    });

    it('should return error response if no user in request', async () => {
      mockReq.user = null;
      const result = await controller.deleteProfilePicture(mockReq, mockRes);
      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while deleting profile image.',
      });
    });

    it('should log error and return error response on exception', async () => {
      mockHelperService.getHelper.mockRejectedValueOnce(new Error('Unexpected'));
      const result = await controller.deleteProfilePicture(mockReq, mockRes);
      expect(result.type).toBe('error');
    });
  });

  //confirm delete account
  describe('/confirm-delete-account', () => {
    let mockReq: any;
    let mockRes: any;

    beforeEach(() => {
      mockReq = {
        cookies: { mockSsoCookie: 'token' },
      };
      mockRes = {
        redirect: jest.fn(),
      };

      mockHelperService.get.mockResolvedValue({ confirmDeleteAccount: jest.fn().mockResolvedValue({ type: 'success' }) });
      mockHelperService.getHelper.mockResolvedValue({
        decodeJWTToken: jest.fn().mockResolvedValue({
          data: { email: 'encryptedEmail', id: 'encryptedId', lgid: '1' },
        }),
        showDeleteButton: jest.fn().mockResolvedValue('Y'),
        clearCacheAndCookies: jest.fn(),
        setBulkCookie: jest.fn(),
      });

      (mockConfigService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'ssoCookie') return 'mockSsoCookie';
        if (key === 'slCookie') return 'mockSlCookie';
        return 'mock';
      });

      // Mock crypto decrypt
      mockCryptoHelper.decrypt.mockImplementation((val) => val);
    });

    it('should confirm delete and redirect on success', async () => {
      const result = await controller.confirmDeleteAccount(mockRes, mockReq);
      expect(mockRes.redirect).toHaveBeenCalledWith('/auth/login');
      expect(result).toBeUndefined();
    });

    it('should return failed if permission denied', async () => {
      mockHelperService.getHelper.mockResolvedValueOnce({
        decodeJWTToken: jest.fn().mockResolvedValue({
          data: { email: 'encryptedEmail', id: 'encryptedId', lgid: '1' },
        }),
        showDeleteButton: jest.fn().mockResolvedValue('N'),
      });
      const result = await controller.confirmDeleteAccount(mockRes, mockReq);
      expect(result).toEqual({ type: 'failed', msg: 'Invalid request' });
    });

    it('should log error and return error on exception', async () => {
      mockHelperService.getHelper.mockRejectedValueOnce(new Error('Unexpected'));
      const result = await controller.confirmDeleteAccount(mockRes, mockReq);
      expect(result).toBeInstanceOf(Error);
    });
  });

});
