import { BadRequestException, Inject, Injectable , InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../services/user.service';
import { JwtService } from '@nestjs/jwt';
import { CryptoHelper } from '../../helper/helper.crypto';
import { Logger } from '../../logging/logger';
import { UserMgmtCommunityService } from '../services/communication/usermgmt.community.service';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { User } from '../../db/mongo/schema/user/user.schema';
import { VIEW_PAGES } from '../../auth/config/view.constants';
import { ResponseCookieType, ViewData } from '../../common/typeDef/auth.type';
import * as moment from 'moment';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { GoogleAnalytics } from '../../helper/helper.ga4';
import { IUserRepository, UserRepository } from '../repositories/user/user.repository';
import { Utility } from './../../common/util/utility';
import { AuthService } from '../../auth/services/auth/auth.service';
import { InjectRepository } from '@nestjs/typeorm';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
import { Repository } from 'typeorm';
import { CachingService } from '../../caching/caching.service';
import { SentinelUsersRole } from '../../db/mysql/entity/sentinel-users-roles.entity';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { GdprRequest } from '../../db/mysql/entity/gdpr-request.entity'; 
import { In } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { SentinelRole } from '../../db/mysql/entity/sentinel-roles.entity';
import { deactivateUserByEmailDto } from '../../internal/dto/deactivate-user-byemail.dto';
import * as drupalHash from 'drupal-hash';

interface UserParams {
  client_id: string;
  user_email: string;
  user_name?: string;
  phone_no?: string;
  country_id?: string | number;
  country_code?: string;
  city_id?: string | number;
  user_roles?: string[] | null;
  user_type?: string;
  overwrite?: string | number;
  lms_url?: string;
  gid?: number;
  sso_request?: string;
  email_block?: string;
  first_name?: string;
  last_name?: string;
}

@Injectable()
export class UserHelper {
  @Inject() private readonly helperService: HelperService;
  @Inject() private readonly configService: ConfigService;
  @Inject(JwtService) private readonly jwtService: JwtService;
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;
  @InjectRepository(SentinelUser) private cloud6SentinelUserRepository: Repository<SentinelUser>;
  @InjectRepository(SentinelUsersRole) private cloud6SentinelUserRoleRepository: Repository<SentinelUsersRole>;
  @InjectRepository(GdprRequest) private gdprRequestRepository: Repository<GdprRequest>;
  @Inject(EventEmitter2) private readonly eventEmitter: EventEmitter2;
  @InjectRepository (SentinelRole) private cloud6RoleRepository: Repository<SentinelRole>;

  #LRS_OBJECT_TYPE_USERAPIV1 = 'accounts_internal_userapiv1';

  /**
   * User registration by email
   * @param Object userParams
   * @return json
   */
  async registerByEmail(userParams: UserParams) {
    let response = {};

    try {
      const [
        UserMgmtUtilityHelper,
        enterpriseService,
        emailHelper,
        userRepository,
        authService,
      ] = await Promise.all([
        this.helperService.getHelper('UserMgmtUtilityHelper'),
        this.helperService.get<EnterpriseService>(EnterpriseService),
        this.helperService.getHelper('EmailHelper'),
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<AuthService>(AuthService),
      ]);

      response = {
        type: 'error',
        msg: 'Some error occurred while registering user by email.',
      };

      const {
        client_id: clientKey = '',
        user_email: userEmail = '',
        user_name: userName = '',
        phone_no: phoneNo = '********',
        country_id: countryId = '',
        country_code: countryCode = '',
        city_id: cityId = '',
        user_roles: rawUserRoles = [],
        user_type: userType = '',
        overwrite: overwrite = '0',
        lms_url: lmsUrl,
        gid: gid,
        sso_request,
        email_block: isEmailBlock,
        first_name: userFirstName = '',
        last_name: userLastName = '',
      }: UserParams = userParams;
      const userRoles = Array.isArray(rawUserRoles) && rawUserRoles.length > 0 ? rawUserRoles : [];
      Logger.info('registerByEmail', {
        cityId: cityId
      });
      if (!Utility.isValidEmail(userEmail)) {
        response['msg'] = 'Please provide valid email address.';
        return response;
      }
      let mailTokens = {};
      Utility.validateClientRequest(clientKey, this.configService.get('clientSecret')); // throws exception if not found

      const userPasswd = this.getUserPassword();
      const phoneNoEmail = Utility.strReplace(phoneNo, '/s+/', '-');
      const overwriteIfExists = overwrite == 1 ? true : false;

      // Typecast the string to boolean using a custom type assertion
      const ssoRequest: boolean = sso_request === 'true' ? true : false;

      const autoProvisionRequest = userParams['auto_provision_request'] || false;
      const b2bSsoFirstLogin = userParams['b2b_sso_first_login'] || false;

      const varCountryId = countryId || this.configService.get('defaultCountryCode'); // Default is 'IN'
      const varCountryCode = countryCode || this.configService.get('defaultCountryCode');
      // let varUserName = (!utilFuncs.empty(userName)) ? userName : utilFuncs.ucfirst(utilFuncs.strstr(userEmail, '@', true));
      const varUserName = userName || Utility.ucfirst(userEmail.split('@')[0]);

      //TODO: can we move this to utility?
      const timeZone = await UserMgmtUtilityHelper.getTimezoneFromCountryCode(varCountryCode);

      let isB2B = false;
      // //To Check learner if it is not related to B2B or B2C
      const ifNotb2corb2b = await this.ifNotB2COrB2BLearner(Number(gid));

      if (gid && Number(gid) !== Number(this.configService.get('defaultLrsApplicationId')) && lmsUrl && !ifNotb2corb2b) {
        isB2B = true;
      } else if (autoProvisionRequest) {
        isB2B = true;
      }

      /* common declarations STARTS */
      const groupData = (await enterpriseService.getGroupByGid(Number(gid))).data || [];
      let accountSetupUrl = '';
      let referer = '';
      let enterpriseLogoUrl = '';
      let enterpriseName = '';
      let subDomain = '';
      if (groupData.length) {
        enterpriseLogoUrl = groupData[0].affiliateLogoUrl.length ? groupData[0].affiliateLogoUrl : '';
        enterpriseName = groupData[0].displayName || '';
        subDomain = groupData?.[0]?.urlHandle?.[0].split('.')?.[0];
      }
      let dashboardUrl = process.env.LMS_SITE_URL; // that's the default URL (lms.simplilearn.com)
      /* common declarations ENDS */

      let emailTemplate = '';
      let onBoardingUrl = '';
      let firstName = varUserName;

      // Check whether user already exists
      const result: Partial<User> = await userRepository.getUserByEmail(userEmail);
      if (result?.email) {
        if (result.status !== 1) {
          const response = {
            type: 'error',
            msg: 'Try using a different email address to create an account.',
            is_new: false,
            user: '',
            userAuthState: this.configService.get('deactivatedUserAuthState'),
          };

          Logger.debug('registerByEmail', {
            METHOD: this.constructor?.name + '@' + this.registerByEmail?.name,
            MESSAGE: 'Cannot register deactivated learner again.',
            REQUEST: { uid: result['uid'] },
            RESPONSE: { response: response },
            TIMESTAMP: new Date().getTime(),
          });

          return response;
        }

        const varUser = result;

        const displayName = varUser?.display_name;
        if (overwriteIfExists === true && displayName !== varUserName) {
          const field_display_name = varUserName;
          await userRepository.findOneAndUpdate({ email: userEmail }, { display_name: field_display_name });
        }

        const response = {
          type: 'success',
          msg: 'Email address already exists, please choose a different one',
          is_new: false,
          user: await UserMgmtUtilityHelper.getUserInfoForAPI(varUser),
        };

        // use case : user has purchased the course once again, without setting up his account
        // in that case, the account_setup flag will be 0, and we will need to send the account setup link
        
        // fetch the account setup flag
        // account setup flag is now determined by the accout_setup and not by varUser['field_account_setup']
        const accountSetupFlag = varUser?.account_setup;
        const accountSetupFlagVal = accountSetupFlag || 0; // 0 is the default
        if (accountSetupFlagVal == 0) {
          // user has not setup his account
          onBoardingUrl = dashboardUrl + '/dashboard/oob/onboarding';
          if (isB2B) {
            onBoardingUrl = dashboardUrl = encodeURIComponent(lmsUrl);
          }

          // let accountSetupMainUrl = process.env.ACCOUNT_SETUP_URL + '?setupAccount=true&redirect_url=' + encodeURIComponent(onBoardingUrl) + '&userEmail=' + encodeURIComponent(userEmail) + '&userName=' + userName + '&countryId=' + varCountryId + '&phoneNo=' + phoneNoEmail;
          // if(isB2B){
          //   accountSetupMainUrl = accountSetupMainUrl + '&isB2b=true&gid=' + gid + '&b2bLmsUrl=' + encodeURIComponent(lmsUrl);
          // }
          response['userAuthState'] = this.configService.get('newUserAuthState'); // value : 2 - new user
          // isB2B true is handled within the function getAccountSetupAndOOBUrl 
          let accountSetupMainUrl = await authService.getAccountSetupAndOOBUrl(result, gid);

          if (ssoRequest) {
            accountSetupMainUrl = accountSetupMainUrl + '&isSSORequest=true';
          }

          //referer for website
          referer = '&referer=WebsiteThankYouPage';
          accountSetupUrl = accountSetupMainUrl + referer;
          //Changing referer for email
          referer = '&referer=AccountSetupEmail';
          const accountSetupEmailUrl = accountSetupMainUrl + referer;

          response['defaultRedirectUrl'] = dashboardUrl;
          response['accountSetupUrl'] = accountSetupUrl;
          if (isB2B) {
            const fullName = displayName.split(' '); // utilFuncs.explode(" ", displayName, 2);
            firstName = fullName?.[0] || displayName;

            if (ssoRequest) {
              // B2B SSO login URL
              const b2bSsoLoginUrl =
                process.env.ACCOUNTS_SITE_URL + '/saml/service-provider/sp-initiated-sso/title/' + subDomain;

              emailTemplate = this.configService.get('b2b_with_sso_course');
              mailTokens = {
                enterpriseName: enterpriseName,
                enterpriseLogoUrl: enterpriseLogoUrl,
                firstName: firstName,
                courseName: '',
                b2bSsoLoginUrl: b2bSsoLoginUrl,
                affiliateLmsUrl: dashboardUrl,
              };
            } else {
              emailTemplate = this.configService.get('b2b_without_sso_template');
              mailTokens = {
                enterpriseLogoUrl: enterpriseLogoUrl,
                firstName: firstName,
                accountSetupLink: accountSetupEmailUrl,
                affiliateLmsUrl: dashboardUrl,
              };
            }
          } else {
            emailTemplate = this.configService.get('account_setup_template'); // @Swati : TODO - Add the template name to constants.php
            mailTokens = {
              name: userName == null || userName == '' ? 'Learner' : userName,
              redirect_link: accountSetupEmailUrl,
            };
          }
        } else {
          let loginPageUrl = process.env.ACCOUNTS_SITE_URL + '?returnLogin=true&useremail=' + userEmail;

          // construct the redirect url back to website's thank you page, if a user has made a purchase,
          // redirect the user to the appropriate page
          if (isB2B) {
            onBoardingUrl = dashboardUrl = lmsUrl;
          }

          response['userAuthState'] = this.configService.get('existingUserAuthState'); // that's the default value (1 : user has an active account & is logged in)
          response['dashboardUrl'] = dashboardUrl;
          response['loginPageUrl'] = loginPageUrl;

          if (isB2B) {
            const fullName = displayName.split(' '); //utilFuncs.explode(" ", displayName, 2);
            firstName = fullName?.[0] || displayName;
            if (ssoRequest) {
              // B2B SSO login URL
              const b2bSsoLoginUrl =
                process.env.ACCOUNTS_SITE_URL + '/saml/service-provider/sp-initiated-sso/title/' + subDomain;

              emailTemplate = this.configService.get('b2b_with_sso_course');
              mailTokens = {
                enterpriseName: enterpriseName,
                enterpriseLogoUrl: enterpriseLogoUrl,
                firstName: firstName,
                courseName: '',
                b2bSsoLoginUrl: b2bSsoLoginUrl,
                affiliateLmsUrl: dashboardUrl,
              };
            } else {
              emailTemplate = this.configService.get('existing_b2b_without_sso_course');
              mailTokens = {
                enterpriseName: enterpriseName,
                enterpriseLogoUrl: enterpriseLogoUrl,
                firstName: firstName,
                b2bLmsUrl: dashboardUrl,
                affiliateLmsUrl: dashboardUrl,
              };
            }
          } else {
            emailTemplate = this.configService.get('existing_learner_template');
            mailTokens = {
              name: userName,
              redirect_link: dashboardUrl,
            };
          }
        }
        let mailResult = {};
        // send email to learner for B2C, B2B with SSO & B2B without SSO
        if (isB2B) {
          // DO NOT send the account setup email if sso learner is logging in for the first time
          // registration happens in the background before the course is purchased, hence, the email should not get triggered
          if (!b2bSsoFirstLogin) {
            mailTokens['gid'] = gid;
            mailResult = await emailHelper.sendEmail(userEmail, emailTemplate, mailTokens);
          }
        } else {
          if (!Utility.isEmpty(isEmailBlock)) {
            response['email_block'] = isEmailBlock;
          } else {
            mailResult = await emailHelper.sendEmail(userEmail, emailTemplate, mailTokens);
          }
        }
        
        if (mailResult['status'] != 'success') {
          Logger.debug('registerByEmail', {
            METHOD: this.constructor?.name + '@' + this.registerByEmail?.name,
            MESSAGE: 'Failed to send account setup email to learner.',
            REQUEST: { uid: varUser['uid'], email: userEmail, emailTemplate: emailTemplate },
            RESPONSE: { response: mailResult },
            TIMESTAMP: new Date().getTime(),
          });
        }

        return response;
      }

      let userOptions = 0; //defualt value
      userOptions = userOptions | this.configService.get('userOptionsSetPasswordPending');
      const fullName = varUserName.split(' ');
      const signupDetail: Partial<User> = {
        email: userEmail,
        password: userPasswd,
        display_name: varUserName,
        first_name: userFirstName || fullName[0] || '',
        last_name: userLastName || fullName[1] || '',
        phone_no: phoneNo,
        country_code: varCountryCode,
        accept_agreement: true,
        user_options: userOptions,
        language: 'en',
        status: 1,
        account_setup: this.configService.get('userOptionsAccountSetupPending'),
        timezone: timeZone,
        password_created: this.configService.get('userOptionsAccountSetupPending'),
        user_type : '' 
      };

      const userService = await this.helperService.get<UserService>(UserService);
      const user = await userService.userRegistration(signupDetail , userRoles , userType);

      if (user?.email && user?.uid) {
        // construct the account setup URL here
        // create a token with following values, to prefill in the accunt setup page
        // user email, user name, phone number
        // redirectUrl will be login URL if he's not logged in
        // adding user details to the URL param to prefil the details in the account setup page - pavansb
        onBoardingUrl = dashboardUrl + '/dashboard/oob/onboarding';

        let accountSetupMainUrl =
          `${this.configService.get('baseUrl')}${this.configService.get('accountSetupUrl')}` +
          '?setupAccount=true&userEmail=' +
          encodeURIComponent(userEmail) +
          '&userName=' +
          encodeURIComponent(userName) +
          '&countryId=' +
          varCountryId +
          '&phoneNo=' +
          phoneNoEmail;
        if (isB2B) {
          dashboardUrl = encodeURIComponent(lmsUrl);
          accountSetupMainUrl =
            accountSetupMainUrl +
            '&redirect_url=' +
            encodeURIComponent(lmsUrl) +
            '&isB2b=true&gid=' +
            gid +
            '&b2bLmsUrl=' +
            encodeURIComponent(lmsUrl);
        } else {
          accountSetupMainUrl = accountSetupMainUrl + '&redirect_url=' + encodeURIComponent(onBoardingUrl);
        }
        if (ssoRequest) {
          accountSetupMainUrl = accountSetupMainUrl + '&isSSORequest=true';
        }

        //referer for website
        referer = '&referer=WebsiteThankYouPage';
        accountSetupUrl = accountSetupMainUrl + referer;

        const displayName = user['display_name'];
        response = {
          type: 'success',
          msg: 'Successfully created new user.',
          is_new: true,
          user: await UserMgmtUtilityHelper.getUserInfoForAPI(user),
          userAuthState: this.configService.get('newUserAuthState'), // value : 2 - new user
          defaultRedirectUrl: dashboardUrl,
          accountSetupUrl: accountSetupUrl,
        };

        /* Community user signup - BEGINS
        const communityUserGroupId = ((!utilFuncs.empty(userType) && 0 === utilFuncs.strcasecmp(userType, 'ATP')) || false !== utilFuncs.array_search('looper_affiliate_student', assignRoles)) 
        ? this.configService.get<string>('communityAtpUserGroupId') : this.configService.get<string>('communityDefaultUserGroupId');

        let signUpNewUserData = {
          'email' : userEmail,
          'name' : userName,
          'password' : userPasswd,
          'userGroupId' : communityUserGroupId,
          'user_agent' : '',
          'ip_address' : ''
        };
        const userMgmtCommunityService = await this.helperService.getHelper<UserMgmtCommunityService>(
          UserMgmtCommunityService,
        );

        const communityResult = await userMgmtCommunityService.signUpNewUser(signUpNewUserData);
        
        if (!utilFuncs.empty(communityResult['status']) && !utilFuncs.empty(communityResult['msg']) 
              && 0 !== utilFuncs.strcasecmp(communityResult['status'], 'success')) {
            Logger.error('registerByEmail',{
              METHOD: this.constructor?.name + '@' + this.registerByEmail?.name,
              MESSAGE: 'Failed community API --> signUpNewUser',
              REQUEST: {'email' : userEmail, 'username' : userName, 'userpassword' : userPasswd, 'communityGid' : communityUserGroupId},
              RESPONSE: communityResult,
              TIMESTAMP: new Date().getTime(),
            });
        }
        /* Community user signup - ENDS */

        //TODO: this should be last implementation
          /* LRS logging via scheduler - STARTS */
          const lrsInstanse = await this.helperService.getHelper('lrsHelper');
          const lrsUserData = {
            id: user?.uid || '',
            email: user?.email || '',
            name: user?.display_name || '',
            roles: user?.roles || {},
            timezone: user?.timezone || '',
          };
          const lrsData = {
            verb: 'register-by-email',
            objectType: this.#LRS_OBJECT_TYPE_USERAPIV1,
            objectId: user?.uid || '',
            dataVals: {
              client_id: clientKey,
              is_new: true,
            },
          }; 
          lrsInstanse.sendDataToLrs(lrsUserData, lrsData);
          /* LRS logging via scheduler - ENDS */
        //Changing referer for email
        referer = '&referer=AccountSetupEmail';
        const accountSetupEmailUrl = accountSetupMainUrl + referer;

        const { emailTemplate, mailTokens } = this.prepareEmailTemplateAndTokens(
          isB2B,
          ssoRequest,
          displayName,
          userName,
          gid,
          enterpriseLogoUrl,
          accountSetupEmailUrl,
          dashboardUrl,
          enterpriseName
        );
        // Send email only under allowed conditions
        let mailResult = { data: { status: '' } };
        if (isB2B && !ssoRequest) {
          if (!b2bSsoFirstLogin) {
            mailTokens['gid'] = 1930; // override if needed
            mailResult = await emailHelper.sendEmail(userEmail, emailTemplate, mailTokens);
          }
        } else if (!Utility.isEmpty(isEmailBlock)) {
          response['email_block'] = isEmailBlock;
        } else {
          mailResult = await emailHelper.sendEmail(userEmail, emailTemplate, mailTokens);
        }

        if (!ssoRequest && mailResult?.data?.status != 'success') {
          Logger.error('emailHelper.sendEmail', {
            METHOD: this.constructor?.name + '@' + this.registerByEmail?.name,
            MESSAGE: 'Failed to send account setup email to learner.',
            REQUEST: { uid: user?.uid, email: userEmail, emailTemplate: emailTemplate },
            RESPONSE: mailResult,
            TIMESTAMP: new Date().getTime(),
          });
        }
      }
    } catch (error: any) {
      Logger.error('registerByEmail Exception', {
        METHOD: this.constructor?.name + '@' + this.registerByEmail?.name,
        MESSAGE: 'Some error occurred while registering a user by email.',
        REQUEST: { userParams },
        EXCEPTION: { exceptionMsg: error?.message },
        TIMESTAMP: new Date().getTime(),
      });
    }
    return response;
  }

  prepareEmailTemplateAndTokens(
    isB2B: boolean,
    ssoRequest: boolean,
    displayName: string,
    userName: string,
    gid: number,
    enterpriseLogoUrl: string,
    accountSetupEmailUrl: string,
    dashboardUrl: string,
    enterpriseName: string
  ): { emailTemplate: string; mailTokens: Record<string, any> } {
    if (isB2B && !ssoRequest) {
      const fullName: string[] = displayName.split(' ');
      const firstName = fullName[0] || '';
      const emailTemplate = this.configService.get('b2b_without_sso_template');
      const mailTokens = {
        enterpriseLogoUrl,
        firstName,
        accountSetupLink: accountSetupEmailUrl,
        affiliateLmsUrl: dashboardUrl,
        gid,
      };
      return { emailTemplate, mailTokens };
    } else {
      const emailTemplate = this.configService.get('account_setup_template');
      const mailTokens: Record<string, any> = {
        name: Utility.isEmpty(userName) ? 'Learner' : userName,
        redirect_link: accountSetupEmailUrl,
        gid,
      };
  
      if (this.ifNotB2COrB2BLearner(gid)) {
        mailTokens['providerName'] = enterpriseName;
      }
  
      return { emailTemplate, mailTokens };
    }
  }


  /**
   * method to get password
   * @param length
   * @returns
   */
  getUserPassword(length = 10): string {
    const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz';
    const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
    const numericChars = '********';
    const specialChars = '!@#$%^&*()_-+=<>?/[]{}';

    const charSets = [lowercaseChars, uppercaseChars, numericChars, specialChars];
    const randomCharSet = (set: string) => set[Math.floor(Math.random() * set.length)];

    // Declare the password as a blank string.
    let pass = '';

    for (const charSet of charSets) {
      pass += randomCharSet(charSet);
    }

    // Fill up the rest of the password length with random characters
    for (let i = pass.length; i < length; i++) {
      const randomSet = charSets[Math.floor(Math.random() * charSets.length)];
      pass += randomCharSet(randomSet);
    }

    // Shuffle the characters in the password to make it random
    pass = pass
      .split('')
      .sort(() => 0.5 - Math.random())
      .join('');
    return pass;
  }

  /**
   *
   * @param userGid
   * @returns boolean
   */
  async ifNotB2COrB2BLearner(userGid: number): Promise<boolean> {
    if (!Utility.isEmpty(userGid)) {
      if (this.configService.get('b2bB2cGroupId').includes(userGid)) {
        return true;
      }
    }
    return false;
  }

  async getSSoCookieRedirectUrl(
    queryParam: { ssoRequest: any; groupId?: any },
    userSsoCookie: string,
  ): Promise<string> {
    try {
      const enterpriseService: EnterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
      const ssoRequest: any = queryParam.ssoRequest ? queryParam.ssoRequest : false;
      const groupId: number = queryParam.groupId ? queryParam.groupId : this.configService.get('defaultUserGroupId');
      const userSsoCookieDecode: any = await this.jwtService.decode(userSsoCookie);
      if (Utility.isEmpty(userSsoCookieDecode)) {
        return '';
      }
      let urlToRedirect = '';
      if (!Utility.isEmpty(userSsoCookieDecode?.data?.id) && !ssoRequest) {
        urlToRedirect = VIEW_PAGES.VIEW_ROUTES.PROFILES.PROFILE; 
      } else if (ssoRequest && Utility.isEmpty(userSsoCookieDecode?.data?.id)) {
        const groupDomain = await enterpriseService.getGroupByGid(Number(groupId));
        const subDomain = groupDomain?.data[0]?.urlHandle[0].split('.');
        const b2bSsoLoginUrl = `${this.configService.get('cloud6Url')}/saml/service-provider/sp-initiated-sso/title/${subDomain}`;
        urlToRedirect = b2bSsoLoginUrl;
      }
      return urlToRedirect;
    } catch (error: any) {
      Logger.error('getSSoCookieRedirectUrl', {
        METHOD: this.constructor.name + '@' + this.getSSoCookieRedirectUrl.name,
        MESSAGE: error.message,
        REQUEST: { queryParam: queryParam, userSsoCookie: userSsoCookie },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return '';
    }
  }

  async createCloud6SentinelUser(sentinelUserData) : Promise<Partial<User>> {
    try {
      return await this.cloud6SentinelUserRepository.save(sentinelUserData); 
    } catch (error) {
      console.log(error)
    }
  }

  async updateCloud6SentinelByUidOrMail(
    updateDetails: Partial<SentinelUser>
  ): Promise<boolean> {
    try {
      const { uid, mail } = updateDetails;
      if (!uid && !mail) {
        Logger.error('updateCloud6SentinelByUidOrMail', {
          METHOD: `${this.constructor.name}@${this.updateCloud6SentinelByUidOrMail.name}`,
          MESSAGE: "No valid filter.(uid/mail)",
          REQUEST: updateDetails,
          TIMESTAMP: Date.now(),
        });
        return false;
      } // Early return if both are missing
  
      // Perform the update directly
      delete updateDetails.uid;
      const updateCriteria= { ...(uid ? { uid:parseInt(uid.toString(),10) } : {}), ...(mail ? { mail } : {}) };
      const result = await this.cloud6SentinelUserRepository.update(
        updateCriteria,
        updateDetails
      );
      if(!Boolean(result.affected)) {
        Logger.error("MySQL data update/save error", {                                    
          METHOD: `${this.constructor.name}@updateCloud6SentinelByUidOrMail`,
          MESSAGE: "Failed to update or save MySQL data",
          REQUEST: updateDetails,
          TIMESTAMP: Date.now(),
        });
      }
      return Boolean(result.affected);
    } catch (error: any) {
      Logger.error('updateCloud6SentinelByUidOrMail', {
        METHOD: `${this.constructor.name}@${this.updateCloud6SentinelByUidOrMail.name}`,
        MESSAGE: error.message,
        REQUEST: updateDetails,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async clearCloud6SentinelUserRole(
    uid : number = null,
    rids : string[] = []
  ): Promise<boolean> {
    try {
      let result;
      if(rids.length){
        result = await this.cloud6SentinelUserRoleRepository.delete({
          rid: In(rids),
          uid: uid
        });
      } else if(uid) {
        result = await this.cloud6SentinelUserRoleRepository.delete({uid : uid});
      }
      if (result.affected && result.affected > 0) {
        return true;
      } else {
        throw new NotFoundException('Failed to clear user roles');
      }
    } catch (error: any) {
      Logger.error('clearCloud6SentinelUserRole', {
        METHOD: `${this.constructor.name}@${this.syncCloud6SentinelUserRole.name}`,
        MESSAGE: error.message,
        REQUEST: uid,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
  
      return false;
    }
  }

  async syncCloud6SentinelUserRole(
    createData : Partial<SentinelUsersRole[]>
  ): Promise<boolean> {
    try {
      const result = await this.cloud6SentinelUserRoleRepository.upsert(createData, { conflictPaths: ['uid', 'rid'] });
      if (result.identifiers.length === createData.length) {
        return true;
      } else {
        throw new NotFoundException('Failed to sync user roles');
      }
    } catch (error: any) {
      Logger.error('syncCloud6SentinelUserRole', {
        METHOD: `${this.constructor.name}@${this.syncCloud6SentinelUserRole.name}`,
        MESSAGE: error.message,
        REQUEST: createData,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
  
      return false;
    }
  }
  async getCloud6SentinelUserByUidOrMail(
    { mail, uid }: Partial<SentinelUser>
  ): Promise<{ status: boolean; data?: SentinelUser; msg: string }> {
    
    const response = { status: false, msg: "No data found" };
  
    try {
      if (!mail && !uid) return response; // Early return if both are missing
  
      const result = await this.cloud6SentinelUserRepository.findOne({
        where: [{ ...(mail ? { mail } : {}), ...(uid ? { uid } : {}) }],
      });
  
      if (result) {
        return { status: true, data: result, msg: "Fetch data successfully" };
      }
  
      return response;
    } catch (error: any) {
      Logger.error('getCloud6SentinelUserByUidOrMail', {
        METHOD: `${this.constructor.name}@${this.getCloud6SentinelUserByUidOrMail.name}`,
        MESSAGE: error.message,
        REQUEST: { mail, uid },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
  
      return response;
    }
  }

  async getB2bUserViewData(queryParam, dataPassToView: any): Promise<object> {
    try {
      const [enterpriseService, cloud6Service] = await Promise.all([
        this.helperService.get<EnterpriseService>(EnterpriseService),
        this.helperService.get<Cloud6Service>(Cloud6Service),
      ]);
      const groupId = queryParam?.gid ? queryParam.gid.trim() : this.configService.get('defaultUserGroupId');
      const b2bLmsUrl = queryParam?.b2bLmsUrl ? queryParam.b2bLmsUrl.trim() : '';
      const ssoRequest = queryParam?.ssoRequest ? queryParam.ssoRequest : false;
      const isB2b = queryParam?.isB2b === 'true' ? true : false;

      const groupData = await enterpriseService.getGroupByGid(Number(groupId));
      dataPassToView.atpLogoUrl = groupData?.data[0]?.affiliateLogoUrl || '';
      dataPassToView.isB2bStudent = true;
      dataPassToView.b2bLmsUrl = b2bLmsUrl;
      dataPassToView.isSso = ssoRequest;
      dataPassToView.groupId = isB2b ? parseInt(groupId, 10) : this.configService.get('defaultUserGroupId');
      
      const enterpriseSettings = await cloud6Service.getLmsEnterpriseSettings({ group_id: groupId });
      const cacheData = await this.lmsSettingsCacheData(enterpriseSettings.data.data);
      let showPhone = true;
      let isPhoneMandatory = false;
      if (cacheData?.phone_number?.value) {
        switch (cacheData.phone_number.value) {
          case 'Yes':
            showPhone = true;
            isPhoneMandatory = true;
            break;
          case 'No':
            showPhone = false;
            isPhoneMandatory = false;
            break;
          case 'Optional':
            showPhone = true;
            isPhoneMandatory = false;
            break;
          default:
            showPhone = true;
            isPhoneMandatory = false;
            break;
        }
      }
      dataPassToView.showPhone = showPhone;
      dataPassToView.isPhoneMandatory = isPhoneMandatory;
      dataPassToView.groupTitle = Utility.ucfirst(groupData?.data[0]?.displayName);
      return dataPassToView;
    } catch (error: any) {
      Logger.error('getB2bUserViewData', {
        METHOD: this.constructor.name + '@' + this.getB2bUserViewData.name,
        MESSAGE: error.message,
        REQUEST: { queryParam },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error.message;
    }
  }

  async lmsSettingsCacheData(enterpriseSettings: any[]) {
    try {
      if (!Array.isArray(enterpriseSettings) || enterpriseSettings.length === 0) {
        return null;
      }

      const lmsSettings: Record<string, any> = {};
      for (const value of enterpriseSettings) {
        const settingsName = value.preference_name.replace(/\s+/g, '_').toLowerCase();
        lmsSettings[settingsName] = { value: value.value };

        if (value.value === 'Custom' || value.preference_name === this.configService.get('lmsPreferenceName')) {
          lmsSettings[settingsName]['custom_value'] = value.custom_value;
        }
      }
      return lmsSettings;
    } catch (error: any) {
      Logger.error('lmsSettingsCacheData', {
        METHOD: this.constructor.name + '@' + this.lmsSettingsCacheData.name,
        MESSAGE: error.message,
        REQUEST: { enterpriseSettings },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error.message;
    }
  }

  /** // add function purpose here
   *
   * @param queryParam
   * @param req
   * @returns
   */
  async getDomainUrlInfo(redirect_url: string): Promise<{
    isB2BAndB2C: boolean;
    domainGid: string;
    domainUrl: string;
  }> {
    try {
      const redirectUrl = (redirect_url ?? '').trim() || '';
      if (redirectUrl && !Utility.isEmpty(redirectUrl)) {
        const parsedUrl = new URL(redirectUrl);
        const domainUrl: string = parsedUrl?.host;
        const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
        const groupDomainData = await enterpriseService.getGroupByDomain({ domain: domainUrl });
        if (groupDomainData?.data?.[0]) {
          const instance = await this.helperService.getHelper('UserHelper');
          const domainGid = groupDomainData?.data[0]?.gid ? groupDomainData?.data[0]?.gid : '';
          const isB2BAndB2C = await instance.ifNotB2COrB2BLearner(domainGid); // change function name as verifyB2bB2c  with isFsaType as flag
          return { isB2BAndB2C, domainGid, domainUrl };
        }
      }
      return { isB2BAndB2C: false, domainGid: '', domainUrl: redirectUrl };
    } catch (error: any) {
      Logger.error('getDomainUrlInfo', {
        METHOD: this.constructor.name + '@' + this.getDomainUrlInfo.name,
        MESSAGE: error.message,
        REQUEST: { urlDomain: redirect_url },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  /**
   *
   * @param userDetails
   * @param isB2b
   * @returns
   */
  async validateUserAccountSetup(
    userDetails: any,
    isB2b: boolean,
  ): Promise<{
    status: boolean;
    msg?: string;
  }> {
    let response: { status: boolean; msg?: string } = { status: false, msg: 'Something went wrong, please try again.' };
    try {
      if (userDetails?.isPhoneMandatoryFrontend && userDetails?.showPhoneFrontend) {
        const phoneNoValidator = /^[0-9]{7,}$/;
        if (!phoneNoValidator.test(userDetails?.phoneNo)) {
          response.msg = 'Please provide a valid phone number.';
          return response;
        }
      }
      if (isB2b) {
        const passwdResult = Utility.validatePasswd(
          userDetails?.userPassword,
          this.configService.get('passwordLength'),
          this.configService.get('maxPasswordLength'),
        );
        if (passwdResult.type === 'error') {
          Logger.error(passwdResult?.msg, passwdResult);
          response.msg = passwdResult?.msg;
          return response;
        }
      }
      response = {
        status: true,
        msg: 'success',
      };
      return response;
    } catch (error: any) {
      Logger.error('validateUserAccountSetup', {
        METHOD: this.constructor.name + '@' + this.validateUserAccountSetup.name,
        MESSAGE: error.message,
        REQUEST: { userDetails: userDetails, isB2b: isB2b },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  async handleAccountSetup(queryParam): Promise<{
    status: boolean;
    data: any;
    msg: string;
  }> {
    try {
      let subheading = 'failed';
      // todo : AS attribute atpLogoUrl is not used
      const isB2b: boolean = queryParam?.isB2b ? queryParam?.isB2b : false;
      const ssoRequest: boolean = queryParam?.ssoRequest ? queryParam?.ssoRequest : false;
      const b2bLmsUrl: string = queryParam?.b2bLmsUrl ? queryParam?.b2bLmsUrl : '';
      let dataPassToView: any = {};
      const viewDataFromQuery: ViewData = await this.setViewDataFromQueryParam(queryParam);
      if (Utility.isEmpty(viewDataFromQuery) && typeof viewDataFromQuery === 'object') {
        return { status: false, data: {}, msg: subheading };
      }
      dataPassToView = { ...dataPassToView, ...viewDataFromQuery };
      let defaultRedirect = '';
      if (!isB2b) {
        dataPassToView.isB2bStudent = false;
        dataPassToView.isSso = false;
        defaultRedirect = await this.configService.get('lmsSiteUrl');
      } else {
        const viewData = await this.getB2bUserViewData(queryParam, dataPassToView);
        dataPassToView = { ...dataPassToView, ...viewData };
        subheading = 'Welcome please confirm your details and setup password.';
        if (ssoRequest) {
          subheading = 'Welcome please connect your account';
        }
        defaultRedirect = decodeURIComponent(global.decodeURIComponent(b2bLmsUrl.trim()));
      }
      dataPassToView.validateRedirectUrl = Utility.validateRedirectUrl(queryParam?.redirect_url) || defaultRedirect;
      dataPassToView = { ...dataPassToView };
      return { status: true, data: dataPassToView, msg: subheading };
    } catch (error: any) {
      Logger.error('handleAccountSetup', {
        METHOD: this.constructor.name + '@' + this.handleAccountSetup.name,
        MESSAGE: error.message,
        REQUEST: { queryParam },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, data: {}, msg: error.message };
    }
  }
  async getFreemiumRedirectUrl(
    rewardData: any,
    signupDetail,
    assignmentToken: string,
    authTypeUser: string,
    redirectUrl: string,
  ): Promise<string> {
    try {
      if (
        rewardData?.respData &&
        rewardData?.respData?.status === 'success' &&
        rewardData?.respData?.skillUpReferralInfo?.enrolmentRestricted
      ) {
        redirectUrl =
          this.configService.get('freemiumAssignmentBlockRedirectUrl') +
          rewardData?.respData?.skillUpReferralInfo?.userRefCode;
      } else {
        redirectUrl = this.configService.get('freemiumAssignmentRedirectUrl') + assignmentToken;
      }
      const payload = {
        data: {
          email: this.cryptoHelper.encrypt(signupDetail.email),
          firstName: signupDetail?.first_name,
          lastName: signupDetail?.last_name,
          phoneNumber: signupDetail?.phone_no,
          authType: authTypeUser,
          loginMethodType: 'email',
          redirectUrl: redirectUrl,
        },
      };

      //jwt token
      const authHelperInstance: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const tokenCreated = await authHelperInstance.createSignedToken(payload, {
        secret: this.configService.get('jwtSecret'),
      });
      redirectUrl = this.configService.get('manageAuthRedirectUrl') + tokenCreated;
      return redirectUrl;
    } catch (error: any) {
      Logger.error('getFreemiumRedirectUrl', {
        METHOD: this.constructor?.name + '@' + this.getFreemiumRedirectUrl?.name,
        MESSAGE: error.message,
        REQUEST: {
          rewardData: rewardData,
          signupDetail: signupDetail,
          assignmentToken: assignmentToken,
          authTypeUser: authTypeUser,
          redirectUrl: redirectUrl,
        },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async setViewDataFromQueryParam(queryParam): Promise<ViewData> {
    try {
      const phoneWithCountryCode: string = queryParam?.phoneNo ? queryParam.phoneNo : '';
      const phoneParts: string[] = phoneWithCountryCode.split('-');
      let varCountryId: string;
      let phoneNo: string;
      if (phoneParts.length > 1) {
        varCountryId = phoneParts[0] !== queryParam?.phoneNo ? phoneParts[0] : '91';
        phoneNo = phoneParts[1].trim() || queryParam?.phoneNo;
      } else {
        varCountryId = '91';
        phoneNo = phoneParts[0];
      }
      // phoneNo = this.cryptoHelper.decrypt(phoneNo);

      // const userNameDecoded: string = this.cryptoHelper.decrypt(queryParam?.userName);
      const userNameDecoded: string = queryParam?.userName;
      const userName: string = queryParam?.userName ? userNameDecoded : '';
      // const userEmail: string = this.cryptoHelper.decrypt(queryParam?.userEmail);
      const userEmail: string = queryParam?.userEmail;
      const showPhone = true;
      const isPhoneMandatory = true;
      const groupIdForView: number = this.configService.get('defaultUserGroupId');
      const dataPassToView: ViewData = {
        showPhone,
        isPhoneMandatory,
        groupId: groupIdForView,
        accountSetupValidateUrl: '/validate/account-setup',
        formAction: '/user/account-setup',
        userEmail,
        userName,
        firstName: userName.split(' ', 2)[0],
        lastName: userName.split(' ', 2)[1] || '',
        varCountryId,
        phoneNo,
        pageSource: queryParam?.referer?.trim(),
      };
      return dataPassToView;
    } catch (error: any) {
      Logger.error('setViewDataFromQueryParam', {
        METHOD: `${this.constructor?.name}@${this.setViewDataFromQueryParam?.name}`,
        MESSAGE: error.message,
        REQUEST: queryParam,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }

  async updateUserData(
    accountSetupInfo,
    isAccountSetupPending = false,
  ): Promise<{
    status: boolean;
    msg: string;
    data: User | object;
  }> {
    const response: { status: boolean; msg: string; data: User | object } = { status: false, msg: 'failed', data: {} };
    try {
      // Step 1: Validate Country Code
      const varCountryCode: string =
        accountSetupInfo?.countryCode && /^[a-zA-Z]+$/.test(accountSetupInfo?.countryCode)
          ? accountSetupInfo?.countryCode
          : 'IN';

      // Step 2: Obtain Helpers
      const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
      // const userService: UserService = await this.helperService.get<UserService>(UserService);
      
      // Step 3: Get Timezone and Cloud6 sentinel User
      const [timezone, clUser] = await Promise.all([
        userMgmtUtilityHelperInstance.getTimezoneFromCountryCode(varCountryCode),
        this.getCloud6SentinelUserByUidOrMail({ mail: accountSetupInfo?.email }),
      ]);

      // Step 4: Update Additional Data
      accountSetupInfo['timezone'] = timezone;
      accountSetupInfo['uid'] = clUser?.data?.uid;

      // step 5: Update User Data in both sentinel_user and mongo
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const [ user , sentinelUser ] = await Promise.all([
        userRepository.findOneAndUpdate(
          { email: accountSetupInfo?.email },
          accountSetupInfo,
        ),
        this.updateCloud6SentinelByUidOrMail( 
          { mail : accountSetupInfo?.email, ...accountSetupInfo } )
      ])

      if (!user && !sentinelUser) {
        throw new BadRequestException('Something went wrong, please try again.');
      }
      response.status = true;
      response.msg = 'success';
      response.data = user;
      return response;
    } catch (error: any) {
      Logger.error('updateUserData', {
        METHOD: this.constructor?.name + '@' + this.updateUserData?.name,
        MESSAGE: error.message,
        REQUEST: { accountSetupInfo, isAccountSetupPending },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }

  async updateUserLoginTime(user: User) {
    const userRepository: IUserRepository = await this.helperService.get<IUserRepository>(UserRepository);
    const logTime = new Date();

    // updating login time in both sentinel_users(mysql) and mongo.
    const [ response , mysqlResponse , lrsInstance] = await Promise.all([
      userRepository.findOneAndUpdate(
        { email: user?.email || '' },
        { login: logTime, access: logTime, timezone: user?.timezone },
      ),
      this.updateCloud6SentinelByUidOrMail({ uid: Number(user?.uid) , login: logTime.getTime(), access: logTime.getTime(), timezone: user?.timezone }),
      this.helperService.getHelper('lrsHelper')
    ])

    if(this.configService.get('enableDrupalSync')) {
      this.syncUserDataWithMySQLDrupal({...user, login: logTime.getTime(), access: logTime.getTime()}); //update the user drupal data
    }

    return { response , mysqlResponse,lrsInstance };
  }

  async updateUserStatus(payload : {userId: number, email : string , status : number}): Promise<Boolean | Error> {
    try {
      const { userId, email , status } = payload;
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      // Update user status
      const [mysqlUpdate , mongoUpdate] = await Promise.all([
        this.cloud6SentinelUserRepository.update({ mail: email }, { status: status }),
        userRepository.findOneAndUpdate({ email: email }, { status: status }),
      ]);
      if(mysqlUpdate && mongoUpdate){
        if(this.configService.get('enableDrupalSync')) {
          await this.syncUserDataWithMySQLDrupal({uid: userId, status: status}); //update the user drupal data
         }
        return true;
      } else {
        throw new InternalServerErrorException('Failed to update user status');
      } 
    } catch (error: any) {
      Logger.error('updateUserStatus', {
        METHOD: this.constructor?.name + '@' + this.updateUserStatus?.name,
        MESSAGE: error.message,
        REQUEST: payload,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  
  async deactivateUserStatus( requestBody: deactivateUserByEmailDto ): Promise<Boolean | Error> {
    try {
      const { user_email: email } = requestBody;
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(email);
      if(!user){
        throw new BadRequestException('UserNotFoundException');
      }
      
      // Activate user status
      if(requestBody?.enable && requestBody?.enable == "1" && user.status == 0) {
        return await this.updateUserStatus({ userId: user.uid,email, status : 1 });
      } 

      // Deactivate user status
      if (user.status == 1) {
        return await this.updateUserStatus({ userId: user.uid, email, status : 0 });
      }
      throw new BadRequestException('UserAlreadyBlock');
    } catch (error: any) {
      Logger.error('deactivateUserStatus', {
        METHOD: this.constructor?.name + '@' + this.deactivateUserStatus?.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  getUserInfo(users: Partial<User[]>) {
    const userResponse = [];
    if (!users || users.length === 0) {
      return userResponse;
    }

    users.forEach((user) => {
      const pictureOutput = user?.profile_pic?.filename
        ? `<div class="user-picture"><img typeof="foaf:Image" src="${this.configService.get('s3BaseUrl')}/${user.profile_pic.filename}" alt="${user.email}'s picture" title="${user.email}'s picture" /></div>`
        : '';
      const roles = user['roles']?.map((userRole) => userRole?.roleName) || [];
      userResponse.push({
        name: user?.name || '',
        mail: user?.email,
        Active: user?.status ? 'Yes' : 'No',
        Language: user?.language && user?.language === 'en' ? 'English' : '',
        Picture: pictureOutput || "",
        Signature: user?.signature || '',
        displayName: user?.display_name,
        phone: user?.phone_no || null,
        location: user?.location || [],
        country_code: user?.country_code || null,
        rid: roles.join(','),
        'Created date': user['createdAt'] ? moment(new Date(user['createdAt'])).format('dddd, MMMM D, YYYY - HH:mm') : '',
        'Last access': user?.access ? moment(user?.access).format('ddd, MM/DD/YYYY - HH:mm') : '',
        'Last login': user?.login ? moment(user?.login).format('ddd, MM/DD/YYYY - HH:mm') : '',
        uid: user?.uid,
        user_type: user?.user_type || '',
        linkedin_status: user?.linkedin_status || 0,
        gid: user.user_groups.join(',') || '',
        timezone: user?.timezone,
      });
    });
    return userResponse;
  }

  getUserEmail(user: Partial<User>) {
    if (!user) {
      return [];
    }
    const roles = user['roles']?.map((userRole) => userRole?.roleName) || [];
    return [
      {
        active: user?.status ? 'Yes' : 'No',
        'authentication module': '',
        'authentication name': '',
        'created date': moment(new Date(user['createdAt'])).format('dddd, MMMM D, YYYY - HH:mm'),
        data: '',
        'e-mail': user?.email || '',
        language: 'English',
        'last access': moment(user?.access).format('ddd, MM/DD/YYYY - HH:mm'),
        'last login': moment(user?.login).format('ddd, MM/DD/YYYY - HH:mm'),
        name: user?.name,
        permission: null,
        profile_pic: user?.profile_pic || '',
        roles: roles.join(','),
        signature: user?.signature || '',
        uid: user?.uid,
      },
    ];
  }
  async sendGA4Events(data, cookieBody, frsRedirectUrl, recommended_courses, userType) {
    try {
      let eventName = 'Complete Login';
      if (data.authType === 'Signup') {
        eventName = 'Complete Signup';
      }
      eventName = eventName.replace(' ', '_');
      const ga4Event = {
        name: eventName.toLowerCase(),
        user_id: this.cryptoHelper.hash(data.email, 'sha256', 'hex'),
        sl_freemium_user: userType === this.configService.get<string>('freeUserType') ? 'true' : 'false',
        params: {
          url: frsRedirectUrl,
          type: 'google',
          sl_utm_src: cookieBody['sl_su_utmz'] || this.configService.get<string>('defaultFreemiumSignupUtm'),
          method: 'one tap',
          source: 'frs',
          utm_source: 'frs',
          branch_utm_source: 'frs',
          recommended_skillup_courses: recommended_courses,
        },
      };
      const cookie = cookieBody;
      let clientId = '';
      if (cookie['_ga']) {
        const cv = cookie['_ga'].split('.');
        const secTag = cv[2] || '';
        const thirdTag = cv[3] || '';
        clientId = `${secTag}.${thirdTag}`;
      }
      Logger.info('sendGA4Events', { eventName, ga4Event, clientId });
      const googleAnalytics: GoogleAnalytics = await this.helperService.get<GoogleAnalytics>(GoogleAnalytics);
      googleAnalytics.sendEvents(data.email, {
        user_id: data.email,
        eventsData: { name: eventName, param: ga4Event },
      });
    } catch (error: any) {
      Logger.log('sendGA4Events', error);
    }
  }

  async getSkillUpInfoInviteLink(userDetails: Partial<User>, data, redirectUrl: string, calendarUrl: string) {
    try {
      const refRewardData = { user_id: userDetails?.uid };
      const paperclipService: PaperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
      const rewardData = await paperclipService.getSkillupReferralRewardInfo(refRewardData);

      const refCode = rewardData?.respData?.skillUpReferralInfo?.userRefCode || '';
      const usermgmtCommunityService: UserMgmtCommunityService = await this.helperService.get<UserMgmtCommunityService>(
        UserMgmtCommunityService,
      );

      const refLink = await usermgmtCommunityService.getSkillupReferralInviteLink(
        'skillup-learner-referral',
        'drip',
        'enrollment-restriction',
        refCode,
        this.configService.get('skillUpReferralBaseUrl'),
      );
      const skillup_ref_link = refLink ? refLink['url'] : '';
      const email = data?.email || '';

      const skillupInviteData = {
        slFreemiumUser: userDetails?.user_type,
        refCode,
        refLink: skillup_ref_link,
        email,
        hashedEmail: this.cryptoHelper.hash(email, 'sha256', 'hex'),
        firstName: data?.firstName,
        lastName: data?.lastName,
        phoneNumber: data?.phoneNumber,
        authType: data?.authType,
        loginMethodType: data?.loginMethodType,
        redirectUrl,
        calendar_url: calendarUrl,
      };
      return { refCode, skillup_ref_link, skillupInviteData };
    } catch (error: any) {
      Logger.log('getSkillUpInfoAndInviteLink', error);
      throw error;
    }
  }
  async handleSkillupCookieActions(cookieBody, data) {
    try {
      const response: {
        redirectUrl: string;
        cookieValue: ResponseCookieType[];
        clearCookie: boolean | false;
        clearCookieData: Array<string>;
      } = {
        redirectUrl: '',
        cookieValue: [],
        clearCookie: false,
        clearCookieData: [],
      };
      const eventData : any = {};

      response.redirectUrl = data?.redirect_url || '';
      const userRepository: IUserRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userDetails: Partial<User> = await userRepository.getUserByEmail(data?.email);
      const userType = userDetails?.user_type;
      // Handle skillUpUserReferralCode
      if (userDetails?.status) {
        const skillupResult = await this.getSkillUpInfoInviteLink(
          userDetails,
          data,
          data?.redirectUrl,
          data?.calendar_url,
        );

        response.cookieValue.push({
          name: 'skillUpUserReferralCode',
          value: skillupResult?.refCode,
          options: { expires: new Date(Date.now() + 7776000) },
        });
      }

      // Handle calendarRedirect
      const calendarUrl = data?.calendar_url?.trim() || '';
      eventData.type = data.loginMethodType;
      if (calendarUrl) {
        response.cookieValue.push({ name: 'calendarRedirect', value: calendarUrl, options: { expires: 3600 * 1000 } });
      }
      // Handle skillUpOneTapRedirectCookie
      const skillUpOneTapCookie = this.configService.get('skillUpOneTapRedirectCookie');
      const skillupHomeRedirect = cookieBody[skillUpOneTapCookie] ? 1 : 0;
      if (skillupHomeRedirect) {
        const skillupRedirectUrl = this.configService.get('sheldonSiteUrl') + cookieBody[skillUpOneTapCookie];
        response.cookieValue.push({ name: skillUpOneTapCookie, value: 0, options: { expires: new Date(0) } });
        response.redirectUrl = decodeURIComponent(skillupRedirectUrl);
      }

      // Handle skillUpQuestionnaireRedirectCookie
      const skillupQuestionnaireCookie = this.configService.get('skillUpQuestionnaireRedirectCookie');
      const skillupQuestionnaireRedirect = cookieBody[skillupQuestionnaireCookie] ? 1 : 0;
      if (skillupQuestionnaireRedirect && data.authType === 'Signup') {
        response.cookieValue.push({ name: skillupQuestionnaireCookie, value: 0, options: { expires: new Date(0) } });
        response.redirectUrl = this.configService.get('skillUpQuestionnaireUrl');
      }

      // Handle skillUpQuizRedirectCookie
      const skillupQuizCookie = this.configService.get('skillUpQuizRedirectCookie');
      const skillup_quiz_redirect = cookieBody[skillupQuizCookie] ? 1 : 0;
      if (skillup_quiz_redirect && data.authType === 'Signup') {
        response.cookieValue.push({ name: skillupQuizCookie, value: 0, options: { expires: new Date(0) } });
        response.redirectUrl = this.configService.get('skillUpQuizUrl');
      }

      // Handle skillUpQuestionnaireDataSubmitted
      const skillupQuestionnairesSubmitted = this.configService.get('skillUpQuestionnaireDataSubmitted');
      const skillupQuestionnaireDataSubmitted = cookieBody[skillupQuestionnairesSubmitted] ? 1 : 0;
      if (skillupQuestionnaireDataSubmitted && data.authType === 'Signup') {
        response.cookieValue.push({
          name: skillupQuestionnairesSubmitted,
          value: 0,
          options: { expires: new Date(0) },
        });
        response.redirectUrl = this.configService.get('sheldonSiteUrl')+'/skillup-free-online-courses';
      }

      // Handle skillUpOriginRedirectCookie
      const skillupOriginCookie = this.configService.get('skillUpOriginRedirectCookie');
      const skillupOriginRedirect = cookieBody[skillupOriginCookie] || '';
      if (skillupOriginRedirect && data.authType !== 'Signup') {
        response.cookieValue.push({ name: skillupOriginCookie, value: 0, options: { expires: new Date(0) } });
        response.redirectUrl = skillupOriginRedirect;
      }

      // Handle skillUpFrsSignup and related cookies
      const defaultFreemiumUtm = this.configService.get('defaultFreemiumSignupUtm');
      const skillupFrsSignup = cookieBody['isFrsPage'] ? 1 : 0;
      const utmData = cookieBody['sl_su_utmz'] || defaultFreemiumUtm;
      const utmDetails = utmData.split('|');

      if (skillupFrsSignup && data.authType === 'Signup') {
        const frsCookieData = Utility.getFrsUtm(utmDetails);
        response.cookieValue.push({ name: 'slUuUtmz', value: frsCookieData, options: { expires: 3600 * 1000 } });
        const instance = await this.helperService.getHelper('UsermgmtCommunityHelper');
        instance.updateUserSignupUtm({ email: data?.email, utm_source: frsCookieData });
        response.cookieValue.push({ name: 'isFrsPage', value: 0, options: { expires: new Date(0) } });
      } else if (skillupFrsSignup && data.authType === 'Login') {
        const frsCookieData = Utility.getFrsUtm(utmDetails);
        response.cookieValue.push({ name: 'slUuUtmz', value: frsCookieData, options: { expires: 3600 * 1000 } });
        response.cookieValue.push({ name: 'isFrsPage', value: 0, options: { expires: new Date(0) } });
      }

      // Handle frsOneTimeRedirectCookie
      const frsOneTimeCookie: string = this.configService.get('frsOneTapRedirectCookie');
      const skillup_frs_redirect = cookieBody[frsOneTimeCookie] ? 1 : 0;

      if (skillup_frs_redirect) {
        const recommended_courses = cookieBody['frsSkillupCourses'] || '';
        const frsRedirectUrl = decodeURIComponent(cookieBody[frsOneTimeCookie]);
        response.clearCookie = true;
        response.cookieValue.push({ name: 'isFrsPage', value: 0 });
        response.clearCookieData.push(frsOneTimeCookie);
        
        //Webengage event
        const eventParam = {
          ...eventData,
          'sl_utm_src': cookieBody['sl_su_utmz'] || this.configService.get<string>('defaultFreemiumSignupUtm'),
          'method': 'one tap',
          'source': 'frs',
          'utm_source': 'frs',
          'branch_utm_source': 'frs',
          'category id': cookieBody['categoryId'] || '',
          'category name': cookieBody['categoryName'] || '',
          'recommended_skillup_courses': recommended_courses,
          'url': decodeURIComponent(cookieBody['simplilearn_first_page']),
          'email': data.email,
        }
        
        //triggering webengage event based on authType
        if (data.authType === 'Signup') {
          this.eventEmitter.emit('user.signup', {
            email: data.email,
            gid: data.gid,
            eventName: 'Complete Signup',
            payload: eventParam,
          });
        } else {
          this.eventEmitter.emit('user.login',{
            email: data.email,
            gid: data.gid,
            eventName: 'Complete Login',
            payload: eventParam,
          });
        }

        // TODO: Send events to GA4
        this.sendGA4Events(data, cookieBody, frsRedirectUrl, recommended_courses, userType);
        response.redirectUrl = frsRedirectUrl;
      }
      return response;
    } catch (error: any) {
      Logger.log('handleSkillupCookieActions', error);
      throw error;
    }
  }

  async handleB2bB2cRedirect(gid, redirectUrl): Promise<string> {
    if (!Utility.isEmpty(gid)) {
      const isUserB2bB2c = await this.ifNotB2COrB2BLearner(gid);
      if (isUserB2bB2c) {
        redirectUrl = this.configService.get('lmsSiteUrl');
      }
    }
    return redirectUrl;
  }

  async getUserEnterpriseAndCourses(userInfo) {
   const cloud6Service: Cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
    const learnerCourseAndResponse = await cloud6Service.getUserEnterpriseList({
      uid: userInfo?.uid,
      email: userInfo?.email,
    });
    return { userEnterpriseList:learnerCourseAndResponse.enterpriseList, learnerCoursesRecords: learnerCourseAndResponse.learnerCourses };
  }
  async isUserMemberOfGroup(uid: string, gId: string): Promise<boolean> {
    try { 
    const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
    const userEnterpriseList = await enterpriseService.getUserEnterpriseList({ uid });
    return userEnterpriseList?.enterpriseList?.length === 0 ? false : true;
    } catch (error: any) {
      Logger.error('isUserMemberOfGroup', {
        METHOD: this.constructor.name + '@' + this.isUserMemberOfGroup.name,
        MESSAGE: error.message,
        REQUEST: { uid, gId },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  async changePasswordAttemptLimit(userEmail: string): Promise<{ status: string; msg: string }> {
    try{
      const fpResetCount = this.configService.get<number>('fpResetCount');
      const fpResetWaitTime = this.configService.get<number>('fpResetWaitTime');
      const fpEmailInCacheTime = this.configService.get<number>('fpEmailInCacheTime');
  
      const cacheService = await this.helperService.get<CachingService>(CachingService);
          const cacheKey = `${userEmail}_reset`;
          const email_cache_count: number = (await cacheService.get(cacheKey)) || 0;
      
          if (email_cache_count >= fpResetCount) {
            cacheService.set(`${userEmail}_reset_timeout`, Date.now(), 60 * fpResetWaitTime);
            return {
              status: 'limit_exceeded',
              msg: `Attempt limit exceeded. Please try again after  ${fpResetWaitTime} minutes.`,
            };
          }
          cacheService.set(cacheKey, email_cache_count + 1, 60 * fpEmailInCacheTime);
      return {
        status: 'failed',
        msg: 'Some error occured while making Rate limit'
      };
    } catch (error : any){
      Logger.error('changeLogRequest', {
        METHOD: this.constructor.name + '@' + this.changePasswordAttemptLimit.name,
        MESSAGE: error.message,
        REQUEST: {userEmail,
          cacheKey: '${userEmail}_reset',
        },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

    // TODO : create BaseApp_Action_Helper_ReferEarn and BaseApp_Communication_Aiven class
    // migrate and modularise the below code after classes have been defined
  
    async syncReferEarnUrl(res, postData, userId): Promise<string | boolean> {
      try {
        if (!postData?.name || !postData?.email || !postData?.userKey) {
          Logger.error('syncReferEarnUrl', {
            METHOD: this.constructor?.name + '@' + this.syncReferEarnUrl?.name,
            MESSAGE: "name or email empty",
            REQUEST: postData,
            TIMESTAMP: Date.now(),
          });
    
          // Set cookie for 5 minutes
          const cookieName = `${this.configService.get('sync_cookie')}${userId}`;
          const cookieHelper = await this.helperService.getHelper('CookieHelper');
          cookieHelper.setCookie(res, cookieName, true, {
            expires: new Date(Date.now() + 300000),
            path: '/',
            domain: this.configService.get('ssoCookieDomain'),
          });
    
          return "name or email empty";
        }
    
        // Add additional fields like in PHP
        postData.course_id = 0;
        postData.time = Date.now();
  
        
        // TODO : need to be implemented after communication clases have been implemented
        // Send data to Kafka
        // const key = this.cryptoHelper.hash(JSON.stringify(postData), 'sha256', 'hex')
        // const kafkaResponse = await this.sendDataToKafka(topic, postData, key);
    
        // Check if Kafka message was published successfully
        // if (kafkaResponse?.offsets?.[0]?.offset !== undefined) {
        //   // Set cookie for 1 hour (success case)
        //   const cookieName = `${this.configService.get('sync_cookie')}${userId}`;
        //   const cookieHelper = await this.helperService.getHelper('CookieHelper');
        //   cookieHelper.setCookie(res, cookieName, true, {
        //     expires: new Date(Date.now() + 3600000), // 1 hour
        //     path: '/',
        //     domain: this.configService.get('ssoCookieDomain'),
        //   });
    
        //   return true;
        // }
    
        return false;
      } catch (error: any) {
        Logger.error('syncReferEarnUrl', {
          METHOD: this.constructor?.name + '@' + this.syncReferEarnUrl?.name,
          MESSAGE: error.message,
          REQUEST: postData,
          STACK: error.stack,
          TIMESTAMP: Date.now(),
        });
    
        return "Error processing request";
      }
    }
    async getAllRolesofUser(userId: string): Promise<SentinelUsersRole[] | Error> {
      try {
        // Query the repository to find all roles with the given userId
        const result: SentinelUsersRole[] = await this.cloud6SentinelUserRoleRepository.find({ where: { uid: parseInt(userId) } });
        return result;
      } catch (error: any) {
        Logger.error('getAllRolesofUser', {
          METHOD: `${this.constructor.name}@${this.getAllRolesofUser.name}`,
          MESSAGE: error.message,
          REQUEST: { userId },
          RESPONSE: error.stack,
          TIMESTAMP: Date.now(),
        });
        return [];
      }
    }
    async createEngagexRole(userId:number, rid: number): Promise<boolean | Error> {
      try {
        // Create a new role for the user
        const newRole = new SentinelUsersRole();
        newRole.uid = userId;
        newRole.rid = rid;
        await this.cloud6SentinelUserRoleRepository.save(newRole);
        return true;
      }
      catch(error: any){
        Logger.error('createEngagexRole', {
          METHOD: `${this.constructor.name}@${this.createEngagexRole.name}`,
          MESSAGE: error.message,
          REQUEST: { userId },
          RESPONSE: error.stack,
          TIMESTAMP: Date.now(),
        });
        return error;
      }
    }

  async updateExistingRole(userId: number, updatedRole: number, existingRole: number): Promise< Boolean | Error > {
    try {
        if (updatedRole) {
          const roleUpdate = await this.cloud6SentinelUserRoleRepository.update(
            { uid: userId, rid: existingRole },
            { rid: updatedRole },
          );
          if (!roleUpdate) {
            return false;
          }
          return true;
        }
    } catch (error:any) {
     Logger.error('updateExistingRole', {
      METHOD: `${this.constructor.name}@${this.updateExistingRole.name}`,
      MESSAGE: error.message,
      REQUEST: { userId, updatedRole },
      RESPONSE: error.stack,
      TIMESTAMP: Date.now(),
     });
     return error;
    }
  }

  async createGdprRequest(inputParams: Partial<GdprRequest>): Promise<number | boolean> {
      try {
        // Create and save the GDPR request
        const result = await this.gdprRequestRepository.save(inputParams);
        if (!result) {
          return false;
        }
        return result.id; // Return the auto-generated ID
      } catch (error: any) {
        Logger.error('createGdprRequest', {
          METHOD: `${this.constructor.name}@${this.createGdprRequest.name}`,
          MESSAGE: error.message,
          REQUEST: { inputParams },
          RESPONSE: error.stack,
          TIMESTAMP: Date.now(),
        });
        return false;
      }
  }
  async authRehash(inputParam: Object):Promise<string | Error>{
    try {
        const token = await this.cryptoHelper.createHmac(JSON.stringify(inputParam),
          this.configService.get<string>('cloud6ApiAuthSecretSalt'),
          this.configService.get('hmacAlgo'),
          this.configService.get('hmacEncoding')
        );
      return token;
    } catch (error: any) {
      Logger.error('authRehash', {
        METHOD: `${this.constructor.name}@${this.authRehash.name}`,
        MESSAGE: error.message,
        REQUEST: { inputParam },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return error;
    }
  }
  async updateUserOptions(userId: number, userPassword: string, userOptions: number) :Promise<Boolean | Error>{
    try {
      // Update the user password and user options in the database
      const userOptns = userOptions ?? 0;
      const hashPassword = drupalHash.hashPassword(userPassword);
      const isPasswordPending = (userOptns & this.configService.get('userOptionsSetPasswordPending')) !== 0;
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const [userRepoResult, sentinelUserResult] = await Promise.all([
        userRepository.findOneAndUpdate(
          { uid: userId },
          { password: hashPassword, user_options: Number(isPasswordPending) },
        ), 
        this.updateCloud6SentinelByUidOrMail({uid: userId, pass: hashPassword, user_options: Number(isPasswordPending)})
      ]);
      if (this.configService.get('enableDrupalSync') && userRepoResult) {
        await this.syncUserDataWithMySQLDrupal({
          userId,
          password: userPassword,
          user_options:  Number(isPasswordPending),
        });
      }
      if (!userRepoResult && !sentinelUserResult) {
        return false;
      }
      return true;
    } catch (error: any){
      Logger.error('updateUserOptions', {
        METHOD: `${this.constructor.name}@${this.updateUserOptions.name}`,
        MESSAGE: error.message,
        REQUEST: { userId, userPassword, userOptions },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      throw error;
    }
  }
  async setupWelcomeEmailToManager(userInfo: Partial<User>, groupInfo: any):Promise<Object | Error>{
    try {
      let emailResponse = {'status': 'error','msg': 'Some error occurred while scheduling welcome email for manager.'};
      if (!userInfo || !groupInfo) {
        emailResponse.msg = 'Invalid input parameters.';
        return emailResponse;
      }
      
      //cloud6 api to send welcome email
      const payload = {
        user:{
          uid: userInfo?.uid,
          email: userInfo?.email,
        },
        group:{
          ...groupInfo
        }
      };
      const cloud6Service: Cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const response = await cloud6Service.sendWelcomeEmailToManager(payload);
      if (!response) {
        return { 'status': 'error', 'msg': 'Some error occurred while scheduling welcome email for manager.' };
      }
      return {'status':'success','msg':'Welcome email scheduled successfully.'};
    } catch(error: any){
      Logger.error('setupWelcomeEmailToManager', {
        METHOD: `${this.constructor.name}@${this.setupWelcomeEmailToManager.name}`,
        MESSAGE: error.message,
        REQUEST: { userInfo, groupInfo},
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return error;
    }
  }

  async removeRoles(uid: number, rid: number, updatedRoles: any[]): Promise<boolean> {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
     
      //updating the user roles in both Mongoo and MySql
      const [userUpdate, sentinelUpdate] = await Promise.all([
        userRepository.findOneAndUpdate({ uid }, { roles: updatedRoles }),
        this.cloud6SentinelUserRoleRepository.delete({ uid, rid }),
      ]);

      if (!userUpdate || !sentinelUpdate.affected) {
        return false;
      }

      return true;
    } catch (error: any) {
      console.error("removeUserRoles:", {
        METHOD: `${this.constructor.name}@${this.removeRoles.name}`,
        MESSAGE: error.message,
        REQUEST: { uid, rid },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async createCloud6SentinelRole(roleData: { rid: number; name: string }): Promise<boolean> {
    try {
      const { rid, name } = roleData;
  
      // Check if role already exists
      const existing = await this.cloud6RoleRepository.findOne({ where: { rid } });
      if (existing) {
        Logger.error('createCloud6SentinelRole', {
          METHOD: `${this.constructor.name}@createCloud6SentinelRole`,
          MESSAGE: 'Role already exists',
          REQUEST: roleData,
          TIMESTAMP: Date.now(),
        });
        return false;
      }
  
      // Create and save new role
      const newRole = this.cloud6RoleRepository.create({ rid, name });
      await this.cloud6RoleRepository.save(newRole);
  
      return true;
    } catch (error: any) {
      Logger.error('createCloud6SentinelRole', {
        METHOD: `${this.constructor.name}@createCloud6SentinelRole`,
        MESSAGE: error.message,
        REQUEST: roleData,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async updateCloud6SentinelRole(roleData: { rid: number; name: string }): Promise<boolean> {
    try {
      await this.cloud6RoleRepository.update({ rid: roleData.rid }, { name: roleData.name });
      return true;
    } catch (error: any) {
      Logger.error('updateCloud6SentinelRole', {
        MESSAGE: error.message,
        REQUEST: roleData,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  // Method to synchronize user data with mySQL drupal database
  async syncUserDataWithMySQLDrupal(userData): Promise<boolean> {
    try {
      const user = await this.userSynchDataTransform(userData);
      const cloud6Service: Cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const response = await cloud6Service.syncUserDataWithMySQL(user);
      return response;
    } catch (error: any) {
      Logger.error('syncUserDataWithMySQL', {
        METHOD: `${this.constructor.name}@${this.syncUserDataWithMySQLDrupal.name}`,
        MESSAGE: error.message,
        REQUEST: { userData },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  // Method to create new user data with mySQL drupal database
  async createUserDataWithMySQLDrupal(userData: Partial<User>): Promise<boolean> {
    try {
      const user = await this.userSynchDataTransform(userData);
      const cloud6Service: Cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const response = await cloud6Service.createUserDataWithMySQL(user);
      return response;
    } catch (error: any) {
      Logger.error('createUserDataWithMySQLDrupal', {
        METHOD: `${this.constructor.name}@${this.createUserDataWithMySQLDrupal.name}`,
        MESSAGE: error.message,
        REQUEST: { userData },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }  

  async userSynchDataTransform(userData): Promise<any> {
    // Transform user data as needed for synchronization
    const transformedData: any = {};
    transformedData['uuid'] = userData.uid;
    transformedData['user_data'] = {
      ...(userData.uid && { uid: userData.uid}),
      ...(userData.email && { mail: userData.email }),
      ...(userData.name && { name: userData.name }),
      ...(userData.display_name && { display_name: userData.display_name }),
      ...(userData.login && { login: userData.login }),
      ...(userData.access && { access: userData.access }),
      ...(userData.timezone && { timezone: userData.timezone }),
      ...(userData.password && {pass: userData.password}),
      ...(userData.roles && {roles: userData.roles}),
      ...(userData.status) && { status: userData.status },
    }; 
    transformedData['user_field_data'] = {
    ...(userData.display_name && { display_name: userData.display_name }),
    ...(userData.user_options && {user_options: userData.user_options.toString()}),
    ...(userData.profile_visibility && { profile_visibility: userData.profile_visibility }),
    ...(userData.first_name && { first_name: userData.first_name }),
    ...(userData.last_name && { last_name: userData.last_name }),
    ...(userData.middle_name && { middle_name: userData.middle_name }),
    ...(userData.gender && { gender: userData.gender }),
    ...(userData.linkedin_url && { linkedin_url: userData.linkedin_url }),
    ...(userData.training_funded_by && { training_funded_by: userData.training_funded_by }),
    ...(userData.title && { title: userData.title }),   
    ...(userData.dob && { dob: userData.dob }),
    ...(userData.correspondence_address && { correspondence_address: userData.correspondence_address }),
    ...(userData.country_code && { country_code: userData.country_code }),
    ...(userData.location && { location: userData.location }),
    ...(userData.phone_no && { phone_no: userData.phone_no }),
    ...(userData.state && { state: userData.state }),
    ...(userData.profile_pic && { profile_pic: userData.profile_pic }),  
    ...(userData.accept_agreement && { accept_agreement: userData.accept_agreement }),
    ...(userData.account_setup && { account_setup: userData.account_setup }),
    ...(userData.password_created && { password_created: userData.password_created }),    
    };

    return transformedData;
  }

  async prepareRoleListForDrupalSync(role): Promise<any[]> {
    try {
      const roleMapping = role.reduce((acc, role) => {
        acc[role.rid] = role.roleName.toLowerCase();
        return acc;
      }, {});
      return roleMapping;
    } catch(error:any) {
      Logger.error('prepareRoleListForDrupalSync', {
        METHOD: `${this.constructor.name}@${this.prepareRoleListForDrupalSync.name}`,
        MESSAGE: error.message,
        REQUEST: { role },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return [];
    }
  }
}
    