
import { Test, TestingModule } from '@nestjs/testing';
import { User<PERSON>elper } from './user.helper';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { User } from '../../db/mongo/schema/user/user.schema';
import { UserType, ViewData } from '../../common/typeDef/auth.type';
import { BadRequestException, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { UserRepository } from '../repositories/user/user.repository';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
import { SentinelUsersRole } from '../../db/mysql/entity/sentinel-users-roles.entity';
import { GdprRequest } from '../../db/mysql/entity/gdpr-request.entity';
import { SentinelRole } from '../../db/mysql/entity/sentinel-roles.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { deactivateUserByEmailDto } from '../../internal/dto/deactivate-user-byemail.dto';
import { In, Repository } from 'typeorm';
import { CachingService } from '../../caching/caching.service';
import * as moment from 'moment';
import { VIEW_PAGES } from '../../auth/config/view.constants';
import { Utility } from '../../common/util/utility';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { UserMgmtCommunityService } from '../services/communication/usermgmt.community.service';
import { AuthService } from '../../auth/services/auth/auth.service';
import { UserService } from '../services/user.service';
import { AccountUser } from '../../db/mysql/entity/account-user.entity';

describe('UserHelper', () => {
  let userHelper: UserHelper;
  let sentinelUserRepoMock: Repository<SentinelUser>;
  let sentinelUserRoleRepoMock: Repository<SentinelUsersRole>;
  let gdprRequestRepoMock: Repository<GdprRequest>;
  let sentinelRoleRepoMock: Repository<SentinelRole>;
  // let drupalUserRepoMock: Repository<AccountUser>;

  const helperServiceMock = {
    get: jest.fn(),
    getHelper: jest.fn(),
  };

  const configServiceMock = {
    get: jest.fn((key: string) => {
      // Default mock values
      const config = {
        clientSecret: 'secret',
        defaultCountryCode: 'IN',
        userOptionsSetPasswordPending: 1,
        userOptionsAccountSetupPending: 0,
        newUserAuthState: 2,
        existingUserAuthState: 1,
        deactivatedUserAuthState: 99,
        baseUrl: 'http://base.url',
        accountSetupUrl: '/setup',
        account_setup_template: 'template_name',
        b2b_without_sso_template: 'b2b_without_sso_template',
        b2b_with_sso_course: 'b2b_with_sso_course',
        existing_b2b_without_sso_course: 'existing_b2b_without_sso_course',
        existing_learner_template: 'existing_learner_template',
        lmsSiteUrl: 'http://lms.site.url',
        s3BaseUrl: 'http://s3.url',
        fpResetCount: 5,
        fpEmailInCacheTime: 10,
        fpResetWaitTime: 15,
        cloud6ApiAuthSecretSalt: 'salt',
        hmacAlgo: 'sha256',
        hmacEncoding: 'hex',
        defaultUserGroupId: 1,
        b2bB2cGroupId: [100, 200],
        lmsPreferenceName: 'lms_pref',
        freemiumAssignmentRedirectUrl: 'http://freemium.url/',
        freemiumAssignmentBlockRedirectUrl: 'http://freemium-blocked.url/',
        manageAuthRedirectUrl: 'http://manage.url/',
        jwtSecret: 'jwt-secret',
        freeUserType: 'free',
        defaultFreemiumSignupUtm: 'default_utm',
        skillUpOneTapRedirectCookie: 'skillUpOneTapRedirectCookie',
        sheldonSiteUrl: 'http://sheldon.com',
        skillUpQuestionnaireRedirectCookie: 'skillUpQuestionnaireRedirectCookie',
        skillUpQuestionnaireUrl: 'http://questionnaire.url',
        skillUpQuizRedirectCookie: 'skillUpQuizRedirectCookie',
        skillUpQuizUrl: 'http://quiz.url',
        skillUpQuestionnaireDataSubmitted: 'skillUpQuestionnaireDataSubmitted',
        skillUpOriginRedirectCookie: 'skillUpOriginRedirectCookie',
        frsOneTapRedirectCookie: 'frsOneTapRedirectCookie',
        sync_cookie: 'sync_cookie',
        ssoCookieDomain: '.test.com',
        enableDrupalSync: false,
      };
      return config[key] || key;
    }),
  };

  const jwtServiceMock = {
    decode: jest.fn(),
  };

  const cryptoHelperMock = {
    encrypt: jest.fn((val) => `encrypted-${val}`),
    decrypt: jest.fn((val) => val.replace('encrypted-', '')),
    hash: jest.fn().mockReturnValue('hashed-value'),
    createHmac: jest.fn().mockReturnValue('hmac-token'),
  };

  const cloud6ServiceMock = {
    getGroupDetailsByGid: jest.fn(),
    getLmsEnterpriseSettings: jest.fn(),
    getGroupByDomain: jest.fn(),
    getUserEnterpriseList: jest.fn(),
    synchUser: jest.fn(),
    sendWelcomeEmailToManager: jest.fn(),
    syncUserDataWithMySQL: jest.fn(),
    createUserDataWithMySQL: jest.fn(),
  };

  const enterpriseServiceMock = {
    getUserEnterpriseList: jest.fn(),
    getGroupByGid: jest.fn(),
    getGroupByDomain: jest.fn(),
  };

  const userRepositoryMock = {
    getUserByEmail: jest.fn(),
    findOneAndUpdate: jest.fn(),
  };

  const authServiceMock = {
    getAccountSetupAndOOBUrl: jest.fn(),
  };

  const emailHelperMock = {
    sendEmail: jest.fn(),
  };

  const userServiceMock = {
    userRegistration: jest.fn(),
  };

  const eventEmitterMock = { emit: jest.fn() };
  const cachingServiceMock = { get: jest.fn(), set: jest.fn(), del: jest.fn() };
  const userMgmtUtilityHelperMock = {
    getTimezoneFromCountryCode: jest.fn().mockResolvedValue('UTC'),
    getUserInfoForAPI: jest.fn().mockResolvedValue({}),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserHelper,
        { provide: HelperService, useValue: helperServiceMock },
        { provide: ConfigService, useValue: configServiceMock },
        { provide: JwtService, useValue: jwtServiceMock },
        { provide: 'CRYPTO_HELPER', useValue: cryptoHelperMock },
        { provide: EventEmitter2, useValue: eventEmitterMock },
        {
          provide: getRepositoryToken(SentinelUser),
          useValue: {
            save: jest.fn(),
            update: jest.fn(),
            findOne: jest.fn(),
            delete: jest.fn(),
            create: jest.fn(),
            upsert: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(SentinelUsersRole),
          useValue: {
            delete: jest.fn(),
            upsert: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(GdprRequest),
          useValue: { save: jest.fn() },
        },
        {
          provide: getRepositoryToken(SentinelRole),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(AccountUser),
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    userHelper = module.get<UserHelper>(UserHelper);
    sentinelUserRepoMock = module.get<Repository<SentinelUser>>(getRepositoryToken(SentinelUser));
    sentinelUserRoleRepoMock = module.get<Repository<SentinelUsersRole>>(getRepositoryToken(SentinelUsersRole));
    gdprRequestRepoMock = module.get<Repository<GdprRequest>>(getRepositoryToken(GdprRequest));
    sentinelRoleRepoMock = module.get<Repository<SentinelRole>>(getRepositoryToken(SentinelRole));
    // drupalUserRepoMock = module.get<Repository<AccountUser>>(getRepositoryToken(AccountUser));


    helperServiceMock.get.mockImplementation(async (service: any) => {
      if (service === EnterpriseService) return enterpriseServiceMock;
      if (service === Cloud6Service) return cloud6ServiceMock;
      if (service === UserRepository) return userRepositoryMock;
      if (service === CachingService) return cachingServiceMock;
      if (service === AuthService) return authServiceMock;
      if (service === UserService) return userServiceMock;
      if (service === 'UserMgmtUtilityHelper') return userMgmtUtilityHelperMock;
      if (service === 'EmailHelper') return emailHelperMock;
      if (service === 'UserHelper') return userHelper;
      return {};
    });

    helperServiceMock.getHelper.mockImplementation(async (helper: string) => {
        if (helper === 'UserMgmtUtilityHelper') return userMgmtUtilityHelperMock;
        if (helper === 'EmailHelper') return emailHelperMock;
        return {};
    });

    jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);
    jest.spyOn(Utility, 'isValidEmail').mockReturnValue(true);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(userHelper).toBeDefined();
  });

  describe('registerByEmail', () => {
    const userParams = {
      client_id: 'client',
      user_email: '<EMAIL>',
      user_name: 'New User',
    };

    it('should register a new B2C user successfully', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userServiceMock.userRegistration.mockResolvedValue({ uid: 1, email: '<EMAIL>', display_name: 'New User' });
      emailHelperMock.sendEmail.mockResolvedValue({ data: { status: 'success' } });
      jest.spyOn(userHelper, 'getUserPassword').mockReturnValue('password');

      const result = await userHelper.registerByEmail(userParams);

      expect(result['type']).toBe('success');
      expect(result['is_new']).toBe(true);
      expect(userServiceMock.userRegistration).toHaveBeenCalled();
      expect(emailHelperMock.sendEmail).toHaveBeenCalled();
    });

    it('should handle existing user who has not set up account', async () => {
      const existingUser = { email: '<EMAIL>', status: 1, account_setup: 0, display_name: 'Old Name' };
      userRepositoryMock.getUserByEmail.mockResolvedValue(existingUser);
      authServiceMock.getAccountSetupAndOOBUrl.mockResolvedValue('http://setup.url');
      emailHelperMock.sendEmail.mockResolvedValue({ data: { status: 'success' } });

      const result = await userHelper.registerByEmail({
        ...userParams,
        user_email: '<EMAIL>',
        user_name: 'New Name',
        overwrite: '1',
      });

      expect(result['type']).toBe('error');
      expect(result['msg']).toContain('Email address already exists');
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        { display_name: 'New Name' },
      );
      expect(emailHelperMock.sendEmail).toHaveBeenCalled();
    });

    it('should return error for a deactivated user', async () => {
      const deactivatedUser = { email: '<EMAIL>', status: 0, uid: '123' };
      userRepositoryMock.getUserByEmail.mockResolvedValue(deactivatedUser);

      const result = await userHelper.registerByEmail({ ...userParams, user_email: '<EMAIL>' });

      expect(result['type']).toBe('error');
      expect(result['msg']).toContain('Try using a different email address');
      expect(result['userAuthState']).toBe(99);
    });

    it('should return error for invalid email format', async () => {
      jest.spyOn(Utility, 'isValidEmail').mockReturnValue(false);
      const result = await userHelper.registerByEmail({ ...userParams, user_email: 'invalid-email' });
      expect(result['msg']).toBe('Please provide valid email address.');
    });

    it('should throw error for invalid client request', async () => {
        jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => {
            throw new Error('Invalid client');
        });
        await expect(userHelper.registerByEmail(userParams)).rejects.toThrow('Invalid client');
    });

    it('should handle error during user registration and throw', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userServiceMock.userRegistration.mockRejectedValue(new Error('DB error'));
      jest.spyOn(userHelper, 'getUserPassword').mockReturnValue('password');

      await expect(userHelper.registerByEmail(userParams)).rejects.toThrow('DB error');
    });

    it('should handle B2B user registration with SSO', async () => {
        userRepositoryMock.getUserByEmail.mockResolvedValue(null);
        userServiceMock.userRegistration.mockResolvedValue({ uid: 1, email: '<EMAIL>', display_name: 'B2B User' });
        emailHelperMock.sendEmail.mockResolvedValue({ data: { status: 'success' } });
        enterpriseServiceMock.getGroupByGid.mockResolvedValue({ data: [{ urlHandle: ['sub.domain.com'] }] });
        jest.spyOn(userHelper, 'getUserPassword').mockReturnValue('password');

        const b2bParams = {
            ...userParams,
            user_email: '<EMAIL>',
            gid: 123,
            lms_url: 'http://b2b.lms.url',
            sso_request: 'true',
        };
        const result = await userHelper.registerByEmail(b2bParams);

        expect(result['type']).toBe('success');
        // For SSO request on creation, email is not sent
        expect(emailHelperMock.sendEmail).not.toHaveBeenCalled();
    });

    it('should handle existing B2B user with account setup complete', async () => {
        const existingUser = { email: '<EMAIL>', status: 1, account_setup: 1, display_name: 'B2B User' };
        userRepositoryMock.getUserByEmail.mockResolvedValue(existingUser);
        enterpriseServiceMock.getGroupByGid.mockResolvedValue({ data: [{ displayName: 'B2B Enterprise' }] });
        emailHelperMock.sendEmail.mockResolvedValue({ data: { status: 'success' } });

        const b2bParams = {
            ...userParams,
            user_email: '<EMAIL>',
            gid: 123,
            lms_url: 'http://b2b.lms.url',
        };
        const result = await userHelper.registerByEmail(b2bParams);

        expect(result['type']).toBe('error');
        expect(result['msg']).toContain('Email address already exists');
        expect(emailHelperMock.sendEmail).toHaveBeenCalledWith(
            '<EMAIL>',
            'existing_b2b_without_sso_course',
            expect.any(Object),
        );
    });
  });

  describe('getSSoCookieRedirectUrl', () => {
    it('should return profile URL if user id exists in cookie and not sso request', async () => {
      jwtServiceMock.decode.mockResolvedValue({ data: { id: '123' } });
      const result = await userHelper.getSSoCookieRedirectUrl({ ssoRequest: false }, 'cookie');
      expect(result).toBe(VIEW_PAGES.VIEW_ROUTES.PROFILES.PROFILE);
    });

    it('should return b2b sso login url for sso request without user id in cookie', async () => {
      jwtServiceMock.decode.mockResolvedValue({ data: {} });
      enterpriseServiceMock.getGroupByGid.mockResolvedValue({ data: [{ urlHandle: ['sub.domain.com'] }] });
      configServiceMock.get.mockReturnValue('http://cloud6.com');
      const result = await userHelper.getSSoCookieRedirectUrl({ ssoRequest: true, groupId: 1 }, 'cookie');
      expect(result).toBe('http://cloud6.com/saml/service-provider/sp-initiated-sso/title/sub');
    });

    it('should return empty string for empty cookie decode', async () => {
      jwtServiceMock.decode.mockResolvedValue(null);
      const result = await userHelper.getSSoCookieRedirectUrl({ ssoRequest: false }, 'cookie');
      expect(result).toBe('');
    });

    it('should return empty string if getGroupByGid fails', async () => {
      jwtServiceMock.decode.mockResolvedValue({ data: {} });
      enterpriseServiceMock.getGroupByGid.mockRejectedValue(new Error('Service Error'));
      const result = await userHelper.getSSoCookieRedirectUrl({ ssoRequest: true, groupId: 1 }, 'cookie');
      expect(result).toBe('');
    });
  });

  describe('createCloud6SentinelUser', () => {
    it('should save and return user data', async () => {
      const userData = { mail: '<EMAIL>' };
      (sentinelUserRepoMock.save as jest.Mock).mockResolvedValue(userData);
      const result = await userHelper.createCloud6SentinelUser(userData);
      expect(result).toEqual(userData);
      expect(sentinelUserRepoMock.save).toHaveBeenCalledWith(userData);
    });

    it('should throw an error if save operation fails', async () => {
      const userData = { mail: '<EMAIL>' };
      (sentinelUserRepoMock.save as jest.Mock).mockRejectedValue(new Error('DB Error'));
      await expect(userHelper.createCloud6SentinelUser(userData)).rejects.toThrow('DB Error');
    });
  });

  describe('updateCloud6SentinelByUidOrMail', () => {
    it('should update user by uid', async () => {
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await userHelper.updateCloud6SentinelByUidOrMail({ uid: 123, name: 'new name' });
      expect(result).toBe(true);
      expect(sentinelUserRepoMock.update).toHaveBeenCalledWith({ uid: 123 }, { name: 'new name' });
    });

    it('should update user by mail', async () => {
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await userHelper.updateCloud6SentinelByUidOrMail({ mail: '<EMAIL>', name: 'new name' });
      expect(result).toBe(true);
      expect(sentinelUserRepoMock.update).toHaveBeenCalledWith({ mail: '<EMAIL>' }, { name: 'new name' });
    });

    it('should return false if update fails', async () => {
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 0 });
      const result = await userHelper.updateCloud6SentinelByUidOrMail({ uid: 123, name: 'new name' });
      expect(result).toBe(false);
    });

    it('should return false if no uid or mail is provided', async () => {
      const result = await userHelper.updateCloud6SentinelByUidOrMail({ name: 'new name' });
      expect(result).toBe(false);
      expect(sentinelUserRepoMock.update).not.toHaveBeenCalled();
    });

    it('should return false on db error', async () => {
      (sentinelUserRepoMock.update as jest.Mock).mockRejectedValue(new Error('DB Error'));
      const result = await userHelper.updateCloud6SentinelByUidOrMail({ uid: 123, name: 'new name' });
      expect(result).toBe(false);
    });
  });

  describe('clearCloud6SentinelUserRole', () => {
    it('should clear all roles for a user if only uid is provided', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await userHelper.clearCloud6SentinelUserRole(123);
      expect(result).toBe(true);
      expect(sentinelUserRoleRepoMock.delete).toHaveBeenCalledWith({ uid: 123 });
    });

    it('should clear specific roles for a user if rids are provided', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockResolvedValue({ affected: 2 });
      const result = await userHelper.clearCloud6SentinelUserRole(123, ['1', '2']);
      expect(result).toBe(true);
      expect(sentinelUserRoleRepoMock.delete).toHaveBeenCalledWith({
        rid: In(['1', '2']),
        uid: 123,
      });
    });

    it('should throw NotFoundException if no roles are affected', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockResolvedValue({ affected: 0 });
      await expect(userHelper.clearCloud6SentinelUserRole(123)).rejects.toThrow(NotFoundException);
    });

    it('should return false on db error', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockRejectedValue(new Error('DB Error'));
      const result = await userHelper.clearCloud6SentinelUserRole(123);
      expect(result).toBe(false);
    });
  });

  describe('syncCloud6SentinelUserRole', () => {
    it('should return true on successful upsert', async () => {
      const createData = [{ uid: 1, rid: 2 }];
      (sentinelUserRoleRepoMock.upsert as jest.Mock).mockResolvedValue({ identifiers: [{}] });
      const result = await userHelper.syncCloud6SentinelUserRole(createData);
      expect(result).toBe(true);
    });

    it('should throw NotFoundException on failed upsert', async () => {
      const createData = [{ uid: 1, rid: 2 }];
      (sentinelUserRoleRepoMock.upsert as jest.Mock).mockResolvedValue({ identifiers: [] });
      await expect(userHelper.syncCloud6SentinelUserRole(createData)).rejects.toThrow(NotFoundException);
    });

    it('should return false on db error', async () => {
        const createData = [{ uid: 1, rid: 2 }];
        (sentinelUserRoleRepoMock.upsert as jest.Mock).mockRejectedValue(new Error('DB Error'));
        const result = await userHelper.syncCloud6SentinelUserRole(createData);
        expect(result).toBe(false);
      });
  });

  describe('getCloud6SentinelUserByUidOrMail', () => {
    it('should get user by mail', async () => {
      const user = { uid: 1, mail: '<EMAIL>' };
      (sentinelUserRepoMock.findOne as jest.Mock).mockResolvedValue(user);
      const result = await userHelper.getCloud6SentinelUserByUidOrMail({ mail: '<EMAIL>' });
      expect(result.status).toBe(true);
      expect(result.data).toEqual(user);
      expect(sentinelUserRepoMock.findOne).toHaveBeenCalledWith({ where: [{ mail: '<EMAIL>' }] });
    });

    it('should get user by uid', async () => {
      const user = { uid: 1, mail: '<EMAIL>' };
      (sentinelUserRepoMock.findOne as jest.Mock).mockResolvedValue(user);
      const result = await userHelper.getCloud6SentinelUserByUidOrMail({ uid: 1 });
      expect(result.status).toBe(true);
      expect(result.data).toEqual(user);
      expect(sentinelUserRepoMock.findOne).toHaveBeenCalledWith({ where: [{ uid: 1 }] });
    });

    it('should return not found if user does not exist', async () => {
      (sentinelUserRepoMock.findOne as jest.Mock).mockResolvedValue(null);
      const result = await userHelper.getCloud6SentinelUserByUidOrMail({ mail: '<EMAIL>' });
      expect(result.status).toBe(false);
      expect(result.msg).toBe('No data found');
    });

    it('should return error response on db error', async () => {
      (sentinelUserRepoMock.findOne as jest.Mock).mockRejectedValue(new Error('DB Error'));
      const result = await userHelper.getCloud6SentinelUserByUidOrMail({ mail: '<EMAIL>' });
      expect(result.status).toBe(false);
    });
  });

  describe('getB2bUserViewData', () => {
    it('should return correct view data for B2B user', async () => {
      const queryParam = { gid: '123', b2bLmsUrl: 'lms.url', ssoRequest: true, isB2b: 'true' };
      enterpriseServiceMock.getGroupByGid.mockResolvedValue({
        data: [{ affiliateLogoUrl: 'logo.url', displayName: 'Group Name' }],
      });
      cloud6ServiceMock.getLmsEnterpriseSettings.mockResolvedValue({ data: { data: [] } });
      jest.spyOn(userHelper, 'lmsSettingsCacheData').mockResolvedValue({ phone_number: { value: 'Yes' } });
      jest.spyOn(Utility, 'ucfirst').mockReturnValue('Group Name');

      const result = await userHelper.getB2bUserViewData(queryParam, {});
      expect(result).toHaveProperty('atpLogoUrl', 'logo.url');
      expect(result).toHaveProperty('isB2bStudent', true);
      expect(result).toHaveProperty('showPhone', true);
      expect(result).toHaveProperty('isPhoneMandatory', true);
    });

    it('should throw error when getGroupByGid fails', async () => {
      const queryParam = { gid: '123', b2bLmsUrl: 'lms.url', ssoRequest: true, isB2b: 'true' };
      enterpriseServiceMock.getGroupByGid.mockRejectedValue(new Error('Service Error'));
      await expect(userHelper.getB2bUserViewData(queryParam, {})).rejects.toThrow('Service Error');
    });
  });

  describe('lmsSettingsCacheData', () => {
    it('should return null for empty enterpriseSettings', async () => {
      expect(await userHelper.lmsSettingsCacheData([])).toBeNull();
    });

    it('should return null for null enterpriseSettings', async () => {
      expect(await userHelper.lmsSettingsCacheData(null)).toBeNull();
    });

    it('should correctly map enterprise settings', async () => {
      const enterpriseSettings = [
        { preference_name: 'Setting One', value: 'Value1' },
        { preference_name: 'Setting Two', value: 'Custom', custom_value: 'CustomValue2' },
        { preference_name: 'lms_pref', value: 'SomeValue', custom_value: 'CustomValue3' },
      ];

      const result = await userHelper.lmsSettingsCacheData(enterpriseSettings);

      expect(result).toEqual({
        setting_one: { value: 'Value1' },
        setting_two: { value: 'Custom', custom_value: 'CustomValue2' },
        lms_pref: { value: 'SomeValue', custom_value: 'CustomValue3' },
      });
    });

    it('should throw error on processing failure', async () => {
        const enterpriseSettings = [{preference_name: null}]; // Invalid data
        await expect(userHelper.lmsSettingsCacheData(enterpriseSettings)).rejects.toThrow();
    });
  });

  describe('getDomainUrlInfo', () => {
    it('should return domain info for a valid redirect_url', async () => {
      const url = 'https://test.domain.com/path';
      enterpriseServiceMock.getGroupByDomain.mockResolvedValue({ data: [{ gid: 123 }] });
      jest.spyOn(userHelper, 'ifNotB2COrB2BLearner').mockResolvedValue(false);
      const result = await userHelper.getDomainUrlInfo(url);
      expect(result).toEqual({ isB2BAndB2C: false, domainGid: 123, domainUrl: 'test.domain.com' });
    });

    it('should return default values for an empty redirect_url', async () => {
      const result = await userHelper.getDomainUrlInfo('');
      expect(result).toEqual({ isB2BAndB2C: false, domainGid: '', domainUrl: '' });
    });

    it('should return default values for a malformed redirect_url', async () => {
        const result = await userHelper.getDomainUrlInfo('not-a-url');
        expect(result).toEqual({ isB2BAndB2C: false, domainGid: '', domainUrl: 'not-a-url' });
      });

    it('should return default values if getGroupByDomain fails', async () => {
        const url = 'https://test.domain.com/path';
        enterpriseServiceMock.getGroupByDomain.mockRejectedValue(new Error('Service Error'));
        const result = await userHelper.getDomainUrlInfo(url);
        expect(result).toBeUndefined();
    });
  });

  describe('validateUserAccountSetup', () => {
    it('should return success for valid user details', async () => {
      const userDetails = { isPhoneMandatoryFrontend: true, showPhoneFrontend: true, phoneNo: '1234567' };
      const result = await userHelper.validateUserAccountSetup(userDetails, false);
      expect(result.status).toBe(true);
    });

    it('should return error for invalid phone number', async () => {
      const userDetails = { isPhoneMandatoryFrontend: true, showPhoneFrontend: true, phoneNo: '123' };
      const result = await userHelper.validateUserAccountSetup(userDetails, false);
      expect(result.status).toBe(false);
      expect(result.msg).toBe('Please provide a valid phone number.');
    });

    it('should return error for invalid password for b2b', async () => {
      jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'error', msg: 'Invalid password' });
      const userDetails = { userPassword: '123' };
      const result = await userHelper.validateUserAccountSetup(userDetails, true);
      expect(result.status).toBe(false);
      expect(result.msg).toBe('Invalid password');
    });

    it('should return success for valid password for b2b', async () => {
      let response : any = { type: 'success' }
      jest.spyOn(Utility, 'validatePasswd').mockReturnValue(response);
      const userDetails = { userPassword: 'ValidPassword1!' };
      const result = await userHelper.validateUserAccountSetup(userDetails, true);
      expect(result.status).toBe(true);
    });

    it('should return a default error message on exception', async () => {
        jest.spyOn(Utility, 'validatePasswd').mockImplementation(() => { throw new Error('test error')});
        const userDetails = { userPassword: 'ValidPassword1!' };
        const result = await userHelper.validateUserAccountSetup(userDetails, true);
        expect(result.status).toBe(false);
        expect(result.msg).toBe('Something went wrong, please try again.');
    });
  });

  describe('handleAccountSetup', () => {
    it('should handle non-b2b account setup', async () => {
      jest.spyOn(userHelper, 'setViewDataFromQueryParam').mockResolvedValue({} as ViewData);
      const result = await userHelper.handleAccountSetup({ isB2b: false });
      expect(result.data.isB2bStudent).toBe(false);
      expect(result.data.validateRedirectUrl).toBe('http://lms.site.url');
    });

    it('should handle b2b account setup', async () => {
      jest.spyOn(userHelper, 'setViewDataFromQueryParam').mockResolvedValue({} as ViewData);
      jest.spyOn(userHelper, 'getB2bUserViewData').mockResolvedValue({ groupTitle: 'B2B' });
      const result = await userHelper.handleAccountSetup({ isB2b: true, b2bLmsUrl: 'b2b.url' });
      expect(result.data.groupTitle).toBe('B2B');
    });

    it('should return error if setViewDataFromQueryParam returns empty', async () => {
        jest.spyOn(userHelper, 'setViewDataFromQueryParam').mockResolvedValue(null);
        const result = await userHelper.handleAccountSetup({ isB2b: false });
        expect(result.status).toBe(false);
    });

    it('should return error on exception', async () => {
        jest.spyOn(userHelper, 'setViewDataFromQueryParam').mockRejectedValue(new Error('Test Error'));
        const result = await userHelper.handleAccountSetup({ isB2b: false });
        expect(result.status).toBe(false);
        expect(result.msg).toBe('Test Error');
    });
  });

  describe('getFreemiumRedirectUrl', () => {
    const signupDetail = { email: '<EMAIL>', first_name: 'John', last_name: 'Doe', phone_no: '123' };
    const assignmentToken = 'token123';
    const authTypeUser = 'Signup';
    const redirectUrl = 'http://default.url';

    it('should create a freemium redirect URL', async () => {
      const rewardData = { respData: { status: 'success', skillUpReferralInfo: { enrolmentRestricted: false } } };
      const authTokenHelperMock = { createSignedToken: jest.fn().mockResolvedValue('jwt-token') };
      helperServiceMock.get.mockResolvedValue(authTokenHelperMock);

      const result = await userHelper.getFreemiumRedirectUrl(
        rewardData,
        signupDetail,
        assignmentToken,
        authTypeUser,
        redirectUrl,
      );

      expect(result).toBe('http://manage.url/jwt-token');
      expect(authTokenHelperMock.createSignedToken).toHaveBeenCalled();
    });

    it('should create a freemium redirect URL when enrollment is restricted', async () => {
      const rewardData = {
        respData: { status: 'success', skillUpReferralInfo: { enrolmentRestricted: true, userRefCode: 'REF123' } },
      };
      const authTokenHelperMock = { createSignedToken: jest.fn().mockResolvedValue('jwt-token') };
      helperServiceMock.get.mockResolvedValue(authTokenHelperMock);

      const result = await userHelper.getFreemiumRedirectUrl(
        rewardData,
        signupDetail,
        assignmentToken,
        authTypeUser,
        redirectUrl,
      );

      expect(result).toBe('http://manage.url/jwt-token');
      expect(authTokenHelperMock.createSignedToken).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            redirectUrl: 'http://freemium-blocked.url/REF123',
          }),
        }),
        expect.any(Object),
      );
    });

    it('should return undefined if an error occurs', async () => {
        const rewardData = { respData: { status: 'success', skillUpReferralInfo: { enrolmentRestricted: false } } };
        helperServiceMock.get.mockRejectedValue(new Error('test error'));
        const result = await userHelper.getFreemiumRedirectUrl(rewardData, signupDetail, assignmentToken, authTypeUser, redirectUrl);
        expect(result).toBeUndefined();
    });
  });

  describe('setViewDataFromQueryParam', () => {
    it('should correctly parse query params and return view data', async () => {
      const queryParam = { phoneNo: '91-**********', userName: 'John Doe', userEmail: '<EMAIL>', referer: 'test' };
      const result = await userHelper.setViewDataFromQueryParam(queryParam);
      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
      expect(result.phoneNo).toBe('**********');
    });

    it('should handle missing phone country code', async () => {
        const queryParam = { phoneNo: '**********', userName: 'John Doe', userEmail: '<EMAIL>', referer: 'test' };
        const result = await userHelper.setViewDataFromQueryParam(queryParam);
        expect(result.varCountryId).toBe('91');
        expect(result.phoneNo).toBe('**********');
    });

    it('should throw BadRequestException on error', async () => {
        const queryParam = { phoneNo: null }; // will cause error
        await expect(userHelper.setViewDataFromQueryParam(queryParam)).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateUserData', () => {
    it('should update user data and return success', async () => {
      const accountSetupInfo = { email: '<EMAIL>', countryCode: 'IN' };
      jest
        .spyOn(userHelper, 'getCloud6SentinelUserByUidOrMail')
        .mockResolvedValue({ status: true, data: { uid: 1 } as any, msg: '' });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({ email: '<EMAIL>' });
      jest.spyOn(userHelper, 'updateCloud6SentinelByUidOrMail').mockResolvedValue(true);
      const result = await userHelper.updateUserData(accountSetupInfo);
      expect(result.status).toBe(true);
      expect(result.data).toHaveProperty('email', '<EMAIL>');
    });

    it('should throw error if user not found in sentinel', async () => {
      const accountSetupInfo = { email: '<EMAIL>', countryCode: 'IN' };
      jest
        .spyOn(userHelper, 'getCloud6SentinelUserByUidOrMail')
        .mockResolvedValue({ status: false, data: null, msg: 'Not Found' });
        await expect(userHelper.updateUserData(accountSetupInfo)).rejects.toThrow(BadRequestException);
    });

    it('should throw error if mongo or sentinel update fails', async () => {
        const accountSetupInfo = { email: '<EMAIL>', countryCode: 'IN' };
        jest
          .spyOn(userHelper, 'getCloud6SentinelUserByUidOrMail')
          .mockResolvedValue({ status: true, data: { uid: 1 } as any, msg: '' });
        userRepositoryMock.findOneAndUpdate.mockResolvedValue(null); // Fails
        jest.spyOn(userHelper, 'updateCloud6SentinelByUidOrMail').mockResolvedValue(false); // Fails
        await expect(userHelper.updateUserData(accountSetupInfo)).rejects.toThrow(BadRequestException);
      });
  });

  describe('updateUserLoginTime', () => {
    it('should update login time for user in mongo and mysql', async () => {
      const user = { uid: '1', email: '<EMAIL>', timezone: 'UTC' } as any;
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      jest.spyOn(userHelper, 'updateCloud6SentinelByUidOrMail').mockResolvedValue(true);
      helperServiceMock.getHelper.mockResolvedValue({ sendDataToLrs: jest.fn() });
      await userHelper.updateUserLoginTime(user);
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalled();
      expect(userHelper.updateCloud6SentinelByUidOrMail).toHaveBeenCalled();
    });

    it('should handle drupal sync if enabled', async () => {
        const user = { uid: '1', email: '<EMAIL>', timezone: 'UTC' } as any;
        configServiceMock.get.mockImplementation((key) => key === 'enableDrupalSync' ? true : key);
        userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
        jest.spyOn(userHelper, 'updateCloud6SentinelByUidOrMail').mockResolvedValue(true);
        jest.spyOn(userHelper, 'syncUserDataWithMySQLDrupal').mockResolvedValue(true);
        helperServiceMock.getHelper.mockResolvedValue({ sendDataToLrs: jest.fn() });
        await userHelper.updateUserLoginTime(user);
        expect(userHelper.syncUserDataWithMySQLDrupal).toHaveBeenCalled();
    });
  });

  describe('updateUserStatus', () => {
    it('should update user status in mongo and mysql', async () => {
      const payload = { userId: 1, email: '<EMAIL>', status: 1 };
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      const result = await userHelper.updateUserStatus(payload);
      expect(result).toBe(true);
    });

    it('should throw InternalServerErrorException if update fails', async () => {
      const payload = { userId: 1, email: '<EMAIL>', status: 1 };
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 0 });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      await expect(userHelper.updateUserStatus(payload)).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle drupal sync if enabled', async () => {
        const payload = { userId: 1, email: '<EMAIL>', status: 1 };
        configServiceMock.get.mockImplementation((key) => key === 'enableDrupalSync' ? true : key);
        (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
        userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
        jest.spyOn(userHelper, 'syncUserDataWithMySQLDrupal').mockResolvedValue(true);
        await userHelper.updateUserStatus(payload);
        expect(userHelper.syncUserDataWithMySQLDrupal).toHaveBeenCalled();
    });

    it('should throw error on db exception', async () => {
        const payload = { userId: 1, email: '<EMAIL>', status: 1 };
        (sentinelUserRepoMock.update as jest.Mock).mockRejectedValue(new Error('DB Error'));
        await expect(userHelper.updateUserStatus(payload)).rejects.toThrow('DB Error');
    });
  });

  describe('deactivateUserStatus', () => {
    it('should deactivate an active user', async () => {
      const userEmail = '<EMAIL>';
      const userMock = { email: userEmail, status: 1, uid: 123 };
      const dto: deactivateUserByEmailDto = { user_email: userEmail, client_id: 'test' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      jest.spyOn(userHelper, 'updateUserStatus').mockResolvedValue(true);

      const result = await userHelper.deactivateUserStatus(dto);

      expect(result).toBe(true);
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(userEmail);
      expect(userHelper.updateUserStatus).toHaveBeenCalledWith({ userId: 123, email: userEmail, status: 0 });
    });

    it('should activate an inactive user if enable flag is set', async () => {
      const userEmail = '<EMAIL>';
      const userMock = { email: userEmail, status: 0, uid: 123 };
      const dto: deactivateUserByEmailDto = { user_email: userEmail, enable: '1', client_id: 'test' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      jest.spyOn(userHelper, 'updateUserStatus').mockResolvedValue(true);

      const result = await userHelper.deactivateUserStatus(dto);

      expect(result).toBe(true);
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(userEmail);
      expect(userHelper.updateUserStatus).toHaveBeenCalledWith({ userId: 123, email: userEmail, status: 1 });
    });

    it('should return BadRequestException if user is already blocked', async () => {
      const userEmail = '<EMAIL>';
      const userMock = { email: userEmail, status: 0 };
      const dto: deactivateUserByEmailDto = { user_email: userEmail, client_id: 'test' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);

      await expect(userHelper.deactivateUserStatus(dto)).rejects.toThrow(BadRequestException);
    });

    it('should return BadRequestException if user is not found', async () => {
      const userEmail = '<EMAIL>';
      const dto: deactivateUserByEmailDto = { user_email: userEmail, client_id: 'test' };
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      await expect(userHelper.deactivateUserStatus(dto)).rejects.toThrow(BadRequestException);
    });

    it('should throw an error if updateUserStatus fails', async () => {
      const userEmail = '<EMAIL>';
      const userMock = { email: userEmail, status: 1, uid: 123 };
      const dto: deactivateUserByEmailDto = { user_email: userEmail, client_id: 'test' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(userMock);
      jest.spyOn(userHelper, 'updateUserStatus').mockRejectedValue(new Error('Update failed'));

      await expect(userHelper.deactivateUserStatus(dto)).rejects.toThrow('Update failed');
    });
  });

  describe('getUserInfo', () => {
    it('should return an empty array for empty users array', () => {
      const result = userHelper.getUserInfo([]);
      expect(result).toEqual([]);
    });

    it('should correctly format user data', () => {
      const now = new Date();
      const users: any[] = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          status: 1,
          language: 'en',
          profile_pic: { filename: 'pic.jpg' },
          signature: 1,
          display_name: 'JohnD',
          phone_no: '12345',
          location: '',
          country_code: 'US',
          roles: [{ roleName: 'Admin', rid: 1 }],
          createdAt: now,
          access: now,
          login: now,
          uid: 1,
          user_type: '' as UserType,
          linkedin_status: 1,
          user_groups: [101, 102],
          timezone: 'UTC',
        },
      ];
      const result = userHelper.getUserInfo(users as User[]);
      expect(result.length).toBe(1);
      expect(result[0].name).toBe('John Doe');
      expect(result[0].mail).toBe('<EMAIL>');
      expect(result[0].Active).toBe('Yes');
      expect(result[0].rid).toBe('Admin');
      expect(result[0].uid).toBe(1);
      expect(result[0].gid).toBe('101,102');
    });

    it('should correctly format user data with missing optional fields', () => {
      const now = new Date();
      const users: any[] = [
        {
          name: 'Jane Doe',
          email: '<EMAIL>',
          status: 0, // Inactive
          language: null,
          profile_pic: null,
          signature: 0,
          display_name: 'JaneD',
          phone_no: null,
          location: null,
          country_code: null,
          roles: [], // No roles
          createdAt: now,
          access: now,
          login: now,
          uid: 2,
          user_type: '' as UserType,
          linkedin_status: 0,
          user_groups: [],
          timezone: null,
        },
      ];
      const result = userHelper.getUserInfo(users as User[]);
      expect(result.length).toBe(1);
      expect(result[0].name).toBe('Jane Doe');
      expect(result[0].mail).toBe('<EMAIL>');
      expect(result[0].Active).toBe('No');
      expect(result[0].rid).toBe('');
      expect(result[0].Picture).toBe('');
      expect(result[0].gid).toBe('');
    });
  });

  describe('getUserEmail', () => {
    it('should return formatted user email details', () => {
      const now = new Date();
      const user: any = {
        name: 'John Doe',
        email: '<EMAIL>',
        status: 1,
        createdAt: now,
        access: now,
        login: now,
        profile_pic: {},
        signature: 1,
        uid: 123,
        roles: [
          { roleName: 'Admin', rid: 1 },
          { roleName: 'User', rid: 2 },
        ],
      };

      const result = userHelper.getUserEmail(user);

      expect(result[0]['e-mail']).toBe('<EMAIL>');
      expect(result[0]['created date']).toBe(moment(now).format('dddd, MMMM D, YYYY - HH:mm'));
      expect(result[0].roles).toBe('Admin,User');
    });

    it('should return an empty array for null user', () => {
      const result = userHelper.getUserEmail(null);
      expect(result).toEqual([]);
    });
  });

  describe('sendGA4Events', () => {
    it('should send GA4 event for signup', async () => {
      const ga4HelperMock = { sendEvents: jest.fn() };
      helperServiceMock.get.mockResolvedValue(ga4HelperMock);

      const data = { authType: 'Signup', email: '<EMAIL>' };
      const cookieBody = { sl_su_utmz: 'utm_data', _ga: 'GA1.2.123.456' };
      const frsRedirectUrl = 'http://redirect.url';
      const recommended_courses = 'course1,course2';
      const userType = 'free';

      await userHelper.sendGA4Events(data, cookieBody, frsRedirectUrl, recommended_courses, userType);

      expect(ga4HelperMock.sendEvents).toHaveBeenCalled();
      const eventDetails = ga4HelperMock.sendEvents.mock.calls[0][1];
      expect(eventDetails.eventsData.name).toBe('complete_signup');
      expect(eventDetails.eventsData.param.user_id).toBe('hashed-email');
    });

    it('should send GA4 event for login', async () => {
        const ga4HelperMock = { sendEvents: jest.fn() };
        helperServiceMock.get.mockResolvedValue(ga4HelperMock);
  
        const data = { authType: 'Login', email: '<EMAIL>' };
        const cookieBody = { sl_su_utmz: 'utm_data', _ga: 'GA1.2.123.456' };
        const frsRedirectUrl = 'http://redirect.url';
        const recommended_courses = 'course1,course2';
        const userType = 'paid';
  
        await userHelper.sendGA4Events(data, cookieBody, frsRedirectUrl, recommended_courses, userType);
  
        expect(ga4HelperMock.sendEvents).toHaveBeenCalled();
        const eventDetails = ga4HelperMock.sendEvents.mock.calls[0][1];
        expect(eventDetails.eventsData.name).toBe('complete_login');
        expect(eventDetails.eventsData.param.sl_freemium_user).toBe('false');
      });

    it('should handle GA4 helper failure gracefully', async () => {
      helperServiceMock.get.mockRejectedValue(new Error('GA4 helper not found'));
      const data = { authType: 'Signup', email: '<EMAIL>' };
      const cookieBody = {};
      // Expect it not to throw
      await expect(userHelper.sendGA4Events(data, cookieBody, '', '', 'free')).resolves.toBeUndefined();
    });
  });

  describe('getSkillUpInfoInviteLink', () => {
    it('should get skillup info and invite link', async () => {
      const paperclipServiceMock = {
        getSkillupReferralRewardInfo: jest
          .fn()
          .mockResolvedValue({ respData: { skillUpReferralInfo: { userRefCode: 'REF123' } } }),
      };
      const communityServiceMock = {
        getSkillupReferralInviteLink: jest.fn().mockResolvedValue({ url: 'http://invite.link' }),
      };
      helperServiceMock.get.mockImplementation(async (service) => {
        if (service === PaperclipService) return paperclipServiceMock;
        if (service === UserMgmtCommunityService) return communityServiceMock;
        return {};
      });

      const userDetails = { uid: 1, user_type: 'free' as UserType };
      const data = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '123',
        authType: 'Signup',
        loginMethodType: 'email',
      };
      const redirectUrl = 'http://redirect.url';
      const calendarUrl = 'http://calendar.url';

      const result = await userHelper.getSkillUpInfoInviteLink(userDetails, data, redirectUrl, calendarUrl);

      expect(result.refCode).toBe('REF123');
      expect(result.skillup_ref_link).toBe('http://invite.link');
      expect(result.skillupInviteData.hashedEmail).toBe('hashed-email');
    });

    it('should throw error on paperclip service failure', async () => {
      const paperclipServiceMock = {
        getSkillupReferralRewardInfo: jest.fn().mockRejectedValue(new Error('Service Error')),
      };
      helperServiceMock.get.mockImplementation(async (service) => {
        if (service === PaperclipService) return paperclipServiceMock;
        return {};
      });

      const userDetails = { uid: 1, user_type: 'free' as UserType };
      const data = { email: '<EMAIL>' };
      await expect(userHelper.getSkillUpInfoInviteLink(userDetails, data, '', '')).rejects.toThrow('Service Error');
    });
  });

  describe('handleSkillupCookieActions', () => {
    beforeEach(() => {
        userRepositoryMock.getUserByEmail.mockResolvedValue({ uid: 1, status: 1, user_type: 'free' });
        jest.spyOn(userHelper, 'getSkillUpInfoInviteLink').mockResolvedValue({
            refCode: 'REF123',
            skillup_ref_link: 'http://invite.link',
            skillupInviteData: { hashedEmail: 'hashed-email' } as any
        });
    });

    it('should handle skillUpOneTapRedirectCookie', async () => {
      const cookieBody = { skillUpOneTapRedirectCookie: '/redirect-path' };
      const data = { email: '<EMAIL>', redirect_url: 'http://default.url' };
      const result = await userHelper.handleSkillupCookieActions(cookieBody, data);
      expect(result.redirectUrl).toBe('http://sheldon.com/redirect-path');
    });

    it('should handle skillUpQuestionnaireRedirectCookie on signup', async () => {
        const cookieBody = { skillUpQuestionnaireRedirectCookie: '1' };
        const data = { email: '<EMAIL>', authType: 'Signup' };
        const result = await userHelper.handleSkillupCookieActions(cookieBody, data);
        expect(result.redirectUrl).toBe('http://questionnaire.url');
    });

    it('should handle skillUpQuizRedirectCookie on signup', async () => {
        const cookieBody = { skillUpQuizRedirectCookie: '1' };
        const data = { email: '<EMAIL>', authType: 'Signup' };
        const result = await userHelper.handleSkillupCookieActions(cookieBody, data);
        expect(result.redirectUrl).toBe('http://quiz.url');
    });

    it('should handle skillUpQuestionnaireDataSubmitted on signup', async () => {
        const cookieBody = { skillUpQuestionnaireDataSubmitted: '1' };
        const data = { email: '<EMAIL>', authType: 'Signup' };
        const result = await userHelper.handleSkillupCookieActions(cookieBody, data);
        expect(result.redirectUrl).toBe('http://sheldon.com/skillup-free-online-courses');
    });

    it('should handle skillUpOriginRedirectCookie on login', async () => {
        const cookieBody = { skillUpOriginRedirectCookie: 'http://origin.url' };
        const data = { email: '<EMAIL>', authType: 'Login' };
        const result = await userHelper.handleSkillupCookieActions(cookieBody, data);
        expect(result.redirectUrl).toBe('http://origin.url');
    });

    it('should handle frsOneTapRedirectCookie and emit signup event', async () => {
        const cookieBody = { frsOneTapRedirectCookie: 'http://frs.redirect' };
        const data = { email: '<EMAIL>', authType: 'Signup' };
        await userHelper.handleSkillupCookieActions(cookieBody, data);
        expect(eventEmitterMock.emit).toHaveBeenCalledWith('user.signup', expect.any(Object));
    });

    it('should handle frsOneTapRedirectCookie and emit login event', async () => {
        const cookieBody = { frsOneTapRedirectCookie: 'http://frs.redirect' };
        const data = { email: '<EMAIL>', authType: 'Login' };
        await userHelper.handleSkillupCookieActions(cookieBody, data);
        expect(eventEmitterMock.emit).toHaveBeenCalledWith('user.login', expect.any(Object));
    });

    it('should throw error on failure', async () => {
        userRepositoryMock.getUserByEmail.mockRejectedValue(new Error('DB Error'));
        const cookieBody = { skillUpOneTapRedirectCookie: '/redirect-path' };
        const data = { email: '<EMAIL>', redirect_url: 'http://default.url' };
        await expect(userHelper.handleSkillupCookieActions(cookieBody, data)).rejects.toThrow('DB Error');
    });
  });

  describe('handleB2bB2cRedirect', () => {
    it('should return the original redirectUrl when gid is empty', async () => {
      const redirectUrl = 'originalUrl';
      const result = await userHelper.handleB2bB2cRedirect('', redirectUrl);
      expect(result).toBe(redirectUrl);
    });

    it('should return lmsSiteUrl when user is B2B or B2C learner', async () => {
      const redirectUrl = 'originalUrl';
      jest.spyOn(userHelper, 'ifNotB2COrB2BLearner').mockResolvedValue(true);
      const result = await userHelper.handleB2bB2cRedirect('some-gid', redirectUrl);
      expect(result).toBe('http://lms.site.url');
    });

    it('should return original url when user is not B2B or B2C learner', async () => {
        const redirectUrl = 'originalUrl';
        jest.spyOn(userHelper, 'ifNotB2COrB2BLearner').mockResolvedValue(false);
        const result = await userHelper.handleB2bB2cRedirect('some-gid', redirectUrl);
        expect(result).toBe(redirectUrl);
      });
  });

  describe('getUserEnterpriseAndCourses', () => {
    it('should return enterprise list and courses for a user', async () => {
      const apiResult = {
        enterpriseList: [{ id: 1, name: 'Enterprise' }],
        learnerCourses: [{ id: 1, name: 'Course' }],
      };
      cloud6ServiceMock.getUserEnterpriseList.mockResolvedValue(apiResult);
      const result = await userHelper.getUserEnterpriseAndCourses({ uid: 'test-uid', email: '<EMAIL>' });
      expect(result).toEqual({
        userEnterpriseList: apiResult.enterpriseList,
        learnerCoursesRecords: apiResult.learnerCourses,
      });
      expect(cloud6ServiceMock.getUserEnterpriseList).toHaveBeenCalledWith({
        uid: 'test-uid',
        email: '<EMAIL>',
      });
    });
  });

  describe('isUserMemberOfGroup', () => {
    it('should return true if user is a member of any group', async () => {
      enterpriseServiceMock.getUserEnterpriseList.mockResolvedValue({ enterpriseList: [{ id: 1 }] });
      const result = await userHelper.isUserMemberOfGroup('some-uid', 'some-gid');
      expect(result).toBe(true);
      expect(enterpriseServiceMock.getUserEnterpriseList).toHaveBeenCalledWith({ uid: 'some-uid' });
    });

    it('should return false if user is not a member of any group', async () => {
      enterpriseServiceMock.getUserEnterpriseList.mockResolvedValue({ enterpriseList: [] });
      const result = await userHelper.isUserMemberOfGroup('some-uid', 'some-gid');
      expect(result).toBe(false);
    });

    it('should return false on service error', async () => {
      enterpriseServiceMock.getUserEnterpriseList.mockRejectedValue(new Error('Service Error'));
      const result = await userHelper.isUserMemberOfGroup('some-uid', 'some-gid');
      expect(result).toBe(false);
    });
  });

  describe('changePasswordAttemptLimit', () => {
    it('should increment cache count if limit is not exceeded', async () => {
      cachingServiceMock.get.mockResolvedValue(2);
      const result = await userHelper.changePasswordAttemptLimit('<EMAIL>');
      expect(cachingServiceMock.get).toHaveBeenCalledWith('test@test.com_reset');
      expect(cachingServiceMock.set).toHaveBeenCalledWith('test@test.com_reset', 3, 600);
      expect(result.status).toBe('failed');
    });

    it('should return limit exceeded message if limit is reached', async () => {
      cachingServiceMock.get.mockResolvedValue(5);
      const result = await userHelper.changePasswordAttemptLimit('<EMAIL>');
      expect(cachingServiceMock.set).toHaveBeenCalledWith('test@test.com_reset_timeout', expect.any(Number), 900);
      expect(result.status).toBe('limit_exceeded');
      expect(result.msg).toContain('15 minutes');
    });

    it('should handle first attempt', async () => {
      cachingServiceMock.get.mockResolvedValue(null); // First attempt
      const result = await userHelper.changePasswordAttemptLimit('<EMAIL>');
      expect(cachingServiceMock.set).toHaveBeenCalledWith('test@test.com_reset', 1, 600);
      expect(result.status).toBe('failed');
    });

    it('should return error on cache service failure', async () => {
        cachingServiceMock.get.mockRejectedValue(new Error('Cache Error'));
        const result = await userHelper.changePasswordAttemptLimit('<EMAIL>');
        expect(result).toBeInstanceOf(Error);
    });
  });

  describe('syncReferEarnUrl', () => {
    it('should return error if name or email is missing', async () => {
      const res = {};
      const postData = { userKey: 'key' };
      const userId = 1;
      const result = await userHelper.syncReferEarnUrl(res, postData, userId);
      expect(result).toBe('name or email empty');
    });

    it('should return false when kafka is not responding', async () => {
      const cookieHelperMock = { setCookie: jest.fn() };
      helperServiceMock.getHelper.mockResolvedValue(cookieHelperMock);
      const res = {};
      const postData = { name: 'test', email: '<EMAIL>', userKey: 'key' };
      const userId = 1;
      const result = await userHelper.syncReferEarnUrl(res, postData, userId);
      expect(result).toBe(false);
    });

    it('should return error message on exception', async () => {
        const res = {};
        const postData = { name: 'test', email: '<EMAIL>', userKey: 'key' };
        const userId = 1;
        helperServiceMock.getHelper.mockRejectedValue(new Error('test error'));
        const result = await userHelper.syncReferEarnUrl(res, postData, userId);
        expect(result).toBe('Error processing request');
    });
  });

  describe('getAllRolesofUser', () => {
    it('should return all roles of a user', async () => {
      const roles = [{ uid: 1, rid: 1 }];
      (sentinelUserRoleRepoMock.find as jest.Mock).mockResolvedValue(roles);
      const result = await userHelper.getAllRolesofUser('1');
      expect(result).toEqual(roles);
    });

    it('should return empty array on db error', async () => {
        (sentinelUserRoleRepoMock.find as jest.Mock).mockRejectedValue(new Error('DB Error'));
        const result = await userHelper.getAllRolesofUser('1');
        expect(result).toEqual([]);
    });
  });

  describe('createEngagexRole', () => {
    it('should create a new role for a user', async () => {
      (sentinelUserRoleRepoMock.save as jest.Mock).mockResolvedValue({});
      const result = await userHelper.createEngagexRole(1, 2);
      expect(result).toBe(true);
    });

    it('should return error on db error', async () => {
      (sentinelUserRoleRepoMock.save as jest.Mock).mockRejectedValue(new Error('DB Error'));
      const result = await userHelper.createEngagexRole(1, 2);
      expect(result).toBeInstanceOf(Error);
    });
  });

  describe('updateExistingRole', () => {
    it('should update an existing role', async () => {
      (sentinelUserRoleRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await userHelper.updateExistingRole(1, 2, 3);
      expect(result).toBe(true);
    });

    it('should return false if update fails', async () => {
      (sentinelUserRoleRepoMock.update as jest.Mock).mockResolvedValue(null);
      const result = await userHelper.updateExistingRole(1, 2, 3);
      expect(result).toBe(false);
    });

    it('should return error on db error', async () => {
        (sentinelUserRoleRepoMock.update as jest.Mock).mockRejectedValue(new Error('DB Error'));
        const result = await userHelper.updateExistingRole(1, 2, 3);
        expect(result).toBeInstanceOf(Error);
    });
  });

  describe('createGdprRequest', () => {
    it('should create a gdpr request', async () => {
      (gdprRequestRepoMock.save as jest.Mock).mockResolvedValue({ id: 1 });
      const result = await userHelper.createGdprRequest({});
      expect(result).toBe(1);
    });

    it('should return false on db failure', async () => {
      (gdprRequestRepoMock.save as jest.Mock).mockRejectedValue(new Error('DB Error'));
      const result = await userHelper.createGdprRequest({});
      expect(result).toBe(false);
    });
  });

  describe('authRehash', () => {
    it('should create a hmac hash of the input', async () => {
      const input = { data: 'test' };
      const result = await userHelper.authRehash(input);
      expect(result).toBe('hmac-token');
      expect(cryptoHelperMock.createHmac).toHaveBeenCalledWith(
        JSON.stringify(input),
        'salt',
        'sha256',
        'hex',
      );
    });

    it('should return error on failure', async () => {
        const input = { data: 'test' };
        cryptoHelperMock.createHmac.mockRejectedValue(new Error('Crypto Error'));
        const result = await userHelper.authRehash(input);
        expect(result).toBeInstanceOf(Error);
    });
  });

  describe('updateUserOptions', () => {
    it('should update user options', async () => {
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await userHelper.updateUserOptions(1, 'password', 0);
      expect(result).toBe(true);
    });

    it('should return false on mysql update failure', async () => {
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 0 });
      const result = await userHelper.updateUserOptions(1, 'password', 0);
      expect(result).toBe(false);
    });

    it('should handle drupal sync if enabled', async () => {
        configServiceMock.get.mockImplementation((key) => key === 'enableDrupalSync' ? true : key);
        userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
        (sentinelUserRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
        jest.spyOn(userHelper, 'syncUserDataWithMySQLDrupal').mockResolvedValue(true);
        const result = await userHelper.updateUserOptions(1, 'password', 0);
        expect(result).toBe(true);
        expect(userHelper.syncUserDataWithMySQLDrupal).toHaveBeenCalled();
    });

    it('should throw error on exception', async () => {
        userRepositoryMock.findOneAndUpdate.mockRejectedValue(new Error('DB Error'));
        await expect(userHelper.updateUserOptions(1, 'password', 0)).rejects.toThrow('DB Error');
    });
  });

  // More describe blocks will be added here in subsequent steps
  describe('setupWelcomeEmailToManager', () => {
      let email : any = '<EMAIL>';
    it('should send welcome email to manager successfully', async () => {
      cloud6ServiceMock.sendWelcomeEmailToManager.mockResolvedValue({ status: true });
      const result = await userHelper.setupWelcomeEmailToManager(email, 123);
      expect(result).toBe(true);
      expect(cloud6ServiceMock.sendWelcomeEmailToManager).toHaveBeenCalledWith('<EMAIL>', 123);
    });

    it('should return false if email sending fails', async () => {
      cloud6ServiceMock.sendWelcomeEmailToManager.mockResolvedValue({ status: false });
      const result = await userHelper.setupWelcomeEmailToManager(email, 123);
      expect(result).toBe(false);
    });

    it('should handle service errors gracefully', async () => {
      cloud6ServiceMock.sendWelcomeEmailToManager.mockRejectedValue(new Error('Service Error'));
      const result = await userHelper.setupWelcomeEmailToManager(email, 123);
      expect(result).toBe(false);
    });
  });

  describe('removeRoles', () => {
    it('should remove roles successfully', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockResolvedValue({ affected: 1 });
      const result = await userHelper.removeRoles(1, 2 ,[2, 3]);
      expect(result).toBe(true);
      expect(sentinelUserRoleRepoMock.delete).toHaveBeenCalledWith({
        uid: 1,
        rid: In([2, 3])
      });
    });

    it('should return false if no roles were affected', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockResolvedValue({ affected: 0 });
      const result = await userHelper.removeRoles(1, 2 ,[2, 3]);
      expect(result).toBe(false);
    });

    it('should handle database errors', async () => {
      (sentinelUserRoleRepoMock.delete as jest.Mock).mockRejectedValue(new Error('DB Error'));
      const result = await userHelper.removeRoles(1, 2 ,[2, 3]);
      expect(result).toBe(false);
    });
  });

  describe('createCloud6SentinelRole', () => {
    it('should create a new sentinel role', async () => {
      const roleData : any = { rid: 1, roleName: 'TestRole' };
      (sentinelRoleRepoMock.create as jest.Mock).mockReturnValue(roleData);
      (sentinelRoleRepoMock.save as jest.Mock).mockResolvedValue(roleData);
      
      const result = await userHelper.createCloud6SentinelRole(roleData);
      expect(result).toBe(true);
      expect(sentinelRoleRepoMock.create).toHaveBeenCalledWith(roleData);
      expect(sentinelRoleRepoMock.save).toHaveBeenCalled();
    });

    it('should return false if save fails', async () => {
      (sentinelRoleRepoMock.create as jest.Mock).mockReturnValue({});
      (sentinelRoleRepoMock.save as jest.Mock).mockRejectedValue(new Error('Save failed'));
      let roleData : any = { rid: 1, roleName: 'TestRole' };
      const result = await userHelper.createCloud6SentinelRole(roleData);
      expect(result).toBe(false);
    });
  });

  describe('updateCloud6SentinelRole', () => {
    it('should update an existing sentinel role', async () => {
      const roleData :any = { rid: 1, roleName: 'UpdatedRole' };
      (sentinelRoleRepoMock.update as jest.Mock).mockResolvedValue({ affected: 1 });
      
      const result = await userHelper.updateCloud6SentinelRole(roleData);
      expect(result).toBe(true);
      expect(sentinelRoleRepoMock.update).toHaveBeenCalledWith({ rid: roleData.rid }, roleData);
    });

    it('should return false if no role was updated', async () => {
      const roleData : any = { rid: 1, roleName: 'UpdatedRole' };
      (sentinelRoleRepoMock.update as jest.Mock).mockResolvedValue({ affected: 0 });
      
      const result = await userHelper.updateCloud6SentinelRole(roleData);
      expect(result).toBe(false);
    });

    it('should handle database errors', async () => {
      const roleData :any = { rid: 1, roleName: 'UpdatedRole' };
      (sentinelRoleRepoMock.update as jest.Mock).mockRejectedValue(new Error('DB Error'));
      
      const result = await userHelper.updateCloud6SentinelRole(roleData);
      expect(result).toBe(false);
    });
  });

  //userSynchDataTransform
  // 
});
