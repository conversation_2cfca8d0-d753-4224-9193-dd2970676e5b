import { Test, TestingModule } from '@nestjs/testing';
import { User<PERSON>elper } from './user.helper';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { User } from '../../db/mongo/schema/user/user.schema';
import { BadRequestException } from '@nestjs/common';
import { UserRepository } from '../repositories/user/user.repository';

describe('UserHelper', () => {
  let userHelper: UserHelper;

  const helperServiceMock = {
    // Implement methods or mock data as needed
    getHelper: jest.fn(),
    get: jest.fn(),
  };

  const configServiceMock = {
    // Implement methods or mock data as needed
    get: jest.fn(),
  };

  const jwtServiceMock = {
    // Implement methods or mock data as needed
    decode: jest.fn(),
  };

  const cryptoHelperMock = {
    // Implement methods or mock data as needed
  };
  // Mock the Cloud6Service
  const cloud6ServiceMock = {
    getGroupDetailsByGid: jest.fn(),
    getLmsEnterpriseSettings: jest.fn(),
    getGroupByDomain: jest.fn(),
    getUserEnterpriseList: jest.fn(),
    synchUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserHelper,
        {
          provide: HelperService,
          useValue: helperServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: JwtService,
          useValue: jwtServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: cryptoHelperMock,
        },
        {
          provide: Cloud6Service,
          useValue: cloud6ServiceMock,
        },
      ],
    }).compile();

    userHelper = module.get<UserHelper>(UserHelper);
  });

  it('should be defined', () => {
    expect(userHelper).toBeDefined();
  });

  describe('getUserPassword', () => {
    it('should generate a password with the specified length', () => {
      const passwordLength = 12;
      const generatedPassword = userHelper.getUserPassword(passwordLength);
      expect(generatedPassword.length).toEqual(passwordLength);
    });

    it('should include characters from all required character sets', () => {
      const passwordLength = 12;
      const generatedPassword = userHelper.getUserPassword(passwordLength);
      expect(generatedPassword).toMatch(/[a-z]/); // Lowercase
      expect(generatedPassword).toMatch(/[A-Z]/); // Uppercase
      expect(generatedPassword).toMatch(/[0-9]/); // Numeric
      expect(generatedPassword).toMatch(/[!@#$%^&*()_\-+=<>?/[\]{}]/); // Special
    });

    it('should shuffle characters to make the password random', () => {
      const passwordLength = 12;
      const originalPassword = userHelper.getUserPassword(passwordLength);
      const shuffledPassword = userHelper.getUserPassword(passwordLength);
      expect(shuffledPassword).not.toEqual(originalPassword);
    });

    it('should handle a length less than 1', () => {
      const passwordLength = 0;
      const generatedPassword = userHelper.getUserPassword(passwordLength);
      expect(generatedPassword.length).toEqual(4);
    });

    it('should handle a length less than the minimum required characters', () => {
      const passwordLength = 4;
      const generatedPassword = userHelper.getUserPassword(passwordLength);
      expect(generatedPassword.length).toEqual(passwordLength);
    });
  });

  describe('ifNotB2COrB2BLearner', () => {
    it('should return true for a valid B2B or B2C userGid', async () => {
      // Mock the helper service's getHelper method
      helperServiceMock.getHelper.mockResolvedValue({
        empty: jest.fn().mockReturnValue(false),
      });

      // Mock the config service's get method
      const validB2bOrB2cGroupId = 123; // Replace with a valid B2B or B2C userGid
      configServiceMock.get.mockReturnValue([validB2bOrB2cGroupId]);

      const userGid = validB2bOrB2cGroupId;
      const result = await userHelper.ifNotB2COrB2BLearner(userGid);

      expect(result).toBe(true);
    });

    it('should return false for an invalid B2B or B2C userGid', async () => {
      // Mock the helper service's getHelper method
      helperServiceMock.getHelper.mockResolvedValue({
        empty: jest.fn().mockReturnValue(true),
      });

      // Mock the config service's get method
      const validB2bOrB2cGroupId = 123; // Replace with a valid B2B or B2C userGid
      configServiceMock.get.mockReturnValue([validB2bOrB2cGroupId]);

      const userGid = 456; // Replace with an invalid B2B or B2C userGid
      const result = await userHelper.ifNotB2COrB2BLearner(userGid);

      expect(result).toBe(false);
    });
  });

  describe('lmsSettingsCacheData', () => {
    it('should return null for empty enterpriseSettings', async () => {
      // Arrange
      const enterpriseSettings = [];

      // Act
      const result = await userHelper.lmsSettingsCacheData(enterpriseSettings);

      // Assert
      expect(result).toBeNull();
    });

    it('should map preference names to settings and include custom_value for Custom or lmsPreferenceName', async () => {
      // Arrange
      configServiceMock.get.mockReturnValue('your-lms-preference-name');
      const enterpriseSettings = [
        { preference_name: 'Setting1', value: 'Value1', custom_value: 'CustomValue1' },
        { preference_name: 'Setting2', value: 'Custom', custom_value: 'CustomValue2' },
        { preference_name: 'Your LMS Preference Name', value: 'SomeValue', custom_value: 'CustomValue3' },
      ];

      // Act
      const result = await userHelper.lmsSettingsCacheData(enterpriseSettings);

      // Assert
      expect(result).toEqual({
        setting1: { value: 'Value1' },
        setting2: { value: 'Custom', custom_value: 'CustomValue2' },
        your_lms_preference_name: { value: 'SomeValue' },
      });
    });
  });

  describe('isUserMemberOfGroup', () => {
    it('should return true when userEnterpriseList is not null and has at least one enterprise', async () => {
      const cloud6ServiceMock = {
        getUserEnterpriseList: jest.fn().mockResolvedValue({
          enterpriseList: [
            {
              /* some enterprise data */
            },
          ],
        }),
      };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === Cloud6Service) {
          return Promise.resolve(cloud6ServiceMock);
        }
      });

      const result = await userHelper.isUserMemberOfGroup('validUid', 'validGId');
      expect(result).toBe(true);
      expect(cloud6ServiceMock.getUserEnterpriseList).toHaveBeenCalledWith({ uid: 'validUid' });
    });

    it('should return false when userEnterpriseList is not null but has zero enterprises', async () => {
      const cloud6ServiceMock = { getUserEnterpriseList: jest.fn().mockResolvedValue({ enterpriseList: [] }) };

      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === Cloud6Service) {
          return Promise.resolve(cloud6ServiceMock);
        }
      });
      const result = await userHelper.isUserMemberOfGroup('validUid', 'validGId');
      expect(result).toBe(false);
      expect(cloud6ServiceMock.getUserEnterpriseList).toHaveBeenCalledWith({ uid: 'validUid' });
    });

    describe('getUserEnterpriseAndCourses', () => {
      it('should return userEnterpriseList and learnerCoursesRecords when both are present in the API result', async () => {
        const userInfo = { uid: 'validUid', email: '<EMAIL>' };
        const enterpriseListApiResult = {
          enterpriseList: [
            {
              /* some enterprise data */
            },
          ],
          learnerCourses: [
            {
              /* some learner courses data */
            },
          ],
        };

        const cloud6ServiceMock = { getUserEnterpriseList: jest.fn().mockResolvedValue(enterpriseListApiResult) };

        jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
          if (helperName === Cloud6Service) {
            return Promise.resolve(cloud6ServiceMock);
          }
        });
        const result = await userHelper.getUserEnterpriseAndCourses(userInfo);
        expect(result).toEqual({
          userEnterpriseList: enterpriseListApiResult.enterpriseList,
          learnerCoursesRecords: enterpriseListApiResult.learnerCourses,
        });
        expect(cloud6ServiceMock.getUserEnterpriseList).toHaveBeenCalledWith({
          uid: userInfo.uid,
          email: userInfo.email,
        });
      });

      it('should return empty arrays for userEnterpriseList and learnerCoursesRecords when API result is null', async () => {
        const userInfo = { uid: 'validUid', email: '<EMAIL>' };

        const cloud6ServiceMock = {
          getUserEnterpriseList: jest.fn().mockResolvedValue({ userEnterpriseList: [], learnerCoursesRecords: [] }),
        };

        jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
          if (helperName === Cloud6Service) {
            return Promise.resolve(cloud6ServiceMock);
          }
        });
        const result = await userHelper.getUserEnterpriseAndCourses(userInfo);
        expect(cloud6ServiceMock.getUserEnterpriseList).toHaveBeenCalledWith({
          uid: userInfo.uid,
          email: userInfo.email,
        });
      });
    });
  });

  describe('getUserInfo', () => {
    it('should return an empty array for empty users array', () => {
      const result = userHelper.getUserInfo([]);
      expect(result).toEqual([]);
    });
  });
  describe('deactive user status', () => {
    const userRepositoryMock = {
      getUserByEmail: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };
    it('should deactivate user status when user is active', async () => {
      const userEmail = '<EMAIL>';
      const userMock = { email: userEmail, status: true };
      const updatedUserMock = { ...userMock, status: 0 };

      // Mock the getUserByEmail method of UserRepository to resolve with the mocked user
      userRepositoryMock.getUserByEmail = jest.fn().mockResolvedValue(userMock);

      // Mock the findOneAndUpdate method of UserRepository to resolve with the mocked updated user
      userRepositoryMock.findOneAndUpdate = jest.fn().mockResolvedValue(updatedUserMock);
      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
      });
      const result = await userHelper.deactivateUserStatus(userEmail);

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(userEmail);
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith({ email: userEmail }, { status: 0 });
      expect(result).toEqual(updatedUserMock);
    });

    it('should throw BadRequestException when user is already blocked', async () => {
      const userEmail = '<EMAIL>';
      const blockedUserMock = { email: userEmail, status: 0 };

      // Mock the getUserByEmail method of UserRepository to resolve with the mocked blocked user
      userRepositoryMock.getUserByEmail = jest.fn().mockResolvedValue(blockedUserMock);
      jest.spyOn(helperServiceMock, 'get').mockImplementation((helperName: any) => {
        if (helperName === UserRepository) {
          return Promise.resolve(userRepositoryMock);
        }
      });
      try {
        await expect(userHelper.deactivateUserStatus(userEmail)).rejects.toThrowError(
          new BadRequestException('UserAlreadyBlock'),
        );
      } catch (e) {}

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(userEmail);
      expect(userRepositoryMock.findOneAndUpdate).not.toHaveBeenCalled();
    });
  });

  describe('get user email', () => {
    it('should return user email details', () => {
      const user: Partial<User> = {
        name: 'John Doe',
        email: '<EMAIL>',
        status: 1,
        access: new Date('2023-01-02T12:00:00Z'),
        login: new Date('2023-01-03T12:00:00Z'),
        profile_pic: {},
        signature: 1,
        uid: 'user123',
        roles: [
          { roleName: 'Admin', rid: 1 },
          { roleName: 'User', rid: 2 },
        ],
      };

      const result = userHelper.getUserEmail(user);

      expect(result).toEqual([
        {
          active: 'Yes',
          'authentication module': '',
          'authentication name': '',
          'created date': 'Invalid date',
          data: '',
          'e-mail': '<EMAIL>',
          language: 'English',
          'last access': 'Mon, 01/02/2023 - 17:30',
          'last login': 'Tue, 01/03/2023 - 17:30',
          name: 'John Doe',
          permission: null,
          profile_pic: {},
          roles: 'Admin,User',
          signature: 1,
          uid: 'user123',
        },
      ]);
    });

    it('should return an empty array for null user', () => {
      const result = userHelper.getUserEmail(null);
      expect(result).toEqual([]);
    });
  });

  describe('handleB2bB2cRedirect', () => {
    it('should return the original redirectUrl when gid is empty', async () => {
      const gid = '';
      const redirectUrl = 'originalUrl';
      const result = await userHelper.handleB2bB2cRedirect(gid, redirectUrl);
      expect(result).toBe(redirectUrl);
    });

    it('should return lmsSiteUrl when user is B2B or B2C learner', async () => {
      const gid = 'someNonEmptyValue';
      const redirectUrl = 'originalUrl';

      // Mock the ifNotB2COrB2BLearner method to return true
      jest.spyOn(userHelper, 'ifNotB2COrB2BLearner').mockResolvedValue(true);

      // Mock the configService.get method to return a specific value
      jest.spyOn(userHelper['configService'], 'get').mockReturnValue('mockedLmsSiteUrl');

      const result = await userHelper.handleB2bB2cRedirect(gid, redirectUrl);

      // Expect the result to be the value returned by configService.get
      expect(result).toBe('mockedLmsSiteUrl');
    });
  });

  afterEach(() => {
    jest.resetAllMocks();
  });
});
// Add more test cases to cover other branches
