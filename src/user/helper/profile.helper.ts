import { Types } from 'mongoose';
import { BadRequestException, Inject, Logger } from '@nestjs/common';
import { ITaxonomyRepository, TaxonomyRepository } from '../repositories/taxonomy/taxonomy.repository';
import { HelperService } from '../../helper/helper.service';
import { Category, Taxonomies } from '../../db/mongo/schema/taxonomies/taxonomies.schema';
import { ConfigService } from '@nestjs/config';
import { Utility } from './../../common/util/utility';
import { IUserRepository, UserRepository } from '../repositories/user/user.repository';
import { SentinelUserProfessionalData } from '../../db/mysql/entity/sentinel_user_professional_data.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { SentinelUserAcademicData } from '../../db/mysql/entity/sentinel_user_academic_data.entity';
import { S3UploaderService } from '../../helper/s3Client/s3.client.helper';
import { AcademicPayloadDto, ProfessionalPayloadDto, UpdateUserPayloadDto } from '../../internal/dto/update-user-profile-payload.dto';
import { ChangeLogService } from '../../common/services/communication/changelog/changelog.service';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
export class ProfileHelper {
  @Inject() private helperService: HelperService;
  @Inject() private configService: ConfigService;
  @InjectRepository(SentinelUserProfessionalData) private cloud6SentinelUserProfessionalData: Repository<SentinelUserProfessionalData>;
  @InjectRepository(SentinelUserAcademicData) private cloud6SentinelUserAcademicData: Repository<SentinelUserAcademicData>;

  async prepareProfileData(data) {
    const getTaxonomyIds = await this.prepareAndSaveTaxonomyData(data);
    return {
      work_experience: [
        {
          designation: data?.designation || '',
          company: data?.company || '',
          job_function: getTaxonomyIds[Category?.JOB_FUNCTION] || '',
          industry: getTaxonomyIds[Category?.INDUSTRY] || '',
          exp_from_month:'month',
          exp_from_year: 'year',
          exp_to_month: 'month',
          exp_to_year: 'year',
          current_role: data?.current_role || '0',
        },
      ],
      academics: [
        {
          qualification: getTaxonomyIds[Category.QUALIFICATION] || '',
          objective: getTaxonomyIds[Category?.OBJECTIVE_OF_TAKING_COURSE] || '',
          specialization: data?.specialization || '',
          institute: data?.institute || '',
          course_from_month: 'month',
          course_from_year: 'year',
          course_to_month: 'month',
          course_to_year: 'year'
        },
      ],
    };
  }

  async prepareAndSaveTaxonomyData(data) {
    try {
      const taxonomyIds: any = {};

      if (!Utility.isEmpty(data?.industry)) {
        const industry: any = await this.getOneTaxonomy(data?.industry);
        taxonomyIds[Category?.INDUSTRY] = industry._id;
      }
      if (!Utility.isEmpty(data?.qualification)) {
        const qualification: any = await this.getOneTaxonomy(data?.qualification);
        taxonomyIds[Category?.QUALIFICATION] = qualification?._id;
      }

      if (!Utility.isEmpty(data?.objective)) {
        const objective: any = await this.getOneTaxonomy(data?.objective);
        taxonomyIds[Category?.OBJECTIVE_OF_TAKING_COURSE] = objective?._id;
      }

      if (!Utility.isEmpty(data?.job_function)) {
        const jobFunction: any = await this.getOneTaxonomy(data?.job_function);
        taxonomyIds[Category?.JOB_FUNCTION] = jobFunction?._id;
      }
      return taxonomyIds;
    } catch (error: any) {
      Logger.error('prepareAndSaveTaxonomyData', {
        METHOD: this.constructor.name + '@' + this.prepareAndSaveTaxonomyData.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { msg: 'Error occurred while updating profile. Please try again.' };
    }
  }

  async saveDynamicProfileData(uid: string, repository, updateObj, fieldName: string): Promise<Boolean> {
    try {
      // Fetch repositories and helpers in parallel
      const [userRepository, userHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper("UserHelper"),
      ]);

      // Ensure MySQL payload exists before modifying it
      const mysqlPayload = updateObj?.mysql?.[fieldName] ?? [];
      mysqlPayload.forEach((obj) => (obj.uid = uid));

      // Create a copy of the MySQL payload (excluding the current field)
      const { [fieldName]: _, ...mysqlUserPayload } = updateObj?.mysql || {};

      // Remove MySQL data from `updateObj` to avoid saving it in MongoDB
      const mongoUpdateObj = { ...updateObj , updated_on : Date.now() };
      delete mongoUpdateObj.mysql;

      const [updatedUserProfile] = await Promise.all([
        // MongoDB update
        userRepository.findOneAndUpdate({ uid }, mongoUpdateObj),
        // Mysql delete existind data
        repository.delete({ uid }),
        // Mysql user update
        userHelper.updateCloud6SentinelByUidOrMail({ uid, updated_on : Date.now(), ...mysqlUserPayload }),
      ]);

      // MySQL updatedata if it exists
      const updatedUserProfileCloud6 = mysqlPayload.length > 0 ? await repository.save(mysqlPayload) : [];
      if (!updatedUserProfileCloud6.length) {
        Logger.error("MySQL data update/save error", {
          METHOD: `${this.constructor.name}@saveDynamicProfileData`,
          MESSAGE: "Failed to update or save MySQL data",
          REQUEST: { uid, fieldName, mysqlPayload },
          TIMESTAMP: Date.now(),
        });
      }
      return Boolean(updatedUserProfile?.uid);
    } catch (error: any) {
      Logger.error("saveDynamicProfileData", {
        METHOD: `${this.constructor.name}@saveDynamicProfileData`,
        MESSAGE: error.message,
        REQUEST: { uid, fieldName },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async getOneTaxonomy(identifier: string): Promise<Taxonomies | null> {
    try {
      if (!identifier) return null;

      const taxonomyRepository = await this.helperService.get<ITaxonomyRepository>(TaxonomyRepository);
      const query = Types.ObjectId.isValid(identifier)
        ? { _id: new Types.ObjectId(identifier) }
        : { tid: Number(identifier) };

      return taxonomyRepository.findOne(query);      
    } catch (error : any) {
      Logger.log('getOneTaxonomy', {
        METHOD: this.constructor.name + '@' + this.getOneTaxonomy.name,
        MESSAGE: error.message,
        REQUEST: identifier,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async userProfileData(params: { uid: number }) {
    try {
      const userRepository = await this.helperService.getHelper<IUserRepository>(UserRepository);
      const respData = await userRepository.findByUID(params?.uid);
      return respData;
    } catch (error: any) {
      Logger.log('userProfileData', {
        METHOD: this.constructor.name + '@' + this.userProfileData.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async saveStaticProfileData(uid: string, updateObj: UpdateUserPayloadDto): Promise<Boolean> {
    try {
      const [userRepository, userHelper, dataChangeLogService] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper("UserHelper"),
        this.helperService.get<ChangeLogService>(ChangeLogService),
      ]);
      const userBeforeUpdate = await userHelper.getCloud6SentinelUserByUidOrMail({ uid })
      let mysqlPayload = updateObj;
      if(updateObj.mysql){
        mysqlPayload = updateObj.mysql
        delete updateObj.mysql
      }
      const [mongoUpdate, mysqlUpdate] = await Promise.all([
        // updating user details in mongo
        userRepository.findOneAndUpdate({ uid }, { updated_on: Date.now(), ...updateObj}),
        // updating user details in mysql
        userHelper.updateCloud6SentinelByUidOrMail({ uid, updated_on: Date.now(), ...mysqlPayload })
      ])

      // added change log for display name changes
      const ChangeLogData = {
        userId: mongoUpdate?.uid,
        userEmail: mongoUpdate?.email,
        module: "User Profile Update",
        recordTable: "users",
        type: "update",
        oldValue: userBeforeUpdate,
        newValue: mongoUpdate,
        changeDate: new Date().toLocaleString("en-GB", { day: "2-digit", month: "2-digit", year: "numeric", hour: "2-digit", minute: "2-digit", second: "2-digit" })
      };
      dataChangeLogService.changeLogRequest(ChangeLogData);
      
      return mongoUpdate.uid && mysqlUpdate;
    } catch (error : any) {
      Logger.error('saveStaticProfileData', {
        METHOD: this.constructor.name + '@' + this.saveStaticProfileData.name,
        MESSAGE: error.message,
        REQUEST: { uid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async uploadProfilePic(file:any, uid: string) {
    try {
      if(file===''){
        return null;
      }
      const s3UploaderService = await this.helperService.get<S3UploaderService>(S3UploaderService);
  
      const bucket = this.configService.get('s3BucketName');
      const key = `${uid}-profile-${Date.now()}-${file.originalname}`;
      
      // 🔹 **Upload to S3**
      await s3UploaderService.uploadToS3(bucket, key, file.buffer, file.mimetype);
      
      const profileImageArray = {
        filename: key,
        filemime: file.mimetype,
        filesize: file.size.toString(),
        status: 'active',
        timestamp: new Date().toISOString(),
      };

      return profileImageArray;
    } catch (error: any) {
      Logger.error('uploadProfilePic', {
        METHOD: this.constructor.name + '@' + this.uploadProfilePic.name,
        MESSAGE: error.message,
        REQUEST: { uid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
  
      throw new BadRequestException('Failed to upload profile picture to S3');
    }
  }



  async saveContactProfileData(uid: string, payload: UpdateUserPayloadDto): Promise<Boolean> {
    try {
      const updateObj = {
        ...payload,
        country_of_residence: payload?.country_of_residence ? 
        JSON.parse(payload?.country_of_residence)?.name ? JSON.parse(payload?.country_of_residence)?.name : payload?.country_of_residence 
        : ''
      }
      delete updateObj.edit_type
      return this.saveStaticProfileData(uid, updateObj);
    } catch (error: any) {
      Logger.log('saveStaticProfileData', {
        METHOD: this.constructor.name + '@' + this.saveStaticProfileData.name,
        MESSAGE: error.message,
        REQUEST: { uid, payload },
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  async saveBasicProfileData(uid: string, payload: UpdateUserPayloadDto): Promise<Boolean> {
    try {
      let updateObj = {
        ...payload,
        dob: Utility.convertToTimestampUnix(payload.dob)
      }
      delete updateObj.edit_type

      if(payload.profile_pic) {
        const profileImagedata = await this.uploadProfilePic(payload.profile_pic, uid);
        updateObj = {
          ...updateObj,
          profile_pic : profileImagedata || {},
          mysql : {
           ...updateObj,
           profile_pic : JSON.stringify(profileImagedata) || '' 
          }
       }
      }

      return this.saveStaticProfileData(uid, updateObj);
    } catch (error: any) {
      Logger.log('saveStaticProfileData', {
        METHOD: this.constructor.name + '@' + this.saveStaticProfileData.name,
        MESSAGE: error.message,
        REQUEST: { uid, payload },
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  async saveProfessionalProfileData(data: ProfessionalPayloadDto, uid: string): Promise<Boolean> {
    try {
      const requiredFields = [
        "company_designation",
        "company_name",
        "job_function",
        "industry",
        "exp_from_month",
        "exp_from_year",
        "exp_to_year",
        "exp_to_month",
        "current_role"
      ];

      const isValid = requiredFields.every((field) => {
        return Array.isArray(data[field]) && data[field].length === data[requiredFields[0]].length;
      });
      // Create work experience array for MongoDB
      let workExperienceArray = isValid ? data.company_designation.map((_: any, index: number) => ({
        designation: data.company_designation[index] || "",
        company: data.company_name[index] || "",
        job_function: data.job_function[index] || "",
        industry: data.industry[index] || "",
        exp_from_month: data.exp_from_month[index] || "",
        exp_from_year: data.exp_from_year[index] || "",
        exp_to_month: (data.exp_to_month || [])[index] || "", // Handle missing key
        exp_to_year: (data.exp_to_year || [])[index] || "", // Handle missing key
        current_role: (data.current_role || [])[index] || "0" // Handle missing key
      })) : [];
      
      // From 'mobile' side we usually get 'tid' so would have to typeCase to mongo ObjectId
      // internal call we recieve ObjectId
      if(workExperienceArray.length && !Types.ObjectId.isValid(workExperienceArray[0]?.industry[0])){
        const taxonomyPromisesMongo = isValid ? workExperienceArray.map(async (exp) => {
          const [jobFunctionTaxonomy, industryTaxonomy] = await Promise.all([
            this.getOneTaxonomy(exp?.job_function || ""),
            this.getOneTaxonomy(exp?.industry || ""),
          ]);
          return {
            ...exp,
            job_function: jobFunctionTaxonomy?._id.toString() || "",
            industry: industryTaxonomy?._id.toString() || "",
          };
        }) : [];
        workExperienceArray = await Promise.all(taxonomyPromisesMongo)
      }

      // Fetch job function and industry taxonomies in parallel for all experiences
      const taxonomyPromises = isValid ? workExperienceArray.map(async (exp) => {
        const [jobFunctionTaxonomy, industryTaxonomy] = await Promise.all([
          this.getOneTaxonomy(exp?.job_function || ""),
          this.getOneTaxonomy(exp?.industry || ""),
        ]);
        return {
          ...exp,
          joining_date: `${exp.exp_from_month}-${exp.exp_from_year}` || "",
          relieving_date: exp.current_role==="0" ? `${exp.exp_to_month}-${exp.exp_to_year}` : "",
          job_function: jobFunctionTaxonomy?.name || "",
          industry: industryTaxonomy?.name || "",
          designation: exp?.designation || "",
          company: exp?.company || "",
        };
      }) : [];

      const [workExperienceArrayMysql, totalWorkExperienceTaxonomy] = await Promise.all([
        Promise.all(taxonomyPromises),
        // Fetch total work experience taxonomy
        this.getOneTaxonomy(data?.where_are_you_in_career),
      ]);

      // Construct update object
      let updateObj = {
        work_experience: workExperienceArray,
        where_are_you_in_career: totalWorkExperienceTaxonomy?._id.toString(),
        mysql: {
          work_experience: workExperienceArrayMysql,
          where_are_you_in_career: totalWorkExperienceTaxonomy?.tid.toString(),
        },
      };

      return this.saveDynamicProfileData(uid, this.cloud6SentinelUserProfessionalData, updateObj, "work_experience");
    } catch (error: any) {
      console.error("Error:", error);
      Logger.error("saveProfessionalProfileData", {
        METHOD: `${this.constructor.name}@saveProfessionalProfileData`,
        MESSAGE: error.message,
        REQUEST: { data, uid },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async saveAcademicProfileData(data: AcademicPayloadDto, uid: string): Promise<Boolean> {
    try {
      const requiredFields = [
        "field_qualification",
        "institute_name",
        "field_specialization",
        "course_from_month",
        "course_from_year",
        "course_to_month",
        "course_to_year"
      ];

      const isValid = requiredFields.every(field =>
        Array.isArray(data[field]) && data[field].length === data[requiredFields[0]].length
      );

      // Prepare academics array for MongoDB
      let academicsArray = isValid ? data.field_qualification.map((_: any, index: number) => ({
        qualification: data.field_qualification[index] || "",
        institute: data.institute_name[index] || "",
        specialization: data.field_specialization[index] || "",
        course_from_month: data.course_from_month[index] || "",
        course_from_year: data.course_from_year[index] || "",
        course_to_month: data.course_to_month[index] || "",
        course_to_year: data.course_to_year[index] || "",
      })) : [];

      // From 'mobile' side we usually get 'tid' so would have to typeCase to mongo ObjectId
      // internal call we recieve ObjectId
      if(isValid && academicsArray.length && !Types.ObjectId.isValid(academicsArray[0]?.qualification[0])){
        const taxonomyPromisesMongo = academicsArray.map(async (academic) => {
          const qualificationTaxonomy = await this.getOneTaxonomy(academic?.qualification || "");
          return {
            ...academic,
            qualification: qualificationTaxonomy?._id.toString() || "",
          };
        });
        academicsArray = await Promise.all(taxonomyPromisesMongo)
      }
        
      // Fetch taxonomies in parallel for qualifications
      const taxonomyPromises = isValid ? academicsArray.map(async (academic) => {
        const qualificationTaxonomy = await this.getOneTaxonomy(academic?.qualification || "");
        return {
          qualification: qualificationTaxonomy?.name || "",
          institute: academic.institute,
          specialization: academic.specialization || "",
          course_start_date: `${academic.course_from_month}-${academic.course_from_year}` || "",
          course_end_date: `${academic.course_to_month}-${academic.course_to_year}` || "",
        };
      }) : [];

      // Fetch highest level of education taxonomy in parallel with specialization taxonomies
      const [academicsArrayMysql, highestEducationTaxonomy] = await Promise.all([
        Promise.all(taxonomyPromises),
        this.getOneTaxonomy(data?.highest_level_of_education),
      ]);

      // Construct update object
      let updateObj = {
        academics: academicsArray,
        highest_level_of_education: highestEducationTaxonomy?._id.toString(),
        mysql: {
          academics: academicsArrayMysql,
          highest_level_of_education: highestEducationTaxonomy?.tid.toString(),
        },
      };
      return this.saveDynamicProfileData(uid, this.cloud6SentinelUserAcademicData, updateObj, "academics");
    } catch (error: any) {
      Logger.error("saveAcademicProfileData", {
        METHOD: `${this.constructor.name}@saveAcademicProfileData`,
        MESSAGE: error.message,
        REQUEST: { data, uid },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async saveOutcomeDetailsData(uid: string , data: any) {

    try {
      // Fetch total work experience taxonomy
      const objectiveTaxonomy = await this.getOneTaxonomy(data?.objective_taking_course);
      const updateObj = {
        objective_taking_course: objectiveTaxonomy?._id?.toString(),
        mysql: {
          objective_taking_course: objectiveTaxonomy?.tid?.toString(),
        },
      };
      // Sync profile data across databases
      return this.saveStaticProfileData(uid , updateObj)
    } catch (error: any) {
      Logger.error("saveOutcomeDetailsData", {
        METHOD: `${this.constructor.name}@saveOutcomeDetailsData`,
        MESSAGE: error.message,
        REQUEST: { data, uid },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
  
      return false;
    }
  }

  async saveProfileData(data, user) {
    try {
      if (!data?.edit_type) {
        throw new BadRequestException("Invalid edit_type.");
      }
      switch (data.edit_type) {
        case 'professional':
          return await this.saveProfessionalProfileData(data, user.uid);

        case 'academics':
          return await this.saveAcademicProfileData(data, user.uid);

        case 'contact':
          return await this.saveContactProfileData(user.uid, data);

        case 'basic':
          return await this.saveBasicProfileData(user.uid, data);
        
        case 'outcome':
            return await this.saveOutcomeDetailsData(user.uid, data)  

        default:
          return true;
      }

    } catch (error: any) {
      Logger.error('saveProfileData', {
        METHOD: this.constructor?.name + '@' + this.saveProfileData?.name,
        MESSAGE: error.message,
        REQUEST: { data, user },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }

  async fetchProfileData(uid, api = 0) {
    try {
      let userData = await this.userProfileData({ uid: uid });
      // Fetch taxonomies in parallel
      const [selectedExperience, selectedEducation, selectedOutcome] = await Promise.all([
        this.getOneTaxonomy(userData?.where_are_you_in_career || ""),
        this.getOneTaxonomy(userData?.highest_level_of_education?.toString() || ""),
        this.getOneTaxonomy(userData?.objective_taking_course?.toString() || "")
      ]);
      
      if(api){
        // Initialize arrays to avoid conditionals later
        let workExperiencePromises = [];
        let academicsPromises = [];

        // Process work experience if available
        if(userData?.work_experience?.length){
          workExperiencePromises = userData.work_experience.map(async (exp) => {
            const [jobFunction, industry] = await Promise.all([
              this.getOneTaxonomy(exp?.job_function?.toString() || ""),
              this.getOneTaxonomy(exp?.industry?.toString() || ""),
            ]);
            return { ...exp, job_function: jobFunction || {}, industry: industry || {} };
          });
        }
        
        // Process academics if available
        if(userData?.academics?.length){
          academicsPromises = userData.academics.map(async (academic) => {
            const qualification = await this.getOneTaxonomy(academic?.qualification?.toString() || "");
            return { ...academic, qualification: qualification || {} };
          });
        }

        // Process all promises in parallel
        const [workExperience, academics] = await Promise.all([
          Promise.all(workExperiencePromises),
          Promise.all(academicsPromises)
        ]);

        // Return structured response with all data
        return {
          ...JSON.parse(JSON.stringify(userData)),
          
          where_are_you_in_career: selectedExperience || {},
          highest_level_of_education: selectedEducation || {},
          objective_taking_course: selectedOutcome || {},
          work_experience: workExperience,
          academics: academics
        };
      }
      return {
        selectedExperienceId: selectedExperience?._id.toString() || "",
        selectedEducationId: selectedEducation?._id.toString() || "",
        selectedOutcomeId: selectedOutcome?._id.toString() || "",
        workExperience: userData?.work_experience || [],
        academicExperience: userData?.academics || [],
        userData: userData,
        profileCompletion: await this.getProfileCompletionStats(userData),
      };
    } catch (error: any) {
      Logger.error('fetchProfileData', {
        METHOD: `${this.constructor.name}@fetchProfileData`,
        MESSAGE: error.message,
        REQUEST: { uid },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      throw error;
    }
  }

  getIndustryDisableList(): string[] {
    try {
      const isProduction = process.env.NODE_ENV === 'production';

      const DisableList = this.configService.get('industry_disable_list') || []
      const prodDisableList = this.configService.get('prod_industry_disable_list') || []
      // industry_disable values
      return isProduction ? prodDisableList : DisableList;   
    } catch (error: any) {
      Logger.error('getIndustryDisableList', {
        METHOD: this.constructor.name + '@' + this.getIndustryDisableList.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      throw error;
    }
  }



  async getProfileCompletionStats(profileData: Record<string, any>, showProfile = true, showPhoneNo = 'Yes', concat = true) {
    try {
      let categories: Record<string, string[]> = {
          Basic: ["title", "first_name", "middle_name", "last_name", "linkedin_url", "training_funded_by"],
          Contact: ["email", "timezone"],
          Professional: ["where_are_you_in_career", "work_experience"],
          Academics: ["highest_level_of_education", "academics"],
          LearningOutcome: ["objective_taking_course"]
      };

      // Dynamically add fields based on conditions
      if (showProfile) {
          categories.Basic.push("gender", "dob", "profile_pic");
          categories.Contact.push("country_of_residence", "state", "location", "correspondence_address");
      }

      if (showPhoneNo !== 'No') {
          categories.Contact.push("phone_no");
      }

      let totalFilled = 0, totalFields = 0;
      let categoryCompletion: Record<string, string> = {};

      // Pre-fetch `where_are_you_in_career` and `industry_disable`
      const totalWorkExperience = String(profileData?.where_are_you_in_career || "");
      
      const industryDisableList = this.getIndustryDisableList(); 
      // Modify work experience check based on `industry_disable`
      if (totalWorkExperience && industryDisableList.includes(totalWorkExperience)) {
          categories.Professional = ["work_experience"]; // Remove where_are_you_in_career
      }

      // Helper function to check if a field is filled
      function isFieldFilled(field: string, value: any): boolean {
          if (field === "where_are_you_in_career") {
              return totalWorkExperience !== undefined && totalWorkExperience !== null;
          }
          if (Array.isArray(value)) return value?.length > 0; // Check for non-empty arrays
          if (typeof value === "object" && value !== null) return Object.keys(value || {}).length > 0; // Check for non-empty objects
          return value !== undefined && value !== null && value !== ""; // Check for valid strings, numbers, etc.
      }

      // Calculate completion percentage for each category
      for (const [category, fields] of Object.entries(categories)) {
          let filledFields = fields.filter(field => isFieldFilled(field, profileData?.[field]));
          let categoryPercent = fields.length ? (filledFields.length / fields.length) * 100 : 0;

          categoryCompletion[category] = categoryPercent.toFixed(0) + "%";
          totalFilled += filledFields.length;
          totalFields += fields.length;
      }

      // Avoid division by zero
      const overallCompletion = totalFields > 0 ? (totalFilled / totalFields) * 100 : 0;

      return {
          categoryCompletion,
          overallCompletion: concat ? overallCompletion.toFixed(0) + "%" : overallCompletion.toFixed(0)
      };
    } catch (error: any) {
      Logger.error('getProfileCompletionStats', {
        METHOD: this.constructor.name + '@' + this.getProfileCompletionStats.name,
        MESSAGE: error.message,
        REQUEST: { profileData, showProfile, showPhoneNo },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      throw error;
    }
  }

  async handleOobtaxonomyData(updateDetails: any): Promise<boolean> {
    try {
      if (Object.keys(updateDetails).length > 0){
        //get taxonomy industry name using the object ID
        const [
          industryName,
          whereAreYouInCareer,
          highestLevelOfEducation,
          objectiveTakingCourse
        ] = await Promise.all([
          this.getOneTaxonomy(updateDetails?.industry),
          this.getOneTaxonomy(updateDetails?.where_are_you_in_career),
          this.getOneTaxonomy(updateDetails?.highest_level_of_education),
          this.getOneTaxonomy(updateDetails?.objective_of_taking_course),
        ]);
        if (industryName) {
          await this.cloud6SentinelUserProfessionalData.save({
            uid: updateDetails?.uid,
            industry: industryName.name,
          });
        }
        let userHelper = await this.helperService.getHelper("UserHelper");
        if (whereAreYouInCareer || highestLevelOfEducation || objectiveTakingCourse) {
          const updateRecords: Partial<SentinelUser> = {
            uid: Number(updateDetails?.uid),
            ...(whereAreYouInCareer && { where_are_you_in_career: whereAreYouInCareer.tid.toString() }),
            ...(highestLevelOfEducation && { highest_level_of_education: highestLevelOfEducation.tid.toString() }),
            ...(objectiveTakingCourse && { objective_taking_course: objectiveTakingCourse.tid.toString() }),
          };
          await userHelper.updateCloud6SentinelByUidOrMail(updateRecords);
        }
        return true;
      }
      return false;
    } catch (error: any) {
      Logger.error('handleOobtaxonomyData', {
        METHOD: `${this.constructor.name}@${this.handleOobtaxonomyData.name}`,
        MESSAGE: error.message,
        REQUEST: updateDetails,
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
}

async updateUserProfileAcademicData(updateDetails: any): Promise<boolean> {
  try {
    if (Object.keys(updateDetails).length > 0){
      //get taxonomy industry name using the object ID
      const qualificationName = await this.getOneTaxonomy(updateDetails?.qualification);
      if (qualificationName) {
        const updateRecords = new SentinelUserAcademicData();
        updateRecords.uid = updateDetails?.uid;
        updateRecords.qualification = qualificationName.name;
        await this.cloud6SentinelUserAcademicData.save(updateRecords);
        return true;
      }
    }
    return false;

  } catch (error: any) {
    Logger.error('updateUserProfileAcademicData', {
      METHOD: `${this.constructor.name}@${this.updateUserProfileAcademicData.name}`,
      MESSAGE: error.message,
      REQUEST: updateDetails,
      RESPONSE: error.stack,
      TIMESTAMP: Date.now(),
    });
    return false;
  }
}

async deleteImage(email: string): Promise<any> {
  const response = {
    type: 'error',
    msg: 'Some error occurred while deleting profile image.',
  };

  try {
    const [userRepository, userHelper] = await Promise.all([
      this.helperService.get<IUserRepository>(UserRepository),
      this.helperService.getHelper('UserHelper'),
    ]);

    // Fetch user from MongoDB
    const user: Partial<User> = await userRepository.getUserByEmail(email);

    if (!user || !user.profile_pic) {
      response.msg = 'Profile image does not exist.';
      return response;
    }

    // Remove profile_pic from MongoDB
    const [updateResult] =  await Promise.all([
      userRepository.findOneAndUpdate({ email }, { profile_pic: null }),
      userHelper.updateCloud6SentinelByUidOrMail({
        uid: user.uid,
        profile_pic: null,
      }),
    ]);

if (updateResult){
    const updatedUser: Partial<User> = await userRepository.getUserByEmail(email);
    const profileStats = await this.getProfileCompletionStats(updatedUser);

    return {
      type: 'success',
      msg: 'Profile picture removed successfully.',
      profileCompletion: profileStats?.overallCompletion,
    }
  } 
    else{
      return response
    }
  } catch (error: any) {
    Logger.error('deleteUserProfileImage error', {
      METHOD: `${this.constructor.name}@deleteUserProfileImage`,
      MESSAGE: error,
      INPUT: { email },
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });

    return response;
  }
}

  async syncTaxonomyDataWithMySQLDrupal(latestuserData : Partial<User>, edit_type : String[] ): Promise<boolean> {
    try {
      const cloud6Service: Cloud6Service = await this.helperService.get<Cloud6Service>(Cloud6Service);
      const reqPayload = {
        type : edit_type,
        data : latestuserData
      };
      const response = await cloud6Service.syncTaxonomyUserDataWithMySQL(reqPayload);
      return response;
    } catch (error: any) {
      Logger.error('syncTaxonomyDataWithMySQLDrupal', {
        METHOD: `${this.constructor.name}@${this.syncTaxonomyDataWithMySQLDrupal.name}`,
        MESSAGE: error.message,
        REQUEST: {
          edit_type : edit_type,
          data : latestuserData
        },
        RESPONSE: error.stack,
        TIMESTAMP: Date.now(),
      });
      return false;
    }
  }  
}
