import { BadRequestException, Inject } from '@nestjs/common';
import { Logger } from '../../logging/logger';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { User } from '../../db/mongo/schema/user/user.schema';
import { IRoleRepository, RoleRepository } from '../repositories/role/role.repository';
import { IUserRepository, UserRepository } from '../repositories/user/user.repository';
import { Ic9Service } from '../../common/services/communication/ice9/ice9.service';
import { Types } from 'mongoose';
import { CachingService } from '../../caching/caching.service';

export class UserMgmtUtilityHelper {
  @Inject() configService: ConfigService;
  @Inject(HelperService) private readonly helperService: HelperService;
 
  async prepareAssignRoleList(roleList, userType = '', skipAssignDefaultRole = false) {
    try {
      const roleRepository = await this.helperService.get<IRoleRepository>(RoleRepository);
      if (Array.isArray(roleList)) {
        if (
          !skipAssignDefaultRole &&
          !(
            userType &&
            userType.toUpperCase() === 'ATP' ||
            roleList.includes('looper_affiliate_student') ||
            roleList.includes('looper_affiliate_user')
          )
        ) {
          roleList.push('looper_student');
        }
        const roles = roleList.length > 0 ? await roleRepository.findAll({ roleName: { $in: roleList } }) : [];
        return roles.map((role) => role['_id']);
      }
      return [];
    } catch (err: any) {
      Logger.log('Error in prepareAssignRoleList', err);
      return [];
    }
  }

  async prepareSentinelUserRoleList(roleList: (string | Types.ObjectId)[], uid: string) {
    try {
      const roleRepository = await this.helperService.get<IRoleRepository>(RoleRepository);
      const objectIds: Types.ObjectId[] = [];
      const roleNames: string[] = [];
  
      for (const item of roleList) {
        if (item instanceof Types.ObjectId) {
          objectIds.push(item);
        } else if (typeof item === 'string' && Types.ObjectId.isValid(item)) {
          objectIds.push(new Types.ObjectId(item));
        } else if (typeof item === 'string') {
          roleNames.push(item);
        }
      }
  
      const query: any = {};
      if (objectIds.length || roleNames.length) {
        query.$or = [];
  
        if (roleNames.length) {
          query.$or.push({ roleName: { $in: roleNames } });
        }
  
        if (objectIds.length) {
          query.$or.push({ _id: { $in: objectIds } });
        }
      }
  
      const roles = query.$or?.length ? await roleRepository.findAll(query) : [];
      return roles.map((role) => ({ rid: role['rid'], uid }));
    } catch (err: any) {
      Logger.log('Error in prepareSentinelUserRoleList', err);
      return [];
    }
  }

  async liveClassRedirectUrl(redirectUrl: string, login = 0, calendarUrl: string): Promise<string> {
    try {
      let urlParam = calendarUrl;
      if (urlParam) {
        urlParam = encodeURIComponent(urlParam);
        if (login && login === 1) {
          if (redirectUrl) {
            urlParam = `${redirectUrl}&calendar_url=${urlParam}`;
          } else {
            urlParam = `${redirectUrl}?calendar_url=${urlParam}`;
          }
        }
        return urlParam;
      } else {
        return redirectUrl;
      }
    } catch (error: any) {
      Logger.error('liveClassRedirectUrl', {
        METHOD: this.constructor?.name + '@' + this.liveClassRedirectUrl.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async getTimezoneFromCountryCode(countryCode: string, timezone: string): Promise<string> {
    try {
      if (countryCode && !timezone) {
        const ice9Service = await this.helperService.get<Ic9Service>(Ic9Service);
        const response = await ice9Service.getCountryJson();
        if (response?.data) {
          const modifiedString = response?.data.replace('var countryDataIe = ', '');
          const countryData = JSON.parse(modifiedString);
          const filteredData = countryData.filter((item) => item.code === countryCode);
          timezone = filteredData.length > 0 ? filteredData[0]?.timeZone : 'America/Chicago';
        }
        return timezone;
      }
    } catch (error: any) {
      Logger.error('getTimezoneFromCountryCode', {
        METHOD: this.constructor.name + '@' + this.getTimezoneFromCountryCode.name,
        MESSAGE: error.message,
        REQUEST: { timezone },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async updateUserTimezone(requestedData: { uid: string; country: string }): Promise<Partial<User>> {
    try {
      const [ userRepository, UserHelper , timeZone] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper("UserHelper"),
        this.getTimezoneFromCountryCode(requestedData?.country, '')
      ]);
     
      const [userUpdate] = await Promise.all([
        userRepository.findOneAndUpdate({ uid: requestedData?.uid }, { timezone: timeZone || 'America/Chicago' }),
        UserHelper.updateCloud6SentinelByUidOrMail({ uid: Number(requestedData?.uid), timezone: timeZone || 'America/Chicago'})
      ]);
      if (this.configService.get('enableDrupalSync')) {
        await UserHelper.syncUserDataWithMySQLDrupal({
          uid: requestedData.uid,
          timezone: timeZone,
        });
      }
  
      if(userUpdate.uid ) {
        return {
          uid: userUpdate.uid,
          timezone: timeZone
        };
      } else {
        throw new BadRequestException('Something went wrong, please try again.')
      }

    } catch (error: any) {
      Logger.error('getTimezoneFromCountryCode', {
        METHOD: this.constructor.name + '@' + this.getTimezoneFromCountryCode.name,
        MESSAGE: error.message,
        REQUEST: requestedData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
     
    }
  }

  /**
   * Method to get user information for API
   * @param userObj
   * @returns
   */
  async getUserInfoForAPI(userObj) {
    let userData = {};
    const authHelper = await this.helperService.getHelper('AuthHelper');
    if (userObj?.uid && userObj?.email) {
      userData = {
        id: userObj?.uid || '',
        email: userObj?.email || '',
        name: userObj?.display_name || '',
        roles: userObj?.roles.reduce((acc, role) => {
          acc[role.rid] = role.roleName;
          return acc;
        }, {}),
        reset_url: await authHelper.getUserPassResetUrl(userObj?.uid)?.url,
      };
    }
    return userData;
  }

  getProfileCompletionStats(userObj: Partial<User>) {
    if (!userObj) {
      return 0;
    }

    const fieldCheckList = {
      webPresenceFlag:
        userObj?.urls?.linkedin_url ||
        userObj?.urls?.facebook_url ||
        userObj?.urls?.blog_url ||
        userObj?.urls?.twitter_url ||
        userObj?.urls?.website_url ||
        userObj?.urls?.other_url
          ? 1
          : 0,
      workExpFlag: userObj?.work_experience?.length > 0 ? 1 : 0,
      academicsFlag: userObj?.academics?.length > 0 ? 1 : 0,
      interestFlag: userObj?.interests?.length > 0 ? 1 : 0,
      displayNameFlag: userObj?.display_name ? 1 : 0,
      userNameFlag: userObj?.name ? 1 : 0,
      userEmailFlag: userObj?.email ? 1 : 0,
      userPhoneFlag: userObj?.phone_no ? 1 : 0,
      userLocationFlag: userObj?.location ? 1 : 0,
      userGenderFlag: userObj?.gender ? 1 : 0,
    };

    const resultList = Object.values(fieldCheckList);
    const completedFields = resultList.reduce((sum, value) => sum + value, 0);
    const numOfFieldChecked = Object.keys(fieldCheckList).length;

    return (completedFields / numOfFieldChecked) * 100;
  }

  /**
   * Method to check and update the attempt limits for a given cache key and timeout key.
   * This method ensures that the number of attempts does not exceed the maximum allowed attempts
   * and enforces a timeout period if the limit is exceeded.
   * 
   * @param cacheKey - The cache key used to track the number of attempts.
   * @param timeoutKey - The cache key used to track the timeout period.
   * @param maxAttempts - The maximum number of allowed attempts before triggering a timeout.
   * @param waitMinutes - The duration (in minutes) to wait before allowing further attempts after exceeding the limit.
   * @param cacheTTLMinutes - The time-to-live (TTL) for the cache entries in minutes.
   * @returns A promise that resolves to an object containing a message (`msg`) and a type (`type`), 
   *          indicating the result of the operation (e.g., success or notice).
   * 
   */
  async checkAndUpdateAttempt(
    cacheKey: string,
    timeoutKey: string,
    maxAttempts: number,
    waitMinutes: number,
    cacheTTLMinutes: number
  ): Promise<{ msg: string; type: string }> {
    try {
      const cacheService = await this.helperService.get<CachingService>(CachingService);

      const email_cache_count: number = Number(await cacheService.get(cacheKey)) || 0;
      const email_timeout: number = Number(await cacheService.get(timeoutKey)) || 0;

      if (!email_timeout) {
        if (email_cache_count >= maxAttempts) {
          await cacheService.set(timeoutKey, Date.now(), 60 * cacheTTLMinutes);
          return {
            type: 'notice',
            msg: `Attempt limit exceeded. Please try again after ${waitMinutes} minutes.`,
          };
        } else {
          await cacheService.set(cacheKey, email_cache_count + 1, 60 * cacheTTLMinutes);
          return {
            type: 'success',
            msg: 'Attempt recorded.',
          };
        }
      } else {
        const timeoutPeriod = waitMinutes * 60 * 1000; // convert minutes to ms
        const timePassed = Date.now() - email_timeout;
        const timeLeft = Math.max(0, timeoutPeriod - timePassed);
        if (timeLeft <= 0) {
          Logger.info('Timeout expired. Resetting attempt count.');
          // optionally reset the attempt count
          await cacheService.set(cacheKey, 1, 1);
          await cacheService.set(timeoutKey, 0, 1); 
          return {
            type: 'success',
            msg: 'Attempt recorded.',
          };
        }

        const minutes = Math.floor(timeLeft / 60000);
        const seconds = String(Math.floor((timeLeft % 60000) / 1000)).padStart(2, '0');
        const leftTimeText = minutes > 0 ? `${minutes}:${seconds} minutes` : `${seconds} seconds`;

        Logger.info('Attempt limit exceeded:', { minutes, seconds, leftTimeText });

        return {
          type: 'notice',
          msg: `Attempt limit exceeded. Please try again after ${leftTimeText}.`,
        };
      }
    }  catch (error: any) {
      Logger.error('checkAndUpdateAttempt', {
        METHOD: this.constructor.name + '@' + this.checkAndUpdateAttempt.name,
        MESSAGE: error.message,
        REQUEST: {
          CACHE_KEY: cacheKey,
          TIMEOUT_KEY: timeoutKey,
          MAX_ATTEMPTS: maxAttempts,
          WAIT_MINUTES: waitMinutes,
          CACHE_TTL_MINUTES: cacheTTLMinutes,
        },
        STACK: error.stack,
        TIMESTAMP: new Date().toISOString(),
      });
      throw new Error('An error occurred while checking and updating attempt limits.');
    }
  }  

  getTitleGenderList(): { title: Record<string, string>; gender: Record<string, string>; industry_disable: number[] } {
    const result = {
      title: {
        "Mr.": "Mr.",
        "Mrs.": "Mrs.",
        "Ms.": "Ms.",
        "Dr.": "Dr.",
        "Prof.": "Prof.",
        "Other": "Other"
      },
      gender: {
        "F": "Female",
        "M": "Male",
        "NonBinary": "Non binary",
        "O": "Other",
        "PreferNotToSay": "Prefer not to say"
      },
      industry_disable: 
         [173190, 171034]
    };

    return result;
  }

  /**
   * Method to show the delete account button to B2C learners only based on configuration
   * @param lgid number | null
   * @param emailDomain string
   * @returns 'Y' | 'N'
   */
  // TODO : Implementation required after 'delete' functionality is implemented.
  showDeleteButton(lgid: number | null, emailDomain: string): 'Y' | 'N' {
    try {
      const gdprDeletePublicEnabled = this.configService.get<string>('gdpr_delete_public_enabled')?.toLowerCase() || 'no';
      const isB2CLearner = !lgid || lgid === 2;
      const emailDomainLower = emailDomain.toLowerCase();

      const isBlockedPublicDomain = this.configService.get<string>('isBlockedPublicDomain').includes(emailDomainLower);
      const isSimplilearnDomain = this.configService.get<string>('isSimplilearnDomain').includes(emailDomainLower);

      if (gdprDeletePublicEnabled === 'no') {
        return isB2CLearner && isBlockedPublicDomain ? 'Y' : 'N';
      }

      if (gdprDeletePublicEnabled === 'yes') {
        return isB2CLearner && !isSimplilearnDomain ? 'Y' : 'N';
      }

      return 'N';
    } catch (error: any) {
      Logger.error('showDeleteButton', {
        METHOD: this.constructor.name + '@' + this.showDeleteButton.name,
        MESSAGE: error.message,
        REQUEST: { lgid, emailDomain },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return 'N';
    }
  }
  
  showDownloadButton(emailDomain: string): string {
    try {
      const gdprEnabled = this.configService.get('gdprEnabled') === 1;

      if (gdprEnabled) {
        return 'Y';
      }

      const allowedDomains = this.configService.get<string>('isSimplilearnDomain');
      return allowedDomains.includes(emailDomain.toLowerCase()) ? 'Y' : 'N';
    } catch (error: any) {
      Logger.error('showDownloadButton', {
        METHOD: this.constructor.name + '@' + this.showDownloadButton.name,
        MESSAGE: error.message,
        REQUEST: { emailDomain },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return 'N';
    }
  }

  async clearCacheAndCookies(res, cacheKey: string) {
    const cacheService = await this.helperService.get<CachingService>(CachingService);
    const cookieHelper = await this.helperService.getHelper('CookieHelper');
    await cacheService.invalidateCache(`sc_${cacheKey}`, null, 0);
    await cookieHelper.clearCookie(res, this.configService.get('ssoCookie'));
    return;
  };

}
