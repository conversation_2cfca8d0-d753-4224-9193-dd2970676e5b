import { Test, TestingModule } from '@nestjs/testing';
import { ProfileHelper } from './profile.helper';
import { ITaxonomyRepository } from '../repositories/taxonomy/taxonomy.repository';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { Category } from '../../db/mongo/schema/taxonomies/taxonomies.schema';
import { Utility } from './../../common/util/utility';

describe('ProfileHelper', () => {
  let profileHelper: ProfileHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfileHelper,
        {
          provide: 'ITaxonomyRepository',
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: HelperService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    profileHelper = module.get<ProfileHelper>(ProfileHelper);
  });

  it('should be defined', () => {
    expect(profileHelper).toBeDefined();
  });

  describe('prepareProfileData', () => {
    it('should prepare profile data', async () => {
      const taxonomyIds = {
        [Category.JOB_FUNCTION]: 'jobFunctionId',
        [Category.INDUSTRY]: 'industryId',
        [Category.QUALIFICATION]: 'qualificationId',
        [Category.OBJECTIVE_OF_TAKING_COURSE]: 'objectiveId',
      };

      jest.spyOn(profileHelper, 'prepareAndSaveTaxonomyData').mockResolvedValue(taxonomyIds);

      const inputData = {
        designation: 'Software Engineer',
        company: 'Example Corp',
        job_function: 'jobFunctionId',
        industry: 'industryId',
        qualification: 'qualificationId',
        objective: 'objectiveId',
        specialization: 'Computer Science',
        institute: 'University of Example',
      };

      const result = await profileHelper.prepareProfileData(inputData);

      expect(result).toEqual({
        work_experience: [
          {
            designation: 'Software Engineer',
            company: 'Example Corp',
            job_function: 'jobFunctionId',
            industry: 'industryId',
          },
        ],
        academics: [
          {
            qualification: 'qualificationId',
            objective: 'objectiveId',
            specialization: 'Computer Science',
            institute: 'University of Example',
          },
        ],
      });
    });

    // Add more test cases as needed
  });

  describe('prepareAndSaveTaxonomyData', () => {
    it('should prepare and save taxonomy data', async () => {
      const inputData = {
        industry: 'industryId',
        qualification: 'qualificationId',
        objective: 'objectiveId',
        job_function: 'jobFunctionId',
      };
      const taxonomyRepoMock = {
        findOne: jest.fn().mockResolvedValue(inputData),
      };

      jest.spyOn(profileHelper['helperService'], 'get').mockResolvedValueOnce(taxonomyRepoMock);

      const taxonomyData = {
        _id: 'taxonomyId',
      };

      jest.spyOn(taxonomyRepoMock, 'findOne').mockResolvedValueOnce(taxonomyData);

      const result = await profileHelper.prepareAndSaveTaxonomyData(inputData);

      expect(result.industry).toEqual(taxonomyData._id);
    });

    // Add more test cases as needed
  });
});
