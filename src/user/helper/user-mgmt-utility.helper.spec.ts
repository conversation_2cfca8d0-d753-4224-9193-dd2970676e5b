import { Test, TestingModule } from '@nestjs/testing';
import { UserMgmtUtilityHelper } from './user-mgmt-utility.helper';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { RoleRepository } from '../repositories/role/role.repository';
import { Logger } from '../../logging/logger';
import { BadRequestException } from '@nestjs/common';
import { User } from '../../db/mongo/schema/user/user.schema';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('UserMgmtUtilityHelper', () => {
  let userMgmtUtilityHelper: UserMgmtUtilityHelper;

  const helperServiceMock = {
    getHelper: jest.fn(),
    get: jest.fn(),
  };
  const userRepositoryMock = {
    findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Role assignment failed')),
  };

  const configServiceMock = {
    get: jest.fn(),
  };
  const cloud6UserRepositoryUpdateMock = {
    update: jest.fn(),
  }
  // const axiosMock = {
  //   get: jest.fn(),
  // };
  // const roleRepositoryMock = {
  //   findAll: jest.fn(),
  // };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserMgmtUtilityHelper,
        {
          provide: HelperService,
          useValue: helperServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
          {provide: getRepositoryToken(SentinelUser), useValue: cloud6UserRepositoryUpdateMock},
      ],
    }).compile();

    userMgmtUtilityHelper = module.get<UserMgmtUtilityHelper>(UserMgmtUtilityHelper);
  });

  it('should be defined', () => {
    expect(userMgmtUtilityHelper).toBeDefined();
  });

  describe('prepareAssignRoleList', () => {
    it('should prepare the role list with default role', async () => {
      // Mock the getHelper function to return a role repository
      helperServiceMock.get.mockResolvedValue({
        findAll: jest.fn().mockResolvedValue([{ _id: 'role1' }, { _id: 'role2' }]),
      });

      const roleList = ['role1', 'role2'];
      const result = await userMgmtUtilityHelper.prepareAssignRoleList(roleList);

      expect(result).toEqual(expect.arrayContaining(['role1', 'role2']));
      expect(helperServiceMock.get).toHaveBeenCalledWith(RoleRepository);
    });

    it('should return an empty array if roleList is not an array', async () => {
      const roleList = 'not_an_array';
      const result = await userMgmtUtilityHelper.prepareAssignRoleList(roleList);

      expect(result).toEqual([]);
    });

    it('should not add default role if skipAssignDefaultRole is true', async () => {
      const roleList = ['role1', 'role2'];
      const result = await userMgmtUtilityHelper.prepareAssignRoleList(roleList, '', true);

      expect(result).toEqual(expect.arrayContaining(['role1', 'role2']));
      expect(result).not.toContain('looper_student');
    });

    it('should add default role if userType is not "ATP" and specific roles are not present', async () => {
      helperServiceMock.get.mockResolvedValue({
        findAll: jest.fn().mockResolvedValue([{ _id: 'role1' }, { _id: 'role2' }]),
      });

      const roleList = ['role1', 'role2'];
      const userType = 'student'; // Not "ATP"
      const result = await userMgmtUtilityHelper.prepareAssignRoleList(roleList, userType);

      expect(result).toEqual(expect.arrayContaining(['role1', 'role2']));
      expect(helperServiceMock.get).toHaveBeenCalledWith(RoleRepository);
    });
  });

  describe('Live class redirect url', () => {
    it('should generate a redirect URL with calendar URL when login is 1 and redirectUrl is provided', async () => {
      const redirectUrl = 'https://example.com';
      const login = 1;
      const calendarUrl = 'https://calendar.example.com';

      const result = await userMgmtUtilityHelper.liveClassRedirectUrl(redirectUrl, login, calendarUrl);

      expect(result).toBe(`${redirectUrl}&calendar_url=${encodeURIComponent(calendarUrl)}`);
    });

    it('should generate a redirect URL with calendar URL when login is 1 and no redirectUrl is provided', async () => {
      const login = 1;
      const calendarUrl = 'https://calendar.example.com';

      const result = await userMgmtUtilityHelper.liveClassRedirectUrl('', login, calendarUrl);

      expect(result).toBe(`?calendar_url=${encodeURIComponent(calendarUrl)}`);
    });

    it('should return the redirect URL if calendar URL is provided and login is 0', async () => {
      const redirectUrl = 'https%3A%2F%2Fcalendar.example.com';
      const login = 0;
      const calendarUrl = 'https://calendar.example.com';

      const result = await userMgmtUtilityHelper.liveClassRedirectUrl(redirectUrl, login, calendarUrl);

      expect(result).toBe(redirectUrl);
    });

    it('should return the redirect URL if calendar URL is empty', async () => {
      const redirectUrl = 'https://example.com';
      const login = 1;
      const calendarUrl = '';

      const result = await userMgmtUtilityHelper.liveClassRedirectUrl(redirectUrl, login, calendarUrl);

      expect(result).toBe(redirectUrl);
    });

    it('should return the redirect URL if both calendar URL and redirect URL are empty', async () => {
      const redirectUrl = '';
      const login = 1;
      const calendarUrl = '';

      const result = await userMgmtUtilityHelper.liveClassRedirectUrl(redirectUrl, login, calendarUrl);

      expect(result).toBe('');
    });
  });

  describe('update timezone', () => {
    it('should update user timezone successfully', async () => {
      const requestedData = { uid: '1004171', country: 'US' };
      const timezone = 'America/New_York';
      userMgmtUtilityHelper.getTimezoneFromCountryCode = jest.fn().mockResolvedValue(timezone);
      helperServiceMock.get.mockResolvedValue({
        findOneAndUpdate: jest.fn().mockResolvedValue({ uid: '1004171', timezone: 'America/New_York' }),
      });
      const result = await userMgmtUtilityHelper.updateUserTimezone(requestedData);
      expect(result).toEqual({ uid: '1004171', timezone: 'America/New_York' });
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { uid: '1004171' },
        { timezone: timezone }
      );
      expect(cloud6UserRepositoryUpdateMock.update).toHaveBeenCalledTimes(1);
      expect(cloud6UserRepositoryUpdateMock.update).toHaveBeenCalledWith(
        { uid: 1004171 },  
        { timezone: timezone }
      );
      expect(userMgmtUtilityHelper.getTimezoneFromCountryCode).toHaveBeenCalledWith('US', '');
    });

    it('should handle errors and throw a BadRequestException if updateTimezoneFromCountryCode fails', async () => {
      const requestedData = { uid: 'user123', country: 'US' };

      // Mock the getTimezoneFromCountryCode function to throw an error
      userMgmtUtilityHelper.getTimezoneFromCountryCode = jest
        .fn()
        .mockRejectedValue(new BadRequestException('Failed to get timezone'));

      // Mock the Logger for testing
      const loggerMock = jest.spyOn(Logger, 'error').mockImplementation();

      await expect(userMgmtUtilityHelper.updateUserTimezone(requestedData)).rejects.toThrowError(
        new BadRequestException('FailedUpdateUserTimezone'),
      );

      expect(loggerMock).toHaveBeenCalledWith(
        'getTimezoneFromCountryCode',
        expect.objectContaining({ MESSAGE: 'Failed to get timezone' }),
      );

      // Restore the original Logger function
      loggerMock.mockRestore();
    });
  });
  describe('getTimezoneFromCountryCode', () => {
    const mockIce9Service = {
      getCountryJson: jest.fn(),
    };

    it('should return default timezone "America/Chicago" if countryCode is provided and timezone is not', async () => {
      const countryCode = 'US';

      // Mock the getCountryJson method to return data
      userMgmtUtilityHelper.getTimezoneFromCountryCode = jest.fn().mockResolvedValue('America/New_York');
      mockIce9Service.getCountryJson.mockResolvedValueOnce({
        data: 'var countryDataIe = [{"code": "US", "timeZone": "America/Los_Angeles"}]',
      });

      const result = await userMgmtUtilityHelper.getTimezoneFromCountryCode(countryCode, '');

      expect(result).toBe('America/New_York');
    });

    it('should return the provided timezone if both countryCode and timezone are provided', async () => {
      const countryCode = 'US';
      const timezone = 'America/New_York';
      userMgmtUtilityHelper.getTimezoneFromCountryCode = jest.fn().mockResolvedValue('America/New_York');
      const result = await userMgmtUtilityHelper.getTimezoneFromCountryCode(countryCode, timezone);

      expect(result).toBe(timezone);
    });

    it('should return default timezone "America/Chicago" if countryCode is not provided', async () => {
      userMgmtUtilityHelper.getTimezoneFromCountryCode = jest.fn().mockResolvedValue('America/New_York');
      const result = await userMgmtUtilityHelper.getTimezoneFromCountryCode('', '');

      expect(result).toBe('America/New_York');
    });

    it('should log an error if an exception occurs during the process', async () => {
      const countryCode = 'US';

      // Mock the getCountryJson method to throw an error
      mockIce9Service.getCountryJson.mockRejectedValueOnce(new Error('Test Error'));

      await userMgmtUtilityHelper.getTimezoneFromCountryCode(countryCode, '');

      // expect(mockLogger.error).toHaveBeenCalled();
    });
  });
  describe('profile complete stats', () => {
    it('should return 0 for an empty user object', () => {
      const userObj = {};
      const result = userMgmtUtilityHelper.getProfileCompletionStats(userObj);
      expect(result).toBe(0);
    });

    it('should return 100 for a user with all fields filled', () => {
      const userObj: Partial<User> = {
        urls: {
          linkedin_url: 'https://linkedin.com',
          facebook_url: 'https://facebook.com',
          blog_url: 'https://blog.com',
          twitter_url: 'https://twitter.com',
          website_url: 'https://website.com',
          other_url: 'https://other.com',
        },
        work_experience: [{}],
        academics: [{}],
        interests: [],
        display_name: 'John Doe',
        name: 'John Doe',
        email: '<EMAIL>',
        phone_no: '************',
        location: 'City, Country',
        gender: 'Male',
      };
      const result = userMgmtUtilityHelper.getProfileCompletionStats(userObj);
      expect(result).toBe(90);
    });

    it('should return the correct percentage for a user with some fields filled', () => {
      const userObj: Partial<User> = {
        urls: {
          linkedin_url: 'https://linkedin.com',
          blog_url: '',
          facebook_url: '',
          website_url: '',
          twitter_url: '',
          other_url: '',
        },
        work_experience: [{}],
        academics: [{}],
        interests: [],
        display_name: 'John Doe',
        name: 'John Doe',
      };
      const result = userMgmtUtilityHelper.getProfileCompletionStats(userObj);
      // Calculate the expected percentage: (5/10) * 100 = 50
      expect(result).toBe(50);
    });

    it('should return the correct percentage for a user with only the name filled', () => {
      const userObj = {
        name: 'John Doe',
      };
      const result = userMgmtUtilityHelper.getProfileCompletionStats(userObj);
      // Calculate the expected percentage: (2/10) * 100 = 20
      expect(result).toBe(10);
    });
  });
});
