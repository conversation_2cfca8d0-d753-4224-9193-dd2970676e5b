{"name": "sentinel", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest build && npm run build:scss", "start:dev": "npm run build:scss && nest start copy:assets --watch", "start:debug": "nest start --debug 0.0.0.0:9229 --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "docker:dev": "./run.sh && docker logs -f sentinel_accounts", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:file": "jest /var/www/html/apachedev/git/sentinel/src/auth/services/auth/auth.service.spec.ts", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "copy:assets": "cpx 'src/assets/**' 'dist/assets'", "build:scss": "sass --no-source-map --style compressed static/frontend/scss/main.scss:static/frontend/css/main.css && cp static/frontend/scss/bootstrap.css static/frontend/css/"}, "dependencies": {"@arendajaelu/nestjs-passport-apple": "^2.0.5", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/client-secrets-manager": "^3.370.0", "@aws-sdk/client-sts": "^3.749.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@golevelup/ts-jest": "^0.4.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.9", "@nestjs/swagger": "^11.0.3", "@nestjs/typeorm": "^11.0.0", "@node-saml/passport-saml": "^5.0.0", "axios": "^1.7.9", "bson": "^6.10.2", "cache-manager": "^6.4.0", "cache-manager-memcached-store": "^6.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "drupal-hash": "^1.0.4", "firebase-admin": "^13.2.0", "hbs": "^4.2.0", "helmet": "^8.0.0", "jwks-rsa": "^3.1.0", "md5": "^2.3.0", "memcache-plus": "^0.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "mongodb": "^5.9.2", "mongodb-client-encryption": "^2.7.1", "mysql": "^2.18.1", "newrelic": "^12.13.0", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-linkedin-oauth2": "^2.0.0", "passport-local": "^1.0.0", "path": "^0.12.7", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid-base64": "^1.0.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "cpx": "^1.5.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "sass": "^1.84.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "setupFilesAfterEnv": ["./../test/config/jest-setup.js"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coveragePathIgnorePatterns": [".module.ts", "main.ts", ".schema.ts", ".entity.ts"]}}